import { pgTable, serial, text, boolean, timestamp, integer, doublePrecision, jsonb } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  
  // Basic profile information
  firstName: text("first_name"),
  lastName: text("last_name"),
  phoneNumber: text("phone_number"),
  profileImageUrl: text("profile_image_url"),
  dateOfBirth: timestamp("date_of_birth"),
  gender: text("gender"),
  
  // Address information
  addressLine1: text("address_line1"),
  addressLine2: text("address_line2"),
  city: text("city"),
  state: text("state"),
  postalCode: text("postal_code"),
  country: text("country"),
  
  // Account status and membership
  isAdmin: boolean("is_admin").default(false),
  isVerified: boolean("is_verified").default(false),
  membershipType: text("membership_type").default("standard"),
  usedPromoCode: text("used_promo_code"),
  
  // Travel preferences
  travelPreferences: jsonb("travel_preferences"), // Preferred destinations, accommodation types, etc.
  paymentMethods: jsonb("payment_methods"), // Saved payment methods
  preferences: jsonb("preferences"), // General app preferences
  communicationPreferences: jsonb("communication_preferences"), // Email, SMS preferences
  
  // Timestamps
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`),
  lastLoginAt: timestamp("last_login_at"),
  
  // Account verification
  verificationToken: text("verification_token"),
  verificationTokenExpiry: timestamp("verification_token_expiry"),
  passwordResetToken: text("password_reset_token"),
  passwordResetTokenExpiry: timestamp("password_reset_token_expiry")
});

// Promo codes table
export const promoCodes = pgTable("promo_codes", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  description: text("description").notNull(),
  membershipType: text("membership_type").default("premium"),
  discountPercent: integer("discount_percent").notNull(),
  maxUsages: integer("max_usages").default(1),
  usageCount: integer("usage_count").default(0),
  expiresAt: timestamp("expires_at"),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`),
  createdBy: integer("created_by").references(() => users.id)
});

// Properties table
export const properties = pgTable("properties", {
  id: serial("id").primaryKey(),
  externalId: text("external_id").unique(),
  name: text("name").notNull(),
  description: text("description"),
  latitude: doublePrecision("latitude").notNull(),
  longitude: doublePrecision("longitude").notNull(),
  address: text("address").notNull(),
  city: text("city").notNull(),
  state: text("state"),
  country: text("country").notNull(),
  rating: doublePrecision("rating"),
  reviewCount: integer("review_count"),
  basePrice: doublePrecision("base_price").notNull(),
  currency: text("currency").default("USD"),
  propertyType: text("property_type"),
  amenities: jsonb("amenities"), // JSON array of amenities
  images: jsonb("images"), // JSON array of image URLs
  source: text("source").default("api"), // Where this property data came from
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`),
  discount: integer("discount").default(0), // Percentage discount
  featured: boolean("featured").default(false)
});

// Reservations table
export const reservations = pgTable("reservations", {
  id: serial("id").primaryKey(),
  propertyId: integer("property_id").references(() => properties.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  checkIn: timestamp("check_in").notNull(),
  checkOut: timestamp("check_out").notNull(),
  guests: integer("guests").notNull(),
  roomCode: text("room_code").notNull(),
  ratePlanCode: text("rate_plan_code").notNull(),
  status: text("status").default("pending"), // pending, confirmed, cancelled
  totalAmount: doublePrecision("total_amount").notNull(),
  currency: text("currency").default("USD"),
  notes: text("notes"),
  specialRequests: text("special_requests"),
  promoCodeUsed: text("promo_code_used"),
  confirmationCode: text("confirmation_code"),
  paymentIntentId: text("payment_intent_id"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`)
});

// Table relationships

export const usersRelations = relations(users, ({ many }) => ({
  reservations: many(reservations),
  promoCodes: many(promoCodes, { relationName: "createdCodes" })
}));

export const propertiesRelations = relations(properties, ({ many }) => ({
  reservations: many(reservations)
}));

export const reservationsRelations = relations(reservations, ({ one }) => ({
  property: one(properties, {
    fields: [reservations.propertyId],
    references: [properties.id]
  }),
  user: one(users, {
    fields: [reservations.userId],
    references: [users.id]
  })
}));

export const promoCodesRelations = relations(promoCodes, ({ many, one }) => ({
  users: many(users, { relationName: "usedByUsers" }),
  creator: one(users, {
    fields: [promoCodes.createdBy],
    references: [users.id]
  })
}));

// Zod schemas for validation
export const insertUserSchema = createInsertSchema(users);
export const selectUserSchema = createSelectSchema(users);
export const insertPropertySchema = createInsertSchema(properties);
export const selectPropertySchema = createSelectSchema(properties);
export const insertReservationSchema = createInsertSchema(reservations);
export const selectReservationSchema = createSelectSchema(reservations);
export const insertPromoCodeSchema = createInsertSchema(promoCodes);
export const selectPromoCodeSchema = createSelectSchema(promoCodes);

// Test results table
export const testResults = pgTable("test_results", {
  id: serial("id").primaryKey(),
  sessionId: text("session_id").notNull(),
  userId: integer("user_id").references(() => users.id),
  testType: text("test_type").notNull(), // "location", "chat", "property", "integrated"
  query: text("query").notNull(),
  success: boolean("success").notNull().default(false),
  duration: integer("duration").notNull(),
  timestamp: timestamp("timestamp").default(sql`CURRENT_TIMESTAMP`),
  metrics: jsonb("metrics").notNull().default({}), // Store test metrics (latency, tokens, etc)
  context: jsonb("context").default({}), // Store test context (environment, settings)
  results: jsonb("results").default({}), // Store detailed test results
  error: text("error") // Store error message if test failed
});

// Test result relations
export const testResultsRelations = relations(testResults, ({ one }) => ({
  user: one(users, {
    fields: [testResults.userId],
    references: [users.id]
  })
}));

// Add testResults to user relations
export const usersTestResultsRelations = relations(users, ({ many }) => ({
  testResults: many(testResults)
}));

// Zod schemas for test results
export const insertTestResultSchema = createInsertSchema(testResults);
export const selectTestResultSchema = createSelectSchema(testResults);

// Analytics tables

// Search logs table to track user searches
export const searchLogs = pgTable("search_logs", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  sessionId: text("session_id").notNull(),
  searchQuery: text("search_query"),
  locationType: text("location_type"), // city, neighborhood, poi, etc.
  locationName: text("location_name"),
  latitude: doublePrecision("latitude"),
  longitude: doublePrecision("longitude"),
  checkIn: timestamp("check_in"),
  checkOut: timestamp("check_out"),
  guests: integer("guests"),
  rooms: integer("rooms"),
  filters: jsonb("filters").default({}), // Store filters like price range, amenities, etc.
  resultsCount: integer("results_count").default(0),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
  source: text("source").default("web"), // web, app, ai, etc.
  userAgent: text("user_agent"),
  ipAddress: text("ip_address"),
  successful: boolean("successful").default(true)
});

// Page views table to track user navigation
export const pageViews = pgTable("page_views", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  sessionId: text("session_id").notNull(),
  pagePath: text("page_path").notNull(),
  pageTitle: text("page_title"),
  referrer: text("referrer"),
  duration: integer("duration"), // Time spent on page in seconds
  bounced: boolean("bounced").default(false),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
  userAgent: text("user_agent"),
  ipAddress: text("ip_address"),
  deviceType: text("device_type") // desktop, mobile, tablet
});

// Property views to track property detail page views
export const propertyViews = pgTable("property_views", {
  id: serial("id").primaryKey(),
  propertyId: integer("property_id").references(() => properties.id).notNull(),
  userId: integer("user_id").references(() => users.id),
  sessionId: text("session_id").notNull(),
  duration: integer("duration"), // Time spent viewing property in seconds
  viewedPhotos: boolean("viewed_photos").default(false),
  viewedRates: boolean("viewed_rates").default(false),
  viewedReviews: boolean("viewed_reviews").default(false),
  viewedAmenities: boolean("viewed_amenities").default(false),
  savedProperty: boolean("saved_property").default(false),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`),
  source: text("source").default("search"), // search, recommendation, direct
  exitAction: text("exit_action") // booking, back_to_search, exit
});

// Table relationships for analytics tables
export const searchLogsRelations = relations(searchLogs, ({ one }) => ({
  user: one(users, {
    fields: [searchLogs.userId],
    references: [users.id]
  })
}));

export const pageViewsRelations = relations(pageViews, ({ one }) => ({
  user: one(users, {
    fields: [pageViews.userId],
    references: [users.id]
  })
}));

export const propertyViewsRelations = relations(propertyViews, ({ one }) => ({
  property: one(properties, {
    fields: [propertyViews.propertyId],
    references: [properties.id]
  }),
  user: one(users, {
    fields: [propertyViews.userId],
    references: [users.id]
  })
}));

// Update user relations to include analytics
export const usersAnalyticsRelations = relations(users, ({ many }) => ({
  searchLogs: many(searchLogs),
  pageViews: many(pageViews),
  propertyViews: many(propertyViews)
}));

// Zod schemas for analytics tables
export const insertSearchLogSchema = createInsertSchema(searchLogs);
export const selectSearchLogSchema = createSelectSchema(searchLogs);
export const insertPageViewSchema = createInsertSchema(pageViews);
export const selectPageViewSchema = createSelectSchema(pageViews);
export const insertPropertyViewSchema = createInsertSchema(propertyViews);
export const selectPropertyViewSchema = createSelectSchema(propertyViews);

// Export types based on the schema
export type User = typeof users.$inferSelect;
export type Property = typeof properties.$inferSelect;
export type Reservation = typeof reservations.$inferSelect;
export type PromoCode = typeof promoCodes.$inferSelect;
export type TestResult = typeof testResults.$inferSelect;
export type SearchLog = typeof searchLogs.$inferSelect;
export type PageView = typeof pageViews.$inferSelect;
export type PropertyView = typeof propertyViews.$inferSelect;