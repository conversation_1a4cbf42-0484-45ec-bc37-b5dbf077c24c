import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Use environment variable for database connection
const connectionString = process.env.DATABASE_URL || '';

// Initialize Postgres client with connection pooling
const client = postgres(connectionString, {
  max: 10, // Maximum number of connections
  idle_timeout: 20, // Max idle time for connections in seconds
  connect_timeout: 10, // Connection timeout in seconds
  prepare: false, // Set true in production for prepared statements
});

// Initialize drizzle with our schema
export const db = drizzle(client, { schema });

// Export a function to run database migrations programmatically if needed
export async function runMigrations() {
  // This would use drizzle-kit migrate
  console.log('Migrations would run here in production environments');
}

// Export a function to check database connectivity
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    // Simple query to test connection
    await client`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection error:', error);
    return false;
  }
}