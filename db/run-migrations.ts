/**
 * Database Migration Runner
 * 
 * This script runs all migrations in the /migrations directory
 * to ensure the database schema is up to date and initialized
 * with necessary data for production deployment.
 */

import { readdir, readFile } from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import logger from '../server/utils/logger';
import pg from 'pg';

// ES modules equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create a database pool connection
const pool = new pg.Pool({
  connectionString: process.env.DATABASE_URL
});

// Main migration runner function
export async function runMigrations() {
  try {
    logger.info('Starting database migrations...');
    
    // Create migrations table if it doesn't exist
    await createMigrationsTable();
    
    // Get list of already applied migrations
    const appliedMigrations = await getAppliedMigrations();
    logger.info(`Found ${appliedMigrations.length} previously applied migrations`);
    
    // Get all migration files
    const migrationFiles = await getMigrationFiles();
    logger.info(`Found ${migrationFiles.length} migration files`);
    
    // Filter out already applied migrations
    const pendingMigrations = migrationFiles.filter(
      file => !appliedMigrations.includes(file)
    );
    
    if (pendingMigrations.length === 0) {
      logger.info('No pending migrations to apply');
      return;
    }
    
    logger.info(`Applying ${pendingMigrations.length} pending migrations`);
    
    // Apply migrations in order
    for (const migrationFile of pendingMigrations) {
      await applyMigration(migrationFile);
    }
    
    logger.info('All migrations applied successfully');
  } catch (error) {
    logger.error('Migration failed', { error: error instanceof Error ? error.message : String(error) });
    throw error;
  } finally {
    // Connection will be handled by the pool
  }
}

// Create migrations tracking table
async function createMigrationsTable() {
  const client = await pool.connect();
  try {
    await client.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
  } finally {
    client.release();
  }
}

// Get list of already applied migrations
async function getAppliedMigrations(): Promise<string[]> {
  const client = await pool.connect();
  try {
    const result = await client.query('SELECT name FROM migrations ORDER BY id');
    return result.rows.map((row: { name: string }) => row.name);
  } finally {
    client.release();
  }
}

// Get all migration files in the migrations directory
async function getMigrationFiles(): Promise<string[]> {
  const migrationsDir = path.resolve(__dirname, '../migrations');
  const files = await readdir(migrationsDir);
  
  // Filter for SQL files and sort them
  // Get all SQL files
  const sqlFiles = files.filter(file => file.endsWith('.sql'));
  
  // Ensure initial schema migration runs first
  const initialSchema = sqlFiles.find(file => file === '001_initial_schema.sql');
  const otherFiles = sqlFiles.filter(file => file !== '001_initial_schema.sql');
  
  // Sort the remaining files
  const sortedOtherFiles = otherFiles.sort((a, b) => {
    // Extract numeric prefix if it exists to ensure proper order
    const numA = parseInt(a.match(/^(\d+)/)?.[1] || '0');
    const numB = parseInt(b.match(/^(\d+)/)?.[1] || '0');
    if (numA !== numB) return numA - numB;
    return a.localeCompare(b);
  });
  
  // Initial schema first, then others
  return initialSchema ? [initialSchema, ...sortedOtherFiles] : sortedOtherFiles;
}

// Apply a single migration
async function applyMigration(filename: string) {
  const client = await pool.connect();
  try {
    // Start transaction
    await client.query('BEGIN');
    
    // Read migration file
    const migrationsDir = path.resolve(__dirname, '../migrations');
    const filePath = path.join(migrationsDir, filename);
    const sql = await readFile(filePath, 'utf8');
    
    logger.info(`Applying migration: ${filename}`);
    
    // Run the migration SQL
    await client.query(sql);
    
    // Record the migration
    await client.query('INSERT INTO migrations (name) VALUES ($1)', [filename]);
    
    // Commit transaction
    await client.query('COMMIT');
    
    logger.info(`Migration applied: ${filename}`);
  } catch (error) {
    // Roll back transaction on error
    await client.query('ROLLBACK');
    logger.error(`Migration failed: ${filename}`, { 
      error: error instanceof Error ? error.message : String(error) 
    });
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
// ES Modules don't have require.main === module, so we don't need this check
// This will only run when directly imported by server/index.ts