# AI Chat Testing Guide - Step by Step

## 🚀 Server Status
✅ Server is running on **port 5003**  
✅ Lambda AI provider initialized successfully  
✅ Database migrations completed  
✅ Enhanced AI chat routes registered  

## 🧪 Testing Steps

### Step 1: Test Server API Directly
```bash
# Test config endpoint
curl -s http://localhost:5003/api/config | head -20

# Test chat endpoint
curl -s -X POST http://localhost:5003/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message":"Hello","sessionId":"test-123"}' | head -10
```

### Step 2: Browser Console Testing
Open your browser console and run these commands:

#### Quick Health Check
```javascript
await testAI.quick()
```

#### Check Current Chat State
```javascript
checkChatState()
```

#### Clear All Chat Data
```javascript
clearChatData()
```

#### Simulate Plan with AI Button
```javascript
simulatePlanWithAI()
// Then manually click "Plan with AI" to see if it processes automatically
```

### Step 3: Manual UI Testing

#### Test the Complete Flow:
1. **Open the website** (http://localhost:5003)
2. **Open browser console** (F12)
3. **Run:** `clearChatData()` to start fresh
4. **Click "Plan with AI" button** on the homepage
5. **Watch console logs** for debugging output
6. **Verify chat modal opens** 
7. **Check if AI responds automatically**

#### Expected Console Output:
When clicking "Plan with AI", you should see:
```
🎯 Plan with AI button clicked!
🧹 Cleared existing localStorage data
💬 Storing initial message: {role: "user", content: "...", id: "..."}
🚀 Set trigger flag to true
✨ Chat modal should now be visible
```

When AiChat component loads:
```
🔍 AiChat initialization check running...
🚀 Trigger flag present: true
📋 Last message: {role: "user", content: "...", id: "..."}
🎯 Processing initial message from Plan with AI: ...
🧹 Cleared trigger flag
🤖 Sending message to AI...
```

### Step 4: Debugging Common Issues

#### Issue: Chat Modal Doesn't Open
**Check:**
```javascript
// In console after clicking button
console.log('showAiChat state should be true');
// Look for the modal backdrop div in DOM
document.querySelector('.fixed.inset-0.bg-black\\/30')
```

#### Issue: Chat Opens But No AI Response
**Check:**
```javascript
// Check if message was stored
checkChatState()

// Check if trigger flag was set and cleared
localStorage.getItem('ai_chat_trigger') // should be null after processing
```

#### Issue: Network Errors
**Check:**
- Open Network tab in DevTools
- Look for `/api/chat` POST request
- Check response status (should be 200)
- Look for streaming response chunks

#### Issue: Multiple Messages Sent
**Check console for:**
```
❌ Initial message already processed, skipping
```

### Step 5: Advanced Testing

#### Test Real User Scenario
```javascript
await testAI.user()
```

#### Test Complete E2E Flow
```javascript
const result = await testAI.full()
console.log(result.summary)
// Should show: "X/7 tests passed (Y/5 critical)"
```

#### Manual API Test
```javascript
fetch('/api/chat', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    message: 'I need a hotel in Paris for 3 nights',
    sessionId: 'manual-test-' + Date.now()
  })
}).then(response => {
  console.log('API Response Status:', response.status);
  return response.body.getReader();
}).then(reader => {
  console.log('Streaming reader available:', !!reader);
  return reader.read();
}).then(({value, done}) => {
  console.log('First chunk received:', !!value, 'Done:', done);
  if (value) {
    console.log('Chunk content:', new TextDecoder().decode(value));
  }
})
```

## 🔧 Troubleshooting

### If Nothing Happens When Clicking "Plan with AI":

1. **Check button click handler:**
   ```javascript
   // In console, look for the button and test click manually
   const button = document.querySelector('button[onclick*="handleOpenChat"]');
   if (button) button.click();
   ```

2. **Check localStorage after clicking:**
   ```javascript
   checkChatState()
   // Should show chatHistory with user message and trigger flag
   ```

3. **Check React state:**
   - Look for `showAiChat` state in React DevTools
   - Should be `true` after clicking

### If Chat Opens But No AI Response:

1. **Check console logs** for the AiChat initialization sequence
2. **Check Network tab** for `/api/chat` requests
3. **Check server logs:**
   ```bash
   tail -f dev-output.log
   ```

### If Server Issues:

1. **Restart development server:**
   ```bash
   npm run dev
   ```

2. **Check port conflicts:**
   ```bash
   lsof -i :5000 :5001 :5002 :5003
   ```

3. **Test server directly:**
   ```bash
   curl http://localhost:5003/api/config
   ```

## ✅ Success Criteria

The AI chat is working correctly when:

- ✅ Button click shows console logs
- ✅ Chat modal appears (visible overlay + chat interface)
- ✅ Console shows initialization sequence
- ✅ Network tab shows successful `/api/chat` request
- ✅ AI response appears in chat (streaming text)
- ✅ No duplicate messages
- ✅ Trigger flag is cleared after processing

## 🚨 Quick Fix Commands

If things get stuck, try these in order:

```javascript
// 1. Clear everything and restart
clearChatData()
location.reload()

// 2. Simulate fresh start
simulatePlanWithAI()
// Then click "Plan with AI"

// 3. Test API directly
await testAI.quick()

// 4. Run comprehensive test
await testAI.full()
```

## 📞 When to Ask for Help

If after following this guide:
- Console shows no errors but chat doesn't work
- API tests pass but UI flow fails
- Server logs show errors you can't resolve
- Tests report critical failures

Then provide the console output and describe exactly which step failed. 