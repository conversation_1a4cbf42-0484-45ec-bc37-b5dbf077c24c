# RoomLama Testing Strategy

## Overview
This document outlines the comprehensive testing strategy for the RoomLama platform, focusing on ensuring stability during feature enhancements and modifications.

## Test Categories

### 1. Unit Tests
- **Service Layer Tests**: Verify individual service methods work correctly in isolation
- **Utility Function Tests**: Ensure utility functions handle all edge cases
- **Component Tests**: Test React components in isolation with controlled inputs

### 2. Integration Tests
- **API Endpoint Tests**: Verify all API endpoints function correctly
- **Service Integration Tests**: Test how services interact with each other
- **Database Integration Tests**: Test database interactions

### 3. Functional Tests
- **User Journey Tests**: Test complete user journeys and flows
- **AI Interaction Tests**: Verify AI responses for different query types
- **Location Detection Tests**: Ensure accurate location detection in different contexts

## Critical Functionality to Test

### Location Detection
- Direct city queries
- Complex location references (landmarks, neighborhoods)
- Misspelled locations
- Ambiguous location references
- Multiple locations in one query

### Property Recommendations
- Relevant properties matching location query
- Properties matching user preferences
- Special property types (beachfront, etc.)
- Price range filtering
- Amenity-based filtering

### User Interaction Flow
- Search functionality
- Property viewing
- Booking process
- User registration and login
- User profile management

### Error Handling
- Invalid API requests
- Database connection issues
- External service failures
- Invalid user inputs

## Test Implementation Plan
1. Fill gaps in existing test coverage for critical functionality
2. Implement mocking strategies for external dependencies
3. Set up CI/CD integration for automated testing
4. Create regression test suite for core functionality

## Test Execution
- **Pre-commit**: Run unit tests
- **Pre-merge**: Run integration tests
- **Release**: Run full test suite including functional tests

## Running Tests

### Using the Test Runner

The project includes a comprehensive test runner script that manages test execution and reporting. Use the following command to run all tests:

```bash
./run-tests.sh
```

The script will:
1. Check if the server is running (some tests require an active server)
2. Run unit tests for server-side code
3. Run functional tests for API endpoints and user flows
4. Run React component tests
5. Generate a detailed report with pass/fail status

### Test Types and Configurations

The project uses specialized Jest configurations for different test types:

1. **Unit Tests** (`jest.config.unit.ts`): 
   - Focus on isolated components with minimal dependencies
   - Run with: `npx jest --config=jest.config.unit.ts`

2. **Functional Tests** (`jest.config.functional.ts`):
   - Test complete user journeys and API functionality
   - Run with: `npx jest --config=jest.config.functional.ts`

3. **React Component Tests** (`jest.config.react.ts`):
   - Test React components with simulated user interactions
   - Run with: `npx jest --config=jest.config.react.ts`

### Running Specific Tests

To run a specific test file or test suite, use the appropriate configuration with the test file path:

```bash
# Run a specific unit test
npx jest --config=jest.config.unit.ts server/__tests__/unit/stream-parser.test.ts

# Run a specific functional test
npx jest --config=jest.config.functional.ts server/__tests__/functional/search-functionality.test.ts

# Run a specific React component test
npx jest --config=jest.config.react.ts client/src/__tests__/AiChat.test.tsx
```

### Test Logs and Reports

When running tests via the test runner script, detailed logs are saved to the `test-logs` directory. Each test run creates a timestamped subfolder containing:

- `test_results.log`: Overall test summary
- Individual log files for each test suite

## Writing New Tests

### Unit Tests

Place new unit tests in the `server/__tests__/unit/` directory following this pattern:

```typescript
import { describe, test, expect } from '@jest/globals';

describe('Component or function name', () => {
  test('should perform expected behavior', () => {
    // Arrange
    // ...

    // Act
    // ...

    // Assert
    expect(result).toBe(expectedValue);
  });
});
```

### Functional Tests

Place new functional tests in the `server/__tests__/functional/` directory. These should focus on complete user flows:

```typescript
describe('User flow name', () => {
  test('should complete the flow successfully', async () => {
    // Setup
    // ...

    // Execute flow steps
    // ...

    // Validate outcomes
    expect(result).toMatchExpectedCriteria();
  });
});
```

### React Component Tests

Place new React component tests in the `client/src/__tests__/` directory. Use React Testing Library for component interaction:

```typescript
import { render, screen, fireEvent } from '@testing-library/react';

describe('ComponentName', () => {
  test('should render and respond to user interaction', () => {
    // Render component
    render(<ComponentName />);

    // Interact with component
    fireEvent.click(screen.getByText('Button text'));

    // Check results
    expect(screen.getByText('Expected result')).toBeInTheDocument();
  });
});
```

## Test Utilities

A set of helper utilities are available in `server/__tests__/test-utils.ts` to simplify common testing tasks and provide type safety:

### Type Safety Helpers

Use these helpers to fix common TypeScript issues in tests:

```typescript
import { assertType, extractJsonData, getStreamReader } from '../test-utils';

// Safely type unknown API data
const typedData = assertType<ExpectedType>(untypedData);

// Extract and type JSON data from responses
const data = await extractJsonData<MyDataType>(response);

// Get a properly typed stream reader (fixes node-fetch compatibility issues)
const reader = getStreamReader(response.body);
```

### Test Mocks

The test utils provide pre-built mocks for common testing scenarios:

```typescript
import { createTypedFetchMock, createErrorFetchMock, MockEventSource } from '../test-utils';

// Create a pre-typed fetch mock
const mockFetch = createTypedFetchMock<UserData>({ id: 1, name: 'Test User' });

// Create a mock for error responses
const errorMock = createErrorFetchMock(404, 'Not found');

// Create a mock for EventSource testing
const mockEventSource = new MockEventSource('url', [
  { data: JSON.stringify({ type: 'text', data: 'Hello' }), delay: 100 },
  { data: '[DONE]', delay: 200 }
]);
```

## Test Mocking Strategies

### API Requests

For tests that involve API requests, use Jest's mocking capabilities:

```typescript
// Mock fetch
jest.mock('node-fetch', () => jest.fn());
const mockedFetch = fetch as jest.MockedFunction<typeof fetch>;

// Configure the mock response
mockedFetch.mockResolvedValue({
  ok: true,
  json: async () => ({ success: true, data: mockData })
} as unknown as Response);
```

### External Services

For external services like OpenAI or Google Maps, create dedicated mock implementations:

```typescript
// Mock OpenAI
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [{
            message: {
              content: JSON.stringify({ /* mock response */ })
            }
          }]
        })
      }
    }
  }));
});
```

## Continuous Integration

Tests should be integrated into the CI/CD pipeline to ensure code quality:

1. Run unit tests on every commit
2. Run functional tests on PR creation/update
3. Run full test suite before deployment

## Test Coverage Goals

Aim for the following test coverage metrics:

- Unit tests: 80%+ coverage of service and utility functions
- Functional tests: Cover all critical user journeys
- Component tests: Test all interactive UI components