-- Add new user profile fields
ALTER TABLE "users" 
  ADD COLUMN IF NOT EXISTS "first_name" TEXT,
  ADD COLUMN IF NOT EXISTS "last_name" TEXT,
  ADD COLUMN IF NOT EXISTS "date_of_birth" TIMESTAMP,
  ADD COLUMN IF NOT EXISTS "gender" TEXT,
  ADD COLUMN IF NOT EXISTS "address_line1" TEXT,
  ADD COLUMN IF NOT EXISTS "address_line2" TEXT,
  ADD COLUMN IF NOT EXISTS "city" TEXT,
  ADD COLUMN IF NOT EXISTS "state" TEXT,
  ADD COLUMN IF NOT EXISTS "postal_code" TEXT,
  ADD COLUMN IF NOT EXISTS "country" TEXT,
  ADD COLUMN IF NOT EXISTS "travel_preferences" JSONB,
  ADD COLUMN IF NOT EXISTS "payment_methods" JSONB,
  ADD COLUMN IF NOT EXISTS "communication_preferences" JSONB,
  ADD COLUMN IF NOT EXISTS "verification_token" TEXT,
  ADD COLUMN IF NOT EXISTS "verification_token_expiry" TIMESTAMP,
  ADD COLUMN IF NOT EXISTS "password_reset_token" TEXT,
  ADD COLUMN IF NOT EXISTS "password_reset_token_expiry" TIMESTAMP;

-- Create analytics tables

-- Search logs table
CREATE TABLE IF NOT EXISTS "search_logs" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER REFERENCES "users"("id"),
  "session_id" TEXT NOT NULL,
  "search_query" TEXT,
  "location_type" TEXT,
  "location_name" TEXT,
  "latitude" DOUBLE PRECISION,
  "longitude" DOUBLE PRECISION,
  "check_in" TIMESTAMP,
  "check_out" TIMESTAMP,
  "guests" INTEGER,
  "rooms" INTEGER,
  "filters" JSONB DEFAULT '{}',
  "results_count" INTEGER DEFAULT 0,
  "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "source" TEXT DEFAULT 'web',
  "user_agent" TEXT,
  "ip_address" TEXT,
  "successful" BOOLEAN DEFAULT TRUE
);

-- Page views table
CREATE TABLE IF NOT EXISTS "page_views" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER REFERENCES "users"("id"),
  "session_id" TEXT NOT NULL,
  "page_path" TEXT NOT NULL,
  "page_title" TEXT,
  "referrer" TEXT,
  "duration" INTEGER,
  "bounced" BOOLEAN DEFAULT FALSE,
  "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "user_agent" TEXT,
  "ip_address" TEXT,
  "device_type" TEXT
);

-- Property views table
CREATE TABLE IF NOT EXISTS "property_views" (
  "id" SERIAL PRIMARY KEY,
  "property_id" INTEGER REFERENCES "properties"("id") NOT NULL,
  "user_id" INTEGER REFERENCES "users"("id"),
  "session_id" TEXT NOT NULL,
  "duration" INTEGER,
  "viewed_photos" BOOLEAN DEFAULT FALSE,
  "viewed_rates" BOOLEAN DEFAULT FALSE,
  "viewed_reviews" BOOLEAN DEFAULT FALSE,
  "viewed_amenities" BOOLEAN DEFAULT FALSE,
  "saved_property" BOOLEAN DEFAULT FALSE,
  "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "source" TEXT DEFAULT 'search',
  "exit_action" TEXT
);

-- Add indexes to improve analytics query performance
CREATE INDEX IF NOT EXISTS "search_logs_user_id_idx" ON "search_logs"("user_id");
CREATE INDEX IF NOT EXISTS "search_logs_created_at_idx" ON "search_logs"("created_at");
CREATE INDEX IF NOT EXISTS "search_logs_location_name_idx" ON "search_logs"("location_name");

CREATE INDEX IF NOT EXISTS "page_views_user_id_idx" ON "page_views"("user_id");
CREATE INDEX IF NOT EXISTS "page_views_created_at_idx" ON "page_views"("created_at");
CREATE INDEX IF NOT EXISTS "page_views_page_path_idx" ON "page_views"("page_path");

CREATE INDEX IF NOT EXISTS "property_views_property_id_idx" ON "property_views"("property_id");
CREATE INDEX IF NOT EXISTS "property_views_user_id_idx" ON "property_views"("user_id");
CREATE INDEX IF NOT EXISTS "property_views_created_at_idx" ON "property_views"("created_at");