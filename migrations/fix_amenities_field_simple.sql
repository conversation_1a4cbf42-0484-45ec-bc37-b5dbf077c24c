-- Simple fix: Wipe all corrupted amenities/images data and start fresh
-- This prevents the malformed array literal errors

-- Step 1: Force all amenities to empty JSONB arrays
UPDATE properties SET amenities = '[]'::jsonb;

-- Step 2: Force all images to empty JSONB arrays  
UPDATE properties SET images = '[]'::jsonb;

-- Step 3: Ensure columns are properly typed as JSONB (they should already be based on schema)
-- But let's be explicit about the type to avoid any TEXT/JSONB confusion
ALTER TABLE properties ALTER COLUMN amenities TYPE jsonb USING '[]'::jsonb;
ALTER TABLE properties ALTER COLUMN images TYPE jsonb USING '[]'::jsonb; 