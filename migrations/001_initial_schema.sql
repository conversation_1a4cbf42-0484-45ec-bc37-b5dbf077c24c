-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  password_hash TEXT NOT NULL,
  name TEXT NOT NULL,
  is_admin BOOLEAN DEFAULT false,
  is_verified B<PERSON><PERSON><PERSON>N DEFAULT false,
  membership_type TEXT DEFAULT 'standard',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login_at TIMESTAMP,
  used_promo_code TEXT,
  preferences TEXT,
  profile_image_url TEXT,
  phone_number TEXT
);

-- Create promo_codes table
CREATE TABLE IF NOT EXISTS promo_codes (
  id SERIAL PRIMARY KEY,
  code TEXT NOT NULL UNIQUE,
  description TEXT NOT NULL,
  membership_type TEXT DEFAULT 'premium',
  discount_percent INTEGER NOT NULL,
  max_usages INTEGER DEFAULT 1,
  usage_count INTEGER DEFAULT 0,
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER REFERENCES users(id)
);

-- Create properties table
CREATE TABLE IF NOT EXISTS properties (
  id SERIAL PRIMARY KEY,
  external_id TEXT UNIQUE,
  name TEXT NOT NULL,
  description TEXT,
  latitude DOUBLE PRECISION NOT NULL,
  longitude DOUBLE PRECISION NOT NULL,
  address TEXT NOT NULL,
  city TEXT NOT NULL,
  state TEXT,
  country TEXT NOT NULL,
  rating DOUBLE PRECISION,
  review_count INTEGER,
  base_price DOUBLE PRECISION NOT NULL,
  currency TEXT DEFAULT 'USD',
  property_type TEXT,
  amenities TEXT,
  images TEXT,
  source TEXT DEFAULT 'api',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  discount INTEGER DEFAULT 0,
  featured BOOLEAN DEFAULT false
);

-- Create reservations table
CREATE TABLE IF NOT EXISTS reservations (
  id SERIAL PRIMARY KEY,
  property_id INTEGER REFERENCES properties(id) NOT NULL,
  user_id INTEGER REFERENCES users(id) NOT NULL,
  check_in TIMESTAMP NOT NULL,
  check_out TIMESTAMP NOT NULL,
  guests INTEGER NOT NULL,
  room_code TEXT NOT NULL,
  rate_plan_code TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  total_amount DOUBLE PRECISION NOT NULL,
  currency TEXT DEFAULT 'USD',
  notes TEXT,
  special_requests TEXT,
  promo_code_used TEXT,
  confirmation_code TEXT,
  payment_intent_id TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add extension for distance calculations
CREATE EXTENSION IF NOT EXISTS earthdistance CASCADE;
CREATE EXTENSION IF NOT EXISTS cube;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_properties_location ON properties USING gist (ll_to_earth(latitude, longitude));
CREATE INDEX IF NOT EXISTS idx_properties_price ON properties(base_price);
CREATE INDEX IF NOT EXISTS idx_properties_rating ON properties(rating);
CREATE INDEX IF NOT EXISTS idx_reservations_user ON reservations(user_id);
CREATE INDEX IF NOT EXISTS idx_reservations_property ON reservations(property_id);
CREATE INDEX IF NOT EXISTS idx_reservations_dates ON reservations(check_in, check_out);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_promo_codes_code ON promo_codes(code);