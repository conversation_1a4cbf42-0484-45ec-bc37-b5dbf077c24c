-- Ensure users table has the required columns for authentication
DO $$
BEGIN
    -- Check if users table exists
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'users') THEN
        CREATE TABLE public.users (
            id SERIAL PRIMARY KEY,
            email TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            is_admin BOOLEAN DEFAULT FALSE,
            is_verified BOOLEAN DEFAULT FALSE,
            membership_type TEXT DEFAULT 'standard',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login_at TIMESTAMP,
            used_promo_code TEXT,
            preferences JSONB,
            profile_image_url TEXT,
            phone_number TEXT
        );
    ELSE
        -- Add missing columns if they don't exist
        
        -- Add is_verified column if it doesn't exist
        IF NOT EXISTS (SELECT FROM pg_attribute 
                       WHERE attrelid = 'public.users'::regclass 
                       AND attname = 'is_verified' 
                       AND NOT attisdropped) THEN
            ALTER TABLE public.users ADD COLUMN is_verified BOOLEAN DEFAULT FALSE;
        END IF;

        -- Add membership_type column if it doesn't exist
        IF NOT EXISTS (SELECT FROM pg_attribute 
                       WHERE attrelid = 'public.users'::regclass 
                       AND attname = 'membership_type' 
                       AND NOT attisdropped) THEN
            ALTER TABLE public.users ADD COLUMN membership_type TEXT DEFAULT 'standard';
        END IF;
        
        -- Add updated_at column if it doesn't exist
        IF NOT EXISTS (SELECT FROM pg_attribute 
                       WHERE attrelid = 'public.users'::regclass 
                       AND attname = 'updated_at' 
                       AND NOT attisdropped) THEN
            ALTER TABLE public.users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        END IF;
        
        -- Add last_login_at column if it doesn't exist
        IF NOT EXISTS (SELECT FROM pg_attribute 
                       WHERE attrelid = 'public.users'::regclass 
                       AND attname = 'last_login_at' 
                       AND NOT attisdropped) THEN
            ALTER TABLE public.users ADD COLUMN last_login_at TIMESTAMP;
        END IF;
        
        -- Add used_promo_code column if it doesn't exist
        IF NOT EXISTS (SELECT FROM pg_attribute 
                       WHERE attrelid = 'public.users'::regclass 
                       AND attname = 'used_promo_code' 
                       AND NOT attisdropped) THEN
            ALTER TABLE public.users ADD COLUMN used_promo_code TEXT;
        END IF;
        
        -- Add profile_image_url column if it doesn't exist
        IF NOT EXISTS (SELECT FROM pg_attribute 
                       WHERE attrelid = 'public.users'::regclass 
                       AND attname = 'profile_image_url' 
                       AND NOT attisdropped) THEN
            ALTER TABLE public.users ADD COLUMN profile_image_url TEXT;
        END IF;
        
        -- Add phone_number column if it doesn't exist
        IF NOT EXISTS (SELECT FROM pg_attribute 
                       WHERE attrelid = 'public.users'::regclass 
                       AND attname = 'phone_number' 
                       AND NOT attisdropped) THEN
            ALTER TABLE public.users ADD COLUMN phone_number TEXT;
        END IF;
    END IF;

    -- Ensure promo_codes table exists
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'promo_codes') THEN
        CREATE TABLE public.promo_codes (
            id SERIAL PRIMARY KEY,
            code TEXT NOT NULL UNIQUE,
            description TEXT NOT NULL,
            membership_type TEXT DEFAULT 'premium',
            discount_percent INTEGER NOT NULL,
            max_usages INTEGER DEFAULT 1,
            usage_count INTEGER DEFAULT 0,
            expires_at TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER REFERENCES public.users(id)
        );
    END IF;
END
$$;

-- Create initial admin user for all environments
-- Uses bcrypt hashed password: password123
DO $$
BEGIN
    -- Only add the initial admin user if the users table is empty
    IF NOT EXISTS (SELECT 1 FROM public.users LIMIT 1) THEN
        INSERT INTO public.users (
            email, 
            password, 
            is_admin, 
            is_verified,
            membership_type,
            created_at,
            updated_at
        ) VALUES (
            '<EMAIL>',
            -- Bcrypted password: password123
            '$2a$10$LuUZnkgFBYZcm64VSWX8Xu4NP9Q7kBgO6kOp5E1RvdR7CTZ9A8l4.',
            TRUE,
            TRUE,
            'premium',
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
        
        RAISE NOTICE 'Created initial admin user: <EMAIL>';
    END IF;
END
$$;