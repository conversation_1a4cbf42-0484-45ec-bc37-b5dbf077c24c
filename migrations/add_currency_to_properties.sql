-- Add missing currency and property_type columns to properties table if it exists
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'properties'
  ) THEN
    -- Add columns
    ALTER TABLE properties 
    ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD',
    ADD COLUMN IF NOT EXISTS property_type TEXT;
  END IF;
END
$$;

-- Update data type for latitude and longitude to ensure they are numeric
-- Only execute if the table and columns exist
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'properties'
  ) THEN
    IF EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'properties' 
      AND column_name = 'latitude'
    ) THEN
      ALTER TABLE properties 
      ALTER COLUMN latitude TYPE DECIMAL USING latitude::DECIMAL;
    END IF;
    
    IF EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'properties' 
      AND column_name = 'longitude'
    ) THEN
      ALTER TABLE properties 
      ALTER COLUMN longitude TYPE DECIMAL USING longitude::DECIMAL;
    END IF;
    
    IF EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'properties' 
      AND column_name = 'base_price'
    ) THEN
      ALTER TABLE properties 
      ALTER COLUMN base_price TYPE DECIMAL USING base_price::DECIMAL;
    END IF;
  END IF;
END
$$;

-- Add missing columns only if the properties table exists
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'properties'
  ) THEN
    -- Add missing columns
    ALTER TABLE properties 
    ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN IF NOT EXISTS discount INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS source TEXT DEFAULT 'api';
  END IF;
END
$$;