#!/bin/bash

# RoomLama Test Runner Script
# This script runs various test suites and generates a comprehensive report

# Colors for pretty output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create logs directory if it doesn't exist
mkdir -p test-logs

# Date and time for log files
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
LOG_DIR="test-logs/${TIMESTAMP}"
mkdir -p $LOG_DIR

# Main log file
MAIN_LOG="${LOG_DIR}/test_results.log"

# Initialize log file
echo "RoomLama Test Run - ${TIMESTAMP}" > $MAIN_LOG
echo "=================================" >> $MAIN_LOG
echo "" >> $MAIN_LOG

# Function to print formatted output
print_message() {
  local type=$1
  local message=$2
  
  case $type in
    "info")
      echo -e "${BLUE}[INFO]${NC} $message"
      echo "[INFO] $message" >> $MAIN_LOG
      ;;
    "success")
      echo -e "${GREEN}[SUCCESS]${NC} $message"
      echo "[SUCCESS] $message" >> $MAIN_LOG
      ;;
    "warning")
      echo -e "${YELLOW}[WARNING]${NC} $message"
      echo "[WARNING] $message" >> $MAIN_LOG
      ;;
    "error")
      echo -e "${RED}[ERROR]${NC} $message"
      echo "[ERROR] $message" >> $MAIN_LOG
      ;;
    *)
      echo -e "$message"
      echo "$message" >> $MAIN_LOG
      ;;
  esac
}

# Function to run a test suite and log results
run_test_suite() {
  local suite_name=$1
  local command=$2
  local log_file="${LOG_DIR}/${suite_name}.log"
  
  print_message "info" "Running test suite: ${suite_name}"
  echo "Test Suite: ${suite_name}" >> $MAIN_LOG
  echo "Command: ${command}" >> $MAIN_LOG
  echo "Log File: ${log_file}" >> $MAIN_LOG
  echo "" >> $MAIN_LOG
  
  # Run the test and capture output
  echo "============ Test Output ============" > $log_file
  eval $command 2>&1 | tee -a $log_file
  
  # Get test result (last command's exit status)
  local result=${PIPESTATUS[0]}
  
  if [ $result -eq 0 ]; then
    print_message "success" "Test suite ${suite_name} passed"
    echo "Result: PASS" >> $MAIN_LOG
  else
    print_message "error" "Test suite ${suite_name} failed with exit code ${result}"
    echo "Result: FAIL (Exit Code: ${result})" >> $MAIN_LOG
  fi
  
  echo "" >> $MAIN_LOG
  echo "==================================" >> $MAIN_LOG
  echo "" >> $MAIN_LOG
  
  return $result
}

# Function to check if the API server is running
check_server() {
  print_message "info" "Checking if API server is running..."
  
  # Try to access the API server
  curl -s http://localhost:5000/api/config > /dev/null
  
  if [ $? -eq 0 ]; then
    print_message "success" "API server is running"
    return 0
  else
    print_message "error" "API server is not running. Please start the server before running tests."
    return 1
  fi
}

# Basic environment check
print_message "info" "Starting RoomLama test run..."
print_message "info" "Node.js version: $(node -v)"
print_message "info" "NPM version: $(npm -v)"

# Check if server is running
check_server
if [ $? -ne 0 ]; then
  print_message "warning" "Some tests may be skipped due to the server not running"
fi

# Initialize counters
TOTAL_SUITES=0
PASSED_SUITES=0
FAILED_SUITES=0
SKIPPED_SUITES=0

# Unit Tests
print_message "info" "Running unit tests..."

# Run all unit tests using the unit test configuration
run_test_suite "unit-tests" "npx jest --config=jest.config.unit.ts"
if [ $? -eq 0 ]; then
  ((PASSED_SUITES++))
else
  ((FAILED_SUITES++))
fi
((TOTAL_SUITES++))

# If you want to run specific tests individually, uncomment the following:
# run_test_suite "stream-parser" "npx jest --config=jest.config.unit.ts server/__tests__/unit/stream-parser.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   ((FAILED_SUITES++))
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "stream-parser-enhanced" "npx jest --config=jest.config.unit.ts server/__tests__/unit/stream-parser-enhanced.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   ((FAILED_SUITES++))
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "openai-service" "npx jest --config=jest.config.unit.ts server/__tests__/unit/openai.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   ((FAILED_SUITES++))
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "chat-api" "npx jest --config=jest.config.unit.ts server/__tests__/unit/chat-api.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   ((FAILED_SUITES++))
# fi
# ((TOTAL_SUITES++))

# Functional Tests
print_message "info" "Running functional tests..."

# Run all functional tests using the functional test configuration
run_test_suite "functional-tests" "npx jest --config=jest.config.functional.ts"
if [ $? -eq 0 ]; then
  ((PASSED_SUITES++))
else
  # Check if tests were skipped due to server not running
  if grep -q "Skipping test because server is not running" "${LOG_DIR}/functional-tests.log"; then
    ((SKIPPED_SUITES++))
    print_message "warning" "Functional tests were skipped because server is not running"
  else
    ((FAILED_SUITES++))
  fi
fi
((TOTAL_SUITES++))

# If you want to run specific functional tests individually, uncomment the following:
# run_test_suite "ai-location-detection" "npx jest --config=jest.config.functional.ts server/__tests__/functional/ai-location-detection.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   # This might be skipped if server is not running
#   if grep -q "Skipping test because server is not running" "${LOG_DIR}/ai-location-detection.log"; then
#     ((SKIPPED_SUITES++))
#     print_message "warning" "Test suite ai-location-detection was skipped"
#   else
#     ((FAILED_SUITES++))
#   fi
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "ai-property-recommendations" "npx jest --config=jest.config.functional.ts server/__tests__/functional/ai-property-recommendations.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   # This might be skipped if server is not running
#   if grep -q "Skipping test because server is not running" "${LOG_DIR}/ai-property-recommendations.log"; then
#     ((SKIPPED_SUITES++))
#     print_message "warning" "Test suite ai-property-recommendations was skipped"
#   else
#     ((FAILED_SUITES++))
#   fi
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "chat-api" "npx jest --config=jest.config.functional.ts server/__tests__/functional/chat-api.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   # This might be skipped if server is not running
#   if grep -q "Skipping test because server is not running" "${LOG_DIR}/chat-api.log"; then
#     ((SKIPPED_SUITES++))
#     print_message "warning" "Test suite chat-api was skipped"
#   else
#     ((FAILED_SUITES++))
#   fi
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "search-functionality" "npx jest --config=jest.config.functional.ts server/__tests__/functional/search-functionality.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   # This might be skipped if server is not running
#   if grep -q "Skipping test because server is not running" "${LOG_DIR}/search-functionality.log"; then
#     ((SKIPPED_SUITES++))
#     print_message "warning" "Test suite search-functionality was skipped"
#   else
#     ((FAILED_SUITES++))
#   fi
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "booking-process" "npx jest --config=jest.config.functional.ts server/__tests__/functional/booking-process.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   # This might be skipped if server is not running
#   if grep -q "Skipping test because server is not running" "${LOG_DIR}/booking-process.log"; then
#     ((SKIPPED_SUITES++))
#     print_message "warning" "Test suite booking-process was skipped"
#   else
#     ((FAILED_SUITES++))
#   fi
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "auth-system" "npx jest --config=jest.config.functional.ts server/__tests__/functional/auth-system.test.ts"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   # This might be skipped if server is not running
#   if grep -q "Skipping test because server is not running" "${LOG_DIR}/auth-system.log"; then
#     ((SKIPPED_SUITES++))
#     print_message "warning" "Test suite auth-system was skipped"
#   else
#     ((FAILED_SUITES++))
#   fi
# fi
# ((TOTAL_SUITES++))

# React Component Tests
print_message "info" "Running React component tests..."

# Run all React component tests using the React test configuration
run_test_suite "react-component-tests" "npx jest --config=jest.config.react.ts"
if [ $? -eq 0 ]; then
  ((PASSED_SUITES++))
else
  ((FAILED_SUITES++))
fi
((TOTAL_SUITES++))

# If you want to run specific React component tests individually, uncomment the following:
# run_test_suite "ai-chat" "npx jest --config=jest.config.react.ts client/src/__tests__/AiChat.test.tsx"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   ((FAILED_SUITES++))
# fi
# ((TOTAL_SUITES++))
# 
# run_test_suite "ai-chat-enhanced" "npx jest --config=jest.config.react.ts client/src/__tests__/AiChat.enhanced.test.tsx"
# if [ $? -eq 0 ]; then
#   ((PASSED_SUITES++))
# else
#   ((FAILED_SUITES++))
# fi
# ((TOTAL_SUITES++))

# Print summary
echo ""
print_message "" "========================================"
print_message "" "            TEST RUN SUMMARY            "
print_message "" "========================================"
print_message "" "Total Test Suites: ${TOTAL_SUITES}"
print_message "success" "Passed: ${PASSED_SUITES}"
print_message "error" "Failed: ${FAILED_SUITES}"
print_message "warning" "Skipped: ${SKIPPED_SUITES}"
print_message "" "========================================"

# Log summary
echo "======================================" >> $MAIN_LOG
echo "TEST RUN SUMMARY" >> $MAIN_LOG
echo "======================================" >> $MAIN_LOG
echo "Total Test Suites: ${TOTAL_SUITES}" >> $MAIN_LOG
echo "Passed: ${PASSED_SUITES}" >> $MAIN_LOG
echo "Failed: ${FAILED_SUITES}" >> $MAIN_LOG
echo "Skipped: ${SKIPPED_SUITES}" >> $MAIN_LOG
echo "======================================" >> $MAIN_LOG

print_message "info" "Test logs saved to ${LOG_DIR}"

# Exit with failure if any tests failed
if [ $FAILED_SUITES -gt 0 ]; then
  exit 1
else
  exit 0
fi