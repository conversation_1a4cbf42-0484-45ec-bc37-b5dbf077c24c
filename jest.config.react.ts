/** @type {import('@jest/types').Config.InitialOptions} */
module.exports = {
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'jsdom',
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
    '^@/(.*)$': '<rootDir>/client/src/$1',
    '^@db$': '<rootDir>/db/index.ts',
    '^@db/(.*)$': '<rootDir>/db/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        useESM: true,
        tsconfig: {
          module: 'NodeNext',
          moduleResolution: 'NodeNext',
          jsx: 'react-jsx'
        }
      }
    ]
  },
  setupFiles: ['<rootDir>/jest.setup.ts'],
  setupFilesAfterEnv: ['@testing-library/jest-dom/extend-expect'],
  testTimeout: 10000,
  verbose: true,
  moduleDirectories: ['node_modules', '<rootDir>'],
  testMatch: [
    "**/client/src/__tests__/**/*.test.tsx",
  ],
  testEnvironmentOptions: {
    url: 'http://localhost/'
  }
};