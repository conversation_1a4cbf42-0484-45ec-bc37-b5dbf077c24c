{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx --inspect=0.0.0.0:9229 server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist --sourcemap", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "test": "NODE_OPTIONS=--experimental-vm-modules jest --config jest.config.ts"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^3.9.1", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.25", "@mui/icons-material": "^6.4.2", "@mui/material": "^6.4.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.4.0", "@tanstack/react-query": "^5.60.5", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.17", "@types/google.maps": "^3.58.1", "@types/jsonwebtoken": "^9.0.9", "@types/pg": "^8.11.13", "@types/socket.io": "^3.0.1", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@use-gesture/react": "^10.3.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "drizzle-orm": "^0.38.4", "drizzle-zod": "^0.6.1", "embla-carousel-react": "^8.5.2", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "mapbox-gl": "^3.9.4", "memorystore": "^1.6.7", "nanoid": "^5.1.2", "node-fetch": "^3.3.2", "openai": "^4.95.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.14.1", "postgres": "^3.4.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-map-gl": "^7.1.9", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.4", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^17.5.0", "supercluster": "^8.0.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.0", "winston": "^3.17.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.14", "@types/node": "^20.16.11", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.27.2", "esbuild": "^0.24.0", "jest": "^29.7.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.9"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}