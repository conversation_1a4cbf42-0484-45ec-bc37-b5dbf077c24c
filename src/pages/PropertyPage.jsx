import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

function PropertyPage() {
  console.log('PropertyPage render', { property, propertyId, checkIn, checkOut });
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [availabilityData, setAvailabilityData] = useState(null);
  const [roomRates, setRoomRates] = useState(null);
  const [shouldDisplayRates, setShouldDisplayRates] = useState(false);
  
  // First useEffect to set dates from URL
  useEffect(() => {
    const checkInParam = searchParams.get('checkIn');
    const checkOutParam = searchParams.get('checkOut');
    
    if (checkInParam && checkOutParam) {
      setCheckIn(new Date(checkInParam));
      setCheckOut(new Date(checkOutParam));
    }
  }, [searchParams]);

  // Second useEffect to trigger search after all required data is loaded
  useEffect(() => {
    const isReadyForSearch = 
      property && 
      propertyId && 
      checkIn && 
      checkOut && 
      !isLoading;

    if (isReadyForSearch) {
      console.log('Triggering initial availability search');
      handleAvailabilitySearch();
    }
  }, [property, propertyId, checkIn, checkOut, isLoading]);

  const handleAvailabilitySearch = async () => {
    if (isLoading) return; // Prevent multiple simultaneous requests
    
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/properties/${propertyId}/availability`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          checkIn: formatDate(checkIn),
          checkOut: formatDate(checkOut),
          guests: parseInt(searchParams.get('guests') || '2'),
          rooms: parseInt(searchParams.get('rooms') || '1'),
        }),
      });

      const data = await response.json();
      console.log('Availability data received:', data); // Debug log
      setAvailabilityData(data);
      setRoomRates(data);
      setShouldDisplayRates(true);  // Force UI update
    } catch (error) {
      console.error('Error fetching availability:', error);
      setError('Failed to fetch availability data');
    } finally {
      setIsLoading(false);
    }
  };

  // Add this effect to handle the display update
  useEffect(() => {
    if (roomRates && shouldDisplayRates) {
      // If you have a function that updates the display, call it here
      updateRateDisplay(roomRates);
      // Or if you're using context
      // updateRateContext(roomRates);
    }
  }, [roomRates, shouldDisplayRates]);

  return (
    <div>
      {/* ... existing JSX ... */}
      
      {isLoading && <div>Loading availability...</div>}
      {error && <div className="error">{error}</div>}
      
      {availabilityData && (
        <div className="availability-results">
          {/* Display your availability data here */}
          {availabilityData.map((room) => (
            <div key={room.id} className="room-option">
              {/* Room details */}
            </div>
          ))}
        </div>
      )}
      
      {/* ... rest of JSX ... */}
    </div>
  );
}

export default PropertyPage; 