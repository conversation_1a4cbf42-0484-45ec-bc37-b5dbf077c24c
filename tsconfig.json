{
  "include": ["client/src/**/*", "db/**/*", "server/**/*"],
  "exclude": ["node_modules", "build", "dist"],
  "compilerOptions": {
    "incremental": true,
    "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo",
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "noEmit": true,
    "sourceMap": true,
    "outDir": "./dist",

    /* Bundler mode */
    "moduleResolution": "Bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "allowJs": true,
    "esModuleInterop": true,

    /* Path Aliases */
    "baseUrl": ".",
    "paths": {
      "@/*": ["client/src/*"],
      "@db/*": ["db/*"]
    },
    "types": ["node", "vite/client", "jest"]
  }
}