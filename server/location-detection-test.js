/**
 * Location detection test script
 * This script tests the chat API's ability to detect and process location data
 */

const fetch = require('node-fetch');

// Test messages with location requests
const TEST_MESSAGES = [
  "I want to find hotels in Miami Beach",
  "Are there any good places to stay in New York City?",
  "I'm looking for accommodation near the Eiffel Tower in Paris",
  "Show me hotels in San Francisco near Fisherman's Wharf",
  "I need a place to stay in Tokyo"
];

// Function to safely parse SSE data
function parseSSEData(data) {
  if (!data.startsWith('data: ')) {
    return null;
  }
  
  const jsonStr = data.slice(6); // Remove 'data: ' prefix
  
  if (jsonStr === '[DONE]') {
    return { done: true };
  }
  
  try {
    return JSON.parse(jsonStr);
  } catch (error) {
    console.error('Failed to parse SSE data:', jsonStr);
    return null;
  }
}

// Function to test chat with location detection
async function testLocationDetection(message) {
  console.log(`\n\nTesting message: "${message}"`);
  console.log('----------------------------------------');
  
  try {
    const response = await fetch('http://localhost:5000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message,
        sessionId: `test-${Date.now()}`,
        context: {}
      })
    });
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    const reader = response.body.getReader();
    let receivedData = '';
    let detectedLocation = null;
    let responseParts = [];
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        break;
      }
      
      // Convert the received chunk to a string and add it to the accumulated data
      const chunk = new TextDecoder().decode(value);
      receivedData += chunk;
      
      // Process complete SSE events
      const events = receivedData.split('\n\n');
      receivedData = events.pop(); // Keep the last potentially incomplete event
      
      for (const event of events) {
        if (!event.trim()) continue;
        
        const parsed = parseSSEData(event);
        if (!parsed) continue;
        
        if (parsed.done) continue;
        
        // Check if this is a location response
        if (parsed.type === 'location') {
          detectedLocation = parsed.data;
          console.log('📍 LOCATION DETECTED:', {
            name: detectedLocation.name,
            coordinates: `${detectedLocation.lat}, ${detectedLocation.lng}`,
            placeType: detectedLocation.placeType || 'unknown'
          });
        } else if (parsed.type === 'text') {
          responseParts.push(parsed.data);
        }
      }
    }
    
    if (detectedLocation) {
      console.log('✅ Test PASSED: Successfully detected location');
    } else {
      console.log('❌ Test FAILED: No location detected in response');
    }
    
    // Print combined text response
    console.log('\nText Response:');
    console.log(responseParts.join(' '));
    
    return { success: !!detectedLocation, location: detectedLocation };
  } catch (error) {
    console.error('Error during test:', error);
    return { success: false, error: error.message };
  }
}

// Run tests for all messages
async function runAllTests() {
  console.log('Starting location detection tests...');
  
  const results = {
    total: TEST_MESSAGES.length,
    passed: 0,
    failed: 0,
    locations: []
  };
  
  for (const message of TEST_MESSAGES) {
    const result = await testLocationDetection(message);
    
    if (result.success) {
      results.passed++;
      if (result.location) {
        results.locations.push({
          message,
          location: result.location
        });
      }
    } else {
      results.failed++;
    }
  }
  
  // Print summary
  console.log('\n\n=============== TEST SUMMARY ===============');
  console.log(`Total tests: ${results.total}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success rate: ${(results.passed / results.total * 100).toFixed(2)}%`);
  
  if (results.locations.length > 0) {
    console.log('\nDetected locations:');
    results.locations.forEach((item, index) => {
      console.log(`${index + 1}. "${item.message}" → ${item.location.name} (${item.location.lat}, ${item.location.lng})`);
    });
  }
}

// Execute all tests
runAllTests().catch(console.error);