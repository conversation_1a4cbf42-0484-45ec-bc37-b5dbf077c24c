/**
 * Simple location detection test script
 * This is a lightweight test to verify location detection in chat responses
 */

import fetch from 'node-fetch';

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
}

// Function to run a simple test
async function testLocationDetection() {
  console.log('Running simple location detection test...');
  
  try {
    // Send a simple message asking about a location with explicit request for full debug output
    const response = await fetch('http://localhost:5000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Debug-Mode': 'true'
      },
      body: JSON.stringify({
        message: 'I want to find hotels in Miami Beach',
        sessionId: `test-session-${Date.now()}`,
        debug: true,
        extractLocation: true  // Signal to the server that we want location extraction
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    // Process the response
    const responseText = await response.text();
    
    // SSE responses are formatted as "data: {...}\n\n"
    const events = responseText.split('\n\n');
    
    console.log(`Received ${events.length} events`);
    
    // Debug: Print full response text for inspection
    console.log('--- Full Response Text ---');
    console.log(responseText);
    console.log('------------------------');

    // Variables to store our findings
    let foundLocationData = false;
    let locationData: LocationData | null = null;
    
    // Process each event
    for (let i = 0; i < events.length; i++) {
      const event = events[i];
      if (!event.trim() || !event.startsWith('data:')) continue;
      
      const jsonStr = event.substring(5).trim();
      if (jsonStr === '[DONE]' || jsonStr === 'DONE') continue;
      
      try {
        const parsed = JSON.parse(jsonStr);
        console.log(`Received event #${i+1} type: ${parsed.type}`);
        console.log(`Event data: ${JSON.stringify(parsed.data, null, 2)}`);
        
        // Check specifically for location type
        if (parsed.type === 'location' && parsed.data) {
          foundLocationData = true;
          locationData = parsed.data;
          console.log('FOUND LOCATION DATA:', locationData);
        }
        
        // Also check for action type that might contain location
        if (parsed.type === 'action' && 
            parsed.data &&
            parsed.data.type === 'location' &&
            parsed.data.data) {
          foundLocationData = true;
          locationData = parsed.data.data;
          console.log('FOUND LOCATION IN ACTION:', locationData);
        }
      } catch (e) {
        console.error('Error parsing event:', e);
      }
    }
    
    // Report results
    if (foundLocationData && locationData) {
      console.log('✅ SUCCESS: Location detected');
      console.log(`Location: ${locationData.name}`);
      console.log(`Coordinates: ${locationData.lat}, ${locationData.lng}`);
      console.log(`Place type: ${locationData.placeType || 'unknown'}`);
    } else {
      console.log('❌ FAIL: No location detected in the response');
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test
testLocationDetection().catch(console.error);