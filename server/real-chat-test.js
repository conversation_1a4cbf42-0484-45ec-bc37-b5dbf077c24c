/**
 * Real-world test for the chat endpoint
 * This test sends a real query to the API to verify the chat functionality
 */

import fetch from 'node-fetch';

// Test request parameters
const testParams = {
  url: 'http://localhost:5000/api/chat',
  message: "Hi! I'm looking for a place to stay in Las Vegas, NV, USA for 2 people. Can you help me find the perfect place?",
  sessionId: `test-session-${Date.now()}`,
  context: {
    location: {
      name: 'Las Vegas, NV, USA',
      lat: 36.1699,
      lng: -115.1398
    },
    dateRange: {
      checkIn: '2025-04-15',
      checkOut: '2025-04-20'
    },
    guests: 2,
    rooms: 1
  }
};

// Function to parse SSE events
function parseSSE(text) {
  return text
    .split('\n\n')
    .filter(chunk => chunk.trim().startsWith('data:'))
    .map(chunk => {
      const data = chunk.replace('data:', '').trim();
      try {
        // Only attempt to parse if it looks like JSON
        if (data.startsWith('{') || data.startsWith('[')) {
          return {
            type: 'json',
            data: JSON.parse(data),
            raw: data
          };
        } else if (data === '[DONE]' || data === 'DONE') {
          return {
            type: 'control',
            data: { action: 'done' },
            raw: data
          };
        } else {
          // Non-JSON data (like log messages)
          return {
            type: 'text',
            data: data,
            raw: data
          };
        }
      } catch (e) {
        return {
          type: 'error',
          error: e.message,
          raw: data
        };
      }
    });
}

// Run the test
async function runTest() {
  console.log('======= REAL CHAT API TEST =======');
  console.log(`Testing with message: "${testParams.message}"`);
  
  try {
    // Make the API request
    const response = await fetch(testParams.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: testParams.message,
        sessionId: testParams.sessionId,
        context: testParams.context
      })
    });

    if (!response.ok) {
      // Handle HTTP error
      const errorText = await response.text();
      console.error(`HTTP Error ${response.status}: ${errorText}`);
      
      try {
        // Try to parse as JSON for more details
        const errorJson = JSON.parse(errorText);
        console.error('Error details:', JSON.stringify(errorJson, null, 2));
      } catch (e) {
        // If it's not JSON, just show the raw text
        console.error('Raw error:', errorText);
      }
      
      return { success: false, error: `HTTP Error ${response.status}` };
    }

    // For SSE responses
    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      // Handle SSE response - using node-fetch which doesn't support the getReader API
      // Instead we'll get the response as text
      const responseText = await response.text();
      let buffer = responseText;
      let chunkCount = 0;
      let validChunks = 0;
      let invalidChunks = 0;
      let logChunks = 0;

      console.log('\nReceiving server-sent events:');
      
      // Process complete events
      if (buffer.includes('\n\n')) {
        const parts = buffer.split('\n\n');
        
        for (const part of parts) {
          if (!part.trim()) continue;
          
          chunkCount++;
          console.log(`\n----- Chunk #${chunkCount} -----`);
          console.log(part);
          
          // Try to parse the event
          try {
            const eventData = part.replace('data:', '').trim();
            
            // Check if it's a log message
            if (eventData.startsWith('Initialize') || 
                eventData.startsWith('Added search') || 
                eventData.startsWith('Recorded') || 
                eventData.startsWith('Updated')) {
              console.log('Detected log message - should be filtered out');
              logChunks++;
              continue;
            }
            
            // Check if it's a done message
            if (eventData === '[DONE]' || eventData === 'DONE') {
              console.log('Stream complete marker received');
              validChunks++;
              continue;
            }
            
            // Try to parse as JSON
            const parsed = JSON.parse(eventData);
            console.log('✅ Valid JSON:', JSON.stringify(parsed).substring(0, 100) + '...');
            validChunks++;
          } catch (e) {
            console.log(`❌ Parse error: ${e.message}`);
            invalidChunks++;
            
            // Additional debugging
            console.log('Raw chunk:', part);
          }
        }
      }
      
      // Log stats
      console.log('\n----- Stream Stats -----');
      console.log(`Total chunks: ${chunkCount}`);
      console.log(`Valid chunks: ${validChunks}`);
      console.log(`Invalid chunks: ${invalidChunks}`);
      console.log(`Log message chunks: ${logChunks}`);
      
      return { 
        success: true, 
        stats: { 
          total: chunkCount, 
          valid: validChunks, 
          invalid: invalidChunks,
          logs: logChunks 
        } 
      };
    } else {
      // Handle regular JSON response
      const data = await response.json();
      console.log('Response:', JSON.stringify(data, null, 2));
      return { success: true, data };
    }
  } catch (error) {
    console.error('Test failed with error:', error.message);
    if (error.stack) console.error(error.stack);
    return { success: false, error: error.message };
  }
}

// Execute the test
runTest()
  .then(result => {
    console.log('\nTest execution completed');
    console.log('Result:', JSON.stringify(result, null, 2));
    process.exit(result.success ? 0 : 1);
  })
  .catch(err => {
    console.error('Test execution failed:', err);
    process.exit(1);
  });