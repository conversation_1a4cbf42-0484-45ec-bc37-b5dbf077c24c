import { db } from "@db/index.js";
import { properties } from "@db/schema.js";
import { searchTravSrvProperties } from "../services/travsrv.js";
import { log } from "../vite.js";
import { sql } from "drizzle-orm";

const BATCH_SIZE = 100;
const RADIUS_KM = 10;

export async function refreshProperties() {
  try {
    log('Starting daily property refresh job');
    
    // Get all unique locations (using first property in each cluster)
    const locations = await db.query.properties.findMany({
      columns: {
        latitude: true,
        longitude: true
      },
      where: sql`
        NOT EXISTS (
          SELECT 1 FROM ${properties} p2
          WHERE point(${properties.latitude}::float, ${properties.longitude}::float) <@> 
                point(p2.latitude::float, p2.longitude::float) <= ${RADIUS_KM}
          AND (${properties.latitude} != p2.latitude OR ${properties.longitude} != p2.longitude)
          AND ${properties.id} > p2.id
        )
      `
    });

    log(`Found ${locations.length} unique locations to refresh`);

    // Process each location
    for (const location of locations) {
      try {
        // Get fresh properties from API
        const freshProperties = await searchTravSrvProperties({
          latitude: parseFloat(location.latitude),
          longitude: parseFloat(location.longitude),
          inDate: new Date().toISOString().split('T')[0], // today
          outDate: new Date(Date.now() + 86400000).toISOString().split('T')[0], // tomorrow
          radius: RADIUS_KM
        });

        if (freshProperties.length === 0) {
          log(`No properties found for location ${location.latitude},${location.longitude}`);
          continue;
        }

        // Update properties in batches
        for (let i = 0; i < freshProperties.length; i += BATCH_SIZE) {
          const batch = freshProperties.slice(i, i + BATCH_SIZE);
          
          await db.transaction(async (tx) => {
            for (const property of batch) {
              const propertyWithTimestamp = {
                ...property,
                lastUpdated: new Date()
              };
              
              await tx.insert(properties)
                .values(propertyWithTimestamp)
                .onConflictDoUpdate({
                  target: [properties.id],
                  set: propertyWithTimestamp
                });
            }
          });
        }

        log(`Updated ${freshProperties.length} properties for location ${location.latitude},${location.longitude}`);
      } catch (error) {
        log(`Error refreshing location ${location.latitude},${location.longitude}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        continue; // Continue with next location even if one fails
      }
    }

    log('Completed daily property refresh job');
  } catch (error) {
    log(`Property refresh job failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
} 