/**
 * Simple standalone test for verifying SSE data parsing
 */

function safeParseSSEData(rawData) {
  // Check if it's an SSE format
  if (!rawData.startsWith('data: ')) {
    return { valid: false, error: new Error('Not SSE format') };
  }

  // Extract the data part 
  const data = rawData.slice(6).trim();
  
  // Skip if it's a DONE message
  if (data === '[DONE]' || data === 'DONE') {
    return { valid: true, parsed: { type: 'control', action: 'done' } };
  }

  try {
    // Skip non-JSON data like log messages
    if (data.includes('Initialize') || 
        data.includes('Added search') || 
        data.includes('Recorded property') || 
        data.includes('Updated filters') ||
        data.includes('Added property to comparison') ||
        data.includes('Recorded booking attempt') ||
        data.includes('Updated conversation context') ||
        data.includes('Added message')) {
      return { 
        valid: false, 
        error: new Error(`Status message encountered: ${data.substring(0, 30)}...`) 
      };
    }

    // Try to detect if the data is not valid JSON before parsing
    if (!/^[\[\{]/.test(data.trim())) {
      return { 
        valid: false, 
        error: new Error(`Not valid JSON format: ${data.substring(0, 30)}...`) 
      };
    }

    const parsed = JSON.parse(data);
    return { valid: true, parsed };
  } catch (e) {
    return { valid: false, error: e };
  }
}

// Test data examples
const testData = [
  'data: {"type":"text","data":"Hello, how can I help you?"}\n\n',
  'data: {"type":"properties","data":[{"id":1,"name":"Hotel A"}]}\n\n',
  'data: Initialized new context\n\n',
  'data: Added search to context\n\n',
  'data: [DONE]\n\n',
  'data: {"malformed": "json"\n\n',
  'data: just some plain text\n\n',
  'data: {"type":"action","data":{"type":"location","label":"New York","data":{"lat":40.7128,"lng":-74.0060}}}\n\n'
];

console.log('===== TESTING SSE DATA PARSING =====');

// Run tests on each data example
testData.forEach((rawData, index) => {
  console.log(`\nTest #${index + 1}: ${rawData.substring(0, 40)}...`);
  const result = safeParseSSEData(rawData);
  if (result.valid) {
    console.log('✅ VALID: ', JSON.stringify(result.parsed).substring(0, 60) + '...');
  } else {
    console.log('❌ INVALID: ', result.error.message);
  }
});

console.log('\n===== TESTING MALFORMED JSON DATA =====');

// Test specifically for JSON data that passes the regex check but fails to parse
const malformedJsonData = [
  'data: {"type":"text","data":"This is incomplete...',
  'data: {"type":"text",data:"Missing quotes"}\n\n',
  'data: {"type":"text","data":{"nested":{{"broken":true}}\n\n'
];

malformedJsonData.forEach((rawData, index) => {
  console.log(`\nBad JSON Test #${index + 1}: ${rawData.substring(0, 40)}...`);
  const result = safeParseSSEData(rawData);
  if (result.valid) {
    console.log('✅ VALID: ', JSON.stringify(result.parsed).substring(0, 60) + '...');
  } else {
    console.log('❌ INVALID: ', result.error.message);
  }
});

// Test with our filtering solution
console.log('\n===== TESTING OUR FILTERING SOLUTION =====');

// Simulate how our solution handles problematic data
function processStreamChunk(data) {
  try {
    // Filter out log messages
    if (data.includes('Initialize') || 
        data.includes('Added search') || 
        data.includes('Recorded property') || 
        data.includes('Updated filters') ||
        data.includes('Added property to comparison') ||
        data.includes('Recorded booking attempt') ||
        data.includes('Updated conversation context') ||
        data.includes('Added message')) {
      console.log('- Filtered out log message:', data.substring(0, 30) + '...');
      return null;
    }
    
    // Check JSON format
    if (!/^[\[\{]/.test(data.trim())) {
      console.log('- Not JSON format:', data.substring(0, 30) + '...');
      return null;
    }
    
    // Try parsing
    try {
      JSON.parse(data);
      return data;
    } catch (e) {
      console.log('- JSON parse error:', e.message);
      return null;
    }
  } catch (e) {
    console.log('- Processing error:', e.message);
    return null;
  }
}

// Test the processing function
console.log('\nValidating our solution:');

const streamData = [
  '{"type":"text","data":"Hello, how can I help you?"}',
  'Initialized new context for session 123',
  'Added search to context',
  '{"type":"action","data":{"type":"location","label":"New York"}}',
  '{"malformed: "json"}',
  'Updated filters in user preferences'
];

// Process each chunk and see what passes through
let validCount = 0;
let filteredCount = 0;

streamData.forEach((chunk, index) => {
  console.log(`\nProcessing chunk #${index + 1}: ${chunk.substring(0, 30)}...`);
  const processed = processStreamChunk(chunk);
  if (processed) {
    console.log('✅ VALID - would be sent to client');
    validCount++;
  } else {
    console.log('❌ FILTERED - would be dropped');
    filteredCount++;
  }
});

console.log('\nProcessing Summary:');
console.log(`- Total chunks: ${streamData.length}`);
console.log(`- Valid chunks sent: ${validCount}`);
console.log(`- Filtered chunks: ${filteredCount}`);
console.log('- Success rate: ' + Math.round((validCount / streamData.length) * 100) + '%');