/**
 * Enhanced Chat Routes
 * 
 * This file provides improved chat API routes with proactive intelligence,
 * enhanced location understanding, improved technical architecture, and
 * comprehensive testing capabilities.
 */
import { Request, Response } from 'express';
import logger, { logOperation, logError } from '../utils/logger.js';
import { 
  handleChatStream, 
  getConversationContext,
  addMessageToConversation,
  TypedChatResponse,
  TextResponse,
  LocationResponse,
  PropertiesResponse,
  ActionResponse,
  ErrorResponse,
  ChatMessage
} from '../services/openai.js';
import { getEnhancedContext, updateEnhancedContext, recordUserMessage, recordAiResponse } from '../services/enhancedContextService.js';
import { getTravelInsights } from '../services/proactiveIntelligence.js';
import { getLocationDetails, validateLocationCoordinates } from '../services/enhancedLocationService.js';
import { runTestCase, testLocationDetection, monitorPerformance } from '../services/chatTestingService.js';
import { contextService } from '../services/contextService.js';

/**
 * Enhanced chat endpoint with improved capabilities
 */
export async function enhancedChatHandler(req: Request, res: Response) {
  const requestId = `enhanced-chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const { 
    message, 
    sessionId = `session-${Date.now()}`, 
    debug = false, 
    extractLocation = true,
    testMode = false 
  } = req.body;
  
  // Check for debug mode header
  const isDebugMode = req.headers['x-debug-mode'] === 'true' || debug === true;

  if (!message) {
    return res.status(400).json({ error: "Message is required" });
  }

  try {
    // Set up SSE headers
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");

    // Get enhanced session context
    const enhancedContext = getEnhancedContext(sessionId);
    
    // Record user message
    recordUserMessage(sessionId, message);

    // Get conversation from original service (compatibility layer)
    const conversation = getConversationContext(sessionId);
    
    // Send initial response indicating we're processing
    const processingResponse: TextResponse = {
      type: 'text',
      data: {
        content: "I'm searching for information to help you. One moment please...",
        isLoading: true
      }
    };
    res.write(`data: ${JSON.stringify(processingResponse)}\n\n`);
    
    // Flag to track if any response was sent
    let responseSent = true;
    
    // Start processing time for metrics
    const processingStartTime = Date.now();
    
    // Generate proactive insights if we have location context
    let proactiveInsights = null;
    
    if (conversation.context.location) {
      proactiveInsights = await getTravelInsights(
        sessionId,
        conversation.context,
        enhancedContext.searchContext
      );
      
      // Log insights for debugging
      if (isDebugMode) {
        logger.debug('Generated proactive insights', { 
          insights: JSON.stringify(proactiveInsights),
          requestId 
        });
      }
    }
    
    // Get enhanced location details if we have a location
    let locationDetails = null;
    
    if (conversation.context.location) {
      locationDetails = await getLocationDetails(
        conversation.context.location.name,
        conversation.context.location.lat,
        conversation.context.location.lng
      );
      
      // Log location details for debugging
      if (isDebugMode) {
        logger.debug('Enhanced location details', { 
          details: JSON.stringify(locationDetails),
          requestId 
        });
      }
    }
    
    // Create generator for chat stream
    const chatStream = handleChatStream(
      message,
      {
        conversation: conversation.context,
        messages: conversation.messages,
        searchContext: enhancedContext.searchContext,
        userPreferences: enhancedContext.userPreferences
      },
      req
    );
    
    // Process the stream
    for await (const response of chatStream) {
      // Process response based on type
      switch (response.type) {
        case 'location':
          // Enhanced location validation
          const locationResponse = response as LocationResponse;
          const location = locationResponse.data.location;
          
          // Validate location coordinates
          const isValidLocation = validateLocationCoordinates(
            location.name,
            location.lat,
            location.lng
          );
          
          if (!isValidLocation) {
            logger.warn('Invalid location coordinates detected', {
              location: location.name,
              coordinates: `${location.lat},${location.lng}`,
              requestId
            });
            
            // Override with corrected coordinates if available
            // This is a placeholder - in a production system, this would use
            // a more sophisticated geocoding correction system
            // For now, we'll just log the issue
          }
          
          // Get enhanced location details
          const enhancedLocation = await getLocationDetails(
            location.name,
            location.lat,
            location.lng
          );
          
          // Add enhanced details to the response if available
          if (enhancedLocation && enhancedLocation.neighborhoods) {
            locationResponse.data.enhancedDetails = {
              neighborhoods: enhancedLocation.neighborhoods.map(n => n.name),
              landmarks: enhancedLocation.landmarks?.map(l => l.name),
              popularTimes: enhancedLocation.popularTimes,
              weatherPattern: enhancedLocation.weatherPattern
            };
          }
          
          // Send enhanced location response
          res.write(`data: ${JSON.stringify(locationResponse)}\n\n`);
          break;
          
        case 'text':
          // Handle text response
          const textResponse = response as TextResponse;
          
          // Check if we have proactive insights to add
          if (proactiveInsights && 
              (proactiveInsights.eventAlerts.length > 0 || 
               proactiveInsights.pricingInsights.length > 0)) {
            
            // Inject insights into the response
            let enhancedContent = textResponse.data.content;
            
            // Add price insights if available
            if (proactiveInsights.pricingInsights.length > 0) {
              const insight = proactiveInsights.pricingInsights[0];
              enhancedContent += `\n\n💡 ${insight.message}`;
            }
            
            // Add event alerts if available
            if (proactiveInsights.eventAlerts.length > 0) {
              const alert = proactiveInsights.eventAlerts[0];
              enhancedContent += `\n\n⚠️ ${alert.message}`;
            }
            
            // Add travel tips if available
            if (proactiveInsights.tips.length > 0) {
              enhancedContent += `\n\n✨ Tip: ${proactiveInsights.tips[0]}`;
            }
            
            // Update the response content
            textResponse.data.content = enhancedContent;
            textResponse.data.enhancedWithInsights = true;
          }
          
          // Send enhanced text response
          res.write(`data: ${JSON.stringify(textResponse)}\n\n`);
          break;
          
        case 'properties':
          // Handle properties response
          const propertiesResponse = response as PropertiesResponse;
          
          // Add alternative suggestions if available
          if (proactiveInsights && proactiveInsights.alternativeSuggestions.length > 0) {
            const locationAlternatives = proactiveInsights.alternativeSuggestions
              .filter(s => s.type === 'location')
              .map(s => ({ 
                name: s.data.name, 
                reason: s.reason, 
                lat: s.data.lat, 
                lng: s.data.lng 
              }));
              
            if (locationAlternatives.length > 0) {
              propertiesResponse.data.alternativeLocations = locationAlternatives;
            }
          }
          
          // Send enhanced properties response
          res.write(`data: ${JSON.stringify(propertiesResponse)}\n\n`);
          break;
          
        default:
          // Send other responses as-is
          res.write(`data: ${JSON.stringify(response)}\n\n`);
          break;
      }
      
      responseSent = true;
      
      // Record AI response for metrics
      recordAiResponse(
        sessionId,
        response.type,
        response,
        Date.now() - processingStartTime
      );
    }

    // If no response was sent, send a fallback error
    if (!responseSent) {
      const fallbackResponse: ErrorResponse = {
        type: 'error',
        data: {
          message: "I'm sorry, I couldn't process your request properly. Please try again."
        }
      };
      res.write(`data: ${JSON.stringify(fallbackResponse)}\n\n`);
    }

    // End the stream
    res.write("data: [DONE]\n\n");
    res.end();

  } catch (error) {
    // Ensure we handle any unexpected errors effectively
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    // Create more detailed error info for debugging
    const errorDetails = {
      message: errorMessage || 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace available',
      type: error instanceof Error ? error.constructor.name : typeof error,
      context: {
        messageLength: message?.length || 0,
        sessionId,
        extractLocation: extractLocation === true,
        requestId,
        params: Object.keys(req.body || {}).join(',')
      }
    };

    // Log the error with detailed information
    logger.error('Enhanced Chat API Error:', { 
      error: errorDetails,
      requestId
    });

    // Error handling
    if (!res.headersSent) {
      // If headers not sent yet, send a JSON error
      res.status(500).json({ 
        error: "Failed to process chat request",
        details: process.env.NODE_ENV === 'development' ? errorDetails : errorMessage,
        requestId
      });
    } else {
      // If headers already sent, continue the stream with an error message
      try {
        const errorResponse = {
          type: 'error',
          data: {
            message: "There was an error processing your request. Please try again.",
            details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
          }
        };
        res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        res.write("data: [DONE]\n\n");
        res.end();
      } catch (streamError) {
        // If we can't even write to the stream, log and give up
        logger.error('Failed to send error response:', { 
          error: streamError,
          requestId
        });
      }
    }
  }
}

/**
 * API endpoint for testing chat functionality
 */
export async function chatTestHandler(req: Request, res: Response) {
  const { testCase, sessionId } = req.body;
  
  if (!testCase) {
    return res.status(400).json({ error: "Test case is required" });
  }
  
  try {
    // Run the test case
    const result = await runTestCase(testCase);
    
    // Return the test results
    res.json({
      success: true,
      result
    });
  } catch (error) {
    logger.error('Chat test failed', {
      testCaseId: testCase.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * API endpoint for testing location detection
 */
export async function locationTestHandler(req: Request, res: Response) {
  try {
    // Run location detection tests
    const results = await testLocationDetection();
    
    // Return the test results
    res.json({
      success: true,
      results
    });
  } catch (error) {
    logger.error('Location detection test failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * API endpoint for getting travel insights
 */
export async function travelInsightsHandler(req: Request, res: Response) {
  const { 
    sessionId, 
    location, 
    checkIn, 
    checkOut, 
    preferences 
  } = req.body;
  
  if (!sessionId || !location) {
    return res.status(400).json({ error: "Session ID and location are required" });
  }
  
  try {
    // Get context for this session
    const enhancedContext = getEnhancedContext(sessionId);
    const conversation = getConversationContext(sessionId);
    
    // Create a conversation context with the provided information
    const contextForInsights = {
      ...conversation.context,
      location: location.name ? location : { 
        name: location,
        lat: 0,
        lng: 0
      },
      dateRange: checkIn && checkOut ? { checkIn, checkOut } : conversation.context.dateRange,
      preferences: preferences || conversation.context.preferences
    };
    
    // Generate travel insights
    const insights = await getTravelInsights(
      sessionId,
      contextForInsights,
      enhancedContext.searchContext
    );
    
    // Return the insights
    res.json({
      success: true,
      insights
    });
  } catch (error) {
    logger.error('Failed to generate travel insights', {
      sessionId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Register enhanced chat routes
 */
export function registerEnhancedChatRoutes(app: any) {
  // Enhanced chat endpoint
  app.post("/api/chat/enhanced", enhancedChatHandler);
  
  // Chat testing endpoints
  app.post("/api/chat/test", chatTestHandler);
  app.post("/api/chat/test/location", locationTestHandler);
  
  // Travel insights endpoint
  app.post("/api/travel/insights", travelInsightsHandler);
  
  logger.info('Enhanced chat routes registered');
}