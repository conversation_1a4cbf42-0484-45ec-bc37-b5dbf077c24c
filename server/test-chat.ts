/**
 * Test wrapper for the chat functionality to isolate and debug JSON parsing issues
 */
import { Request, Response } from 'express';
import { contextService } from './services/contextService.js';
import { 
  getConversationContext, 
  handleChatStream, 
  addMessageToConversation, 
  needsSummarization, 
  summarizeConversation
} from './services/openai.js';

// Import the necessary types
import type { 
  ChatResponse, 
  TypedChatResponse,
  TextResponse,
  PropertiesResponse,
  LocationResponse,
  ActionResponse,
  ErrorResponse
} from './services/openai.js';
import logger, { logError, logOperation } from './utils/logger.js';

/**
 * Creates a fake request/response pair for testing
 */
function createMockReqRes() {
  const req = {
    body: {},
    headers: {}
  } as unknown as Request;

  const resData: { 
    headers: Record<string, string>,
    statusCode: number,
    body: string[],
    ended: boolean
  } = {
    headers: {},
    statusCode: 200,
    body: [],
    ended: false
  };

  const res = {
    setHeader: (name: string, value: string) => {
      resData.headers[name] = value;
      return res;
    },
    status: (code: number) => {
      resData.statusCode = code;
      return res;
    },
    write: (chunk: string) => {
      resData.body.push(chunk);
      return res;
    },
    json: (data: any) => {
      resData.body.push(JSON.stringify(data));
      resData.ended = true;
      return res;
    },
    end: () => {
      resData.ended = true;
      return res;
    },
    headersSent: false
  } as unknown as Response;

  return { req, res, resData };
}

/**
 * Safely parse SSE data to detect non-JSON content with enhanced error handling
 */
function safeParseSSEData(rawData: string): { valid: boolean, parsed?: any, error?: Error, reason?: string } {
  // Check if it's an SSE format
  if (!rawData.startsWith('data: ')) {
    return { valid: false, error: new Error('Not SSE format') };
  }

  // Extract the data part 
  const data = rawData.slice(6).trim();
  
  // Skip if it's a DONE message
  if (data === '[DONE]' || data === 'DONE') {
    return { valid: true, parsed: { type: 'control', action: 'done' } };
  }

  try {
    // Skip non-JSON data like log messages
    if (data.startsWith('Initialize') || 
        data.startsWith('Added search') || 
        data.startsWith('Recorded property') || 
        data.startsWith('Updated filters') ||
        data.startsWith('Added property to comparison') ||
        data.startsWith('Recorded booking attempt') ||
        data.startsWith('Updated conversation context') ||
        data.startsWith('Added message')) {
      return { 
        valid: false, 
        error: new Error(`Status message encountered: ${data.substring(0, 30)}...`) 
      };
    }

    // Try to detect if the data is not valid JSON before parsing
    if (!/^[\[\{]/.test(data.trim())) {
      return { 
        valid: false, 
        error: new Error(`Not valid JSON format: ${data.substring(0, 30)}...`) 
      };
    }

    const parsed = JSON.parse(data);
    return { valid: true, parsed };
  } catch (e) {
    return { valid: false, error: e as Error };
  }
}

/**
 * Test the chat API with a sample message
 */
export async function testChatAPI(message: string = "Hello, I'm looking for a hotel in New York") {
  console.log('=========== CHAT API TEST ===========');
  console.log(`Testing with message: "${message}"`);
  
  const { req, res, resData } = createMockReqRes();
  
  const sessionId = `test-session-${Date.now()}`;
  const requestId = `test-chat-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  
  try {
    // Setup test request
    req.body = { 
      message, 
      sessionId,
      context: {
        location: {
          name: 'New York',
          latitude: 40.7128,
          longitude: -74.0060
        },
        dateRange: {
          checkIn: '2025-04-01',
          checkOut: '2025-04-05'
        },
        guests: 2,
        rooms: 1
      }
    };
    
    // Set up SSE headers
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    
    // Get session context
    const sessionContext = contextService.getContext(sessionId);
    const conversation = getConversationContext(sessionId);
    
    // Add user message to conversation
    addMessageToConversation(sessionId, {
      role: 'user',
      content: message
    });
    
    // Prepare chat context
    const chatContext = {
      conversation: conversation.context,
      messages: conversation.messages,
      searchContext: sessionContext.searchContext,
      userPreferences: sessionContext.userPreferences
    };
    
    console.log('Chat context prepared:', JSON.stringify(chatContext, null, 2));
    
    // Process chat stream
    const chatStream = handleChatStream(message, chatContext, req);
    
    let responseSent = false;
    let chunkCount = 0;
    let validChunks = 0;
    let invalidChunks = 0;
    
    console.log('\nBeginning stream processing...');
    
    // Process each chunk and collect metrics
    for await (const chunk of chatStream) {
      chunkCount++;
      console.log(`\nChunk #${chunkCount}:`);
      
      if (chunk) {
        // Debug info about the chunk
        console.log(`- Type: ${typeof chunk}`);
        console.log(`- Has 'type' property: ${typeof chunk === 'object' && 'type' in chunk}`);
        console.log(`- Has 'data' property: ${typeof chunk === 'object' && 'data' in chunk}`);
        
        // For logging, convert to string safely
        const chunkPreview = typeof chunk === 'object' 
          ? JSON.stringify(chunk).substring(0, 100) 
          : String(chunk).substring(0, 100);
        console.log(`- Preview: ${chunkPreview}${chunkPreview.length >= 100 ? '...' : ''}`);
        
        // Simulate the same validation as in routes.ts
        if (typeof chunk === 'object' && 'type' in chunk && chunk.type && 'data' in chunk) {
          // Skip log messages
          if (typeof chunk.data === 'string' && 
              (chunk.data.includes('Initialized new context') || 
               chunk.data.includes('Added search to context'))) {
            console.log('- Status: SKIPPED (log message)');
            continue;
          }
          
          const response = chunk as TypedChatResponse;
          
          // Prepare to send to client
          const sseData = `data: ${JSON.stringify(response)}\n\n`;
          console.log(`- Writing SSE data: ${sseData.substring(0, 100)}${sseData.length >= 100 ? '...' : ''}`);
          
          // Test if this can be properly parsed by client
          const parseResult = safeParseSSEData(sseData);
          if (parseResult.valid) {
            console.log('- Parse status: ✅ VALID JSON');
            validChunks++;
          } else {
            console.log(`- Parse status: ❌ INVALID JSON - ${parseResult.error?.message}`);
            invalidChunks++;
          }
          
          // Add to fake response
          res.write(sseData);
          responseSent = true;
        } else {
          console.log('- Status: INVALID CHUNK FORMAT (not a TypedChatResponse)');
          invalidChunks++;
        }
      } else {
        console.log('- Status: EMPTY CHUNK');
      }
    }
    
    // Test stats
    console.log('\n--- Chat Stream Stats ---');
    console.log(`Total chunks: ${chunkCount}`);
    console.log(`Valid chunks: ${validChunks}`);
    console.log(`Invalid chunks: ${invalidChunks}`);
    console.log(`Response sent: ${responseSent}`);
    
    // End the stream
    res.write("data: [DONE]\n\n");
    res.end();
    
    console.log('\nTest completed successfully');
    return { success: true, stats: { total: chunkCount, valid: validChunks, invalid: invalidChunks } };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error('\nTest failed with error:', errorMessage);
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
    
    // Log the error
    logError(requestId, 'Chat test error', {
      error: errorMessage,
      context: {
        messageLength: message.length
      }
    });
    
    return { 
      success: false, 
      error: errorMessage
    };
  }
}

/**
 * Run the test directly
 */
// ES Module syntax - just run the test immediately
testChatAPI()
  .then(result => {
    console.log('\nTest execution completed');
    console.log('Result:', JSON.stringify(result, null, 2));
    process.exit(result.success ? 0 : 1);
  })
  .catch(err => {
    console.error('Test execution failed:', err);
    process.exit(1);
  });