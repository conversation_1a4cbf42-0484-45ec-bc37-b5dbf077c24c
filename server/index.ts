import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import session from "express-session";
import MemoryStore from "memorystore";
import cors from 'cors';
import { refreshProperties } from './jobs/propertyRefresh.js';
import { createServer } from 'net';
import { runMigrations } from '../db/run-migrations';
import logger from './utils/logger';

const app = express();
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

const SessionStore = MemoryStore(session);

// Session middleware
app.use(
  session({
    cookie: { maxAge: 86400000 },
    store: new SessionStore({
      checkPeriod: 86400000, // prune expired entries every 24h
    }),
    resave: false,
    secret: "your-secret-key",
    saveUninitialized: false,
  })
);

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

// Function to find an available port
const findAvailablePort = (startPort: number, maxAttempts = 10): Promise<number> => {
  return new Promise((resolve, reject) => {
    let currentPort = startPort;
    let attempts = 0;

    const tryPort = () => {
      const server = createServer();

      server.once('error', (err: NodeJS.ErrnoException) => {
        if (err.code === 'EADDRINUSE') {
          server.close();
          if (attempts >= maxAttempts) {
            reject(new Error(`Could not find an available port after ${maxAttempts} attempts`));
            return;
          }
          attempts++;
          currentPort++;
          tryPort();
        } else {
          reject(err);
        }
      });

      server.once('listening', () => {
        server.close(() => {
          resolve(currentPort);
        });
      });

      server.listen(currentPort, '0.0.0.0');
    };

    tryPort();
  });
};

// Schedule daily property refresh at 3 AM UTC - Run after server starts
const schedulePropertyRefresh = () => {
  const now = new Date();
  const scheduledTime = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    3, // 3 AM UTC
    0,
    0
  );

  // If it's past 3 AM today, schedule for tomorrow
  if (now > scheduledTime) {
    scheduledTime.setDate(scheduledTime.getDate() + 1);
  }

  const timeUntilRefresh = scheduledTime.getTime() - now.getTime();

  setTimeout(async () => {
    try {
      await refreshProperties();
    } catch (error) {
      log(`Failed to run property refresh: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    schedulePropertyRefresh(); // Schedule next run
  }, timeUntilRefresh);

  log(`Property refresh scheduled for ${scheduledTime.toISOString()}`);
};

(async () => {
  try {
    // Run database migrations on startup
    // This ensures proper tables exist and initial admin user is created
    log('Running database migrations...');
    await runMigrations();
    log('Database migrations completed successfully');
  } catch (error) {
    log(`Failed to run database migrations: ${error instanceof Error ? error.message : 'Unknown error'}`);
    // Continue startup even if migrations fail to allow development to proceed
  }

  const server = registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    console.error('Error:', err);
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    
    // Log detailed error information
    log(`ERROR: ${message} (${status}) - ${err.stack || 'No stack trace'}`);

    res.status(status).json({ 
      message,
      timestamp: new Date().toISOString(),
      status
    });
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  try {
    // Use fixed ports instead of dynamic port finding to avoid port conflicts
    // Find available ports for both the server and inspector
    // const [serverPort, inspectorPort] = await Promise.all([
    //   findAvailablePort(5000),
    //   findAvailablePort(9229)
    // ]);
    
    const serverPort = 5000;
    const inspectorPort = 9229;

    // Start the server with the fixed port
    const startupOptions = {
      execArgv: [`--inspect=0.0.0.0:${inspectorPort}`]
    };

    server.listen(serverPort, "0.0.0.0", () => {
      log(`Server started successfully on port ${serverPort}`);
      log(`Inspector available on port ${inspectorPort}`);
      // Schedule property refresh after server starts
      schedulePropertyRefresh();
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
})().catch(error => {
  console.error('Server startup error:', error);
  process.exit(1);
});