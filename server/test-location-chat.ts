/**
 * Test utility for location-based chat functionality
 * This tests our geocoding integration and location link handling
 */

import { 
  handleChatStream, 
  addMessageToConversation, 
  getConversationContext,
  TypedChatResponse, 
  LocationResponse,
  TextResponse, 
  PropertiesResponse, 
  ActionResponse,
  ErrorResponse 
} from './services/openai';
import { contextService } from './services/contextService';
import { logError } from './utils/logger';
import { Request, Response } from 'express';

// Define the response type that includes location
type ExtendedChatResponse = TypedChatResponse | LocationResponse;

/**
 * Creates a fake request/response pair for testing
 */
function createMockReqRes() {
  const reqHeaders: Record<string, string> = {
    'user-agent': 'TestClient/1.0',
    'x-request-id': `test-${Date.now()}`
  };
  
  // Store response data for inspection
  const resData = {
    statusCode: 200,
    headers: {} as Record<string, string>,
    body: ''
  };
  
  const req = {
    headers: reqHeaders,
    body: {} as any
  } as Request;
  
  const res = {
    statusCode: 200,
    setHeader: (name: string, value: string) => {
      resData.headers[name] = value;
      return res;
    },
    status: (code: number) => {
      resData.statusCode = code;
      return res;
    },
    write: (data: string) => {
      resData.body += data;
      return res;
    },
    end: () => {
      return res;
    }
  } as unknown as Response;

  return { req, res, resData };
}

/**
 * Safely parse SSE data to detect non-JSON content
 */
function safeParseSSEData(rawData: string): { valid: boolean, parsed?: any, error?: Error, reason?: string } {
  // Check if it's an SSE format
  if (!rawData.startsWith('data: ')) {
    return { valid: false, error: new Error('Not SSE format') };
  }

  // Extract the data part 
  const data = rawData.slice(6).trim();
  
  // Skip if it's a DONE message
  if (data === '[DONE]' || data === 'DONE') {
    return { valid: true, parsed: { type: 'control', action: 'done' } };
  }

  try {
    // Skip non-JSON data like log messages
    if (data.includes('Initialize') || 
        data.includes('Added search') || 
        data.includes('Recorded property') || 
        data.includes('Updated filters') ||
        data.includes('Added property to comparison') ||
        data.includes('Recorded booking attempt') ||
        data.includes('Updated conversation context') ||
        data.includes('Added message')) {
      return { 
        valid: false, 
        reason: 'Status message',
        error: new Error(`Status message encountered: ${data.substring(0, 30)}...`) 
      };
    }

    // Try to detect if the data is not valid JSON before parsing
    if (!/^[\[\{]/.test(data.trim())) {
      return { 
        valid: false, 
        reason: 'Not JSON format',
        error: new Error(`Not valid JSON format: ${data.substring(0, 30)}...`) 
      };
    }

    const parsed = JSON.parse(data);
    return { valid: true, parsed };
  } catch (e) {
    return { 
      valid: false, 
      reason: 'Parse error',
      error: e as Error 
    };
  }
}

/**
 * Test the chat API with location-based queries
 */
export async function testLocationChat(message: string = "Tell me about hotels in Miami Beach") {
  console.log('=========== LOCATION CHAT TEST ===========');
  console.log(`Testing with message: "${message}"`);
  
  const { req, res, resData } = createMockReqRes();
  
  const sessionId = `test-session-${Date.now()}`;
  const requestId = `test-location-chat-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  
  try {
    // Setup test request
    req.body = { 
      message, 
      sessionId
    };
    
    // Set up SSE headers
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    
    // Get session context
    const sessionContext = contextService.getContext(sessionId);
    const conversation = getConversationContext(sessionId);
    
    // Add user message to conversation
    addMessageToConversation(sessionId, {
      role: 'user',
      content: message
    });
    
    // Prepare chat context
    const chatContext = {
      conversation: conversation.context,
      messages: conversation.messages,
      searchContext: sessionContext.searchContext,
      userPreferences: sessionContext.userPreferences
    };
    
    console.log('Chat context prepared:', JSON.stringify(chatContext, null, 2));
    
    // Process chat stream
    const chatStream = handleChatStream(message, chatContext, req);
    
    let responseSent = false;
    let chunkCount = 0;
    let validChunks = 0;
    let invalidChunks = 0;
    let locationChunks = 0;
    
    console.log('\nBeginning stream processing...');
    
    // Process each chunk and collect metrics
    for await (const chunk of chatStream) {
      chunkCount++;
      console.log(`\nChunk #${chunkCount}:`);
      
      if (chunk) {
        // Debug info about the chunk
        console.log(`- Type: ${typeof chunk}`);
        console.log(`- Has 'type' property: ${typeof chunk === 'object' && 'type' in chunk}`);
        console.log(`- Has 'data' property: ${typeof chunk === 'object' && 'data' in chunk}`);
        
        // For logging, convert to string safely
        const chunkPreview = typeof chunk === 'object' 
          ? JSON.stringify(chunk).substring(0, 100) 
          : String(chunk).substring(0, 100);
        console.log(`- Preview: ${chunkPreview}${chunkPreview.length >= 100 ? '...' : ''}`);
        
        // Simulate the same validation as in routes.ts
        if (typeof chunk === 'object' && 'type' in chunk && chunk.type && 'data' in chunk) {
          // Skip log messages
          if (typeof chunk.data === 'string' && 
              (chunk.data.includes('Initialized new context') || 
                chunk.data.includes('Added search to context'))) {
            console.log('- Status: SKIPPED (log message)');
            continue;
          }
          
          // Check for location data - safely cast for TypeScript
          const typedChunk = chunk as ExtendedChatResponse;
          if (typedChunk.type === 'location') {
            locationChunks++;
            console.log('- Status: FOUND LOCATION DATA ✅');
            console.log(`  Location: ${JSON.stringify(typedChunk.data)}`);
          }
          
          // Check for actions that might contain location data
          if (chunk.type === 'action' && chunk.data?.type === 'location') {
            locationChunks++;
            console.log('- Status: FOUND LOCATION ACTION ✅');
            console.log(`  Location action: ${JSON.stringify(chunk.data)}`);
          }
          
          // Prepare to send to client
          const sseData = `data: ${JSON.stringify(chunk)}\n\n`;
          console.log(`- Writing SSE data: ${sseData.substring(0, 100)}${sseData.length >= 100 ? '...' : ''}`);
          
          // Test if this can be properly parsed by client
          const parseResult = safeParseSSEData(sseData);
          if (parseResult.valid) {
            console.log('- Parse status: ✅ VALID JSON');
            validChunks++;
          } else {
            console.log(`- Parse status: ❌ INVALID JSON - ${parseResult.error?.message}`);
            invalidChunks++;
          }
          
          // Add to fake response
          res.write(sseData);
          responseSent = true;
        } else {
          console.log('- Status: INVALID CHUNK FORMAT (not a proper response)');
          invalidChunks++;
        }
      } else {
        console.log('- Status: EMPTY CHUNK');
      }
    }
    
    // Test stats
    console.log('\n--- Location Chat Stream Stats ---');
    console.log(`Total chunks: ${chunkCount}`);
    console.log(`Valid chunks: ${validChunks}`);
    console.log(`Invalid chunks: ${invalidChunks}`);
    console.log(`Location chunks: ${locationChunks}`);
    console.log(`Response sent: ${responseSent}`);
    
    // End the stream
    res.write("data: [DONE]\n\n");
    res.end();
    
    console.log('\nTest completed successfully');
    return { 
      success: true, 
      stats: { 
        total: chunkCount, 
        valid: validChunks, 
        invalid: invalidChunks,
        locations: locationChunks
      } 
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error('\nTest failed with error:', errorMessage);
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
    
    // Log the error
    logError(requestId, 'Location chat test error', {
      error: errorMessage,
      context: {
        messageLength: message.length
      }
    });
    
    return { 
      success: false, 
      error: errorMessage
    };
  }
}

// Run the test with different location queries
const testQueries = [
  "Tell me about hotels in Miami Beach",
  "What are good hotels in New York?",
  "I need a hotel in San Francisco"
];

async function runAllTests() {
  console.log('\n============== RUNNING MULTIPLE LOCATION TESTS ==============\n');
  const results = [];
  
  for (const query of testQueries) {
    console.log(`\n\n>>> TESTING: "${query}"\n`);
    try {
      const result = await testLocationChat(query);
      results.push({ query, ...result });
    } catch (err) {
      results.push({ 
        query, 
        success: false, 
        error: err instanceof Error ? err.message : String(err) 
      });
    }
  }
  
  console.log('\n============== LOCATION TEST SUMMARY ==============\n');
  for (const result of results) {
    console.log(`- "${result.query}": ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    if (result.success && result.stats) {
      console.log(`  Stats: ${result.stats.valid}/${result.stats.total} valid chunks, ${result.stats.locations} location chunks`);
    }
    if (!result.success && result.error) {
      console.log(`  Error: ${result.error}`);
    }
  }
  
  return {
    allSucceeded: results.every(r => r.success),
    results
  };
}

// ES Module - run tests directly
runAllTests()
  .then(summary => {
    console.log('\nAll tests completed.');
    process.exit(summary.allSucceeded ? 0 : 1);
  })
  .catch(err => {
    console.error('Test execution failed:', err);
    process.exit(1);
  });