import { describe, test, expect, jest, beforeEach, afterEach } from '@jest/globals';
import fetch from 'node-fetch';
import { TextResponse, LocationResponse, TypedChatResponse } from '../../services/openai';

// Test location queries and expected responses
const TEST_QUERIES = [
  {
    message: "I'm looking for hotels in Miami Beach",
    expectedLocation: "Miami Beach",
    shouldDetectLocation: true
  },
  {
    message: "What about properties near Central Park in New York?",
    expectedLocation: "Central Park",
    shouldDetectLocation: true
  },
  {
    message: "I want to stay in a 5-star hotel",
    expectedLocation: null,
    shouldDetectLocation: false
  },
  {
    message: "Are there any hotels in Tokyo with a view of Mount Fuji?",
    expectedLocation: "Tokyo",
    shouldDetectLocation: true
  },
  {
    message: "Do you have family-friendly resorts in Orlando?",
    expectedLocation: "Orlando",
    shouldDetectLocation: true
  }
];

/**
 * Parse SSE data from a chat response
 */
function parseSSEData(data: string): TypedChatResponse | null | { done: boolean } {
  if (data.trim() === 'data: [DONE]') {
    return { done: true };
  }

  if (!data.startsWith('data: ')) {
    return null;
  }

  const jsonString = data.slice(6);
  
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.error('Error parsing SSE data:', e);
    return null;
  }
}

/**
 * Determine if a response contains location data
 */
function isLocationResponse(response: TypedChatResponse): response is LocationResponse {
  return response.type === 'location';
}

/**
 * Test if a message generates a location response from the chat API
 */
async function testLocationDetection(message: string): Promise<{
  success: boolean;
  location?: { name: string; lat: number; lng: number; placeType?: string };
  error?: string;
  textResponses?: string[];
}> {
  const API_URL = 'http://localhost:5000/api/chat';
  const textResponses: string[] = [];
  
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, sessionId: `test-${Date.now()}` }),
    });

    if (!response.ok || !response.body) {
      return { 
        success: false, 
        error: `API request failed with status ${response.status}` 
      };
    }

    // Read the stream
    const reader = response.body.getReader();
    let locationData = null;
    let hasLocationResponse = false;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = new TextDecoder().decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (!line.trim()) continue;
        
        const parsed = parseSSEData(line);
        if (!parsed || 'done' in parsed) continue;

        if (isLocationResponse(parsed)) {
          locationData = parsed.data;
          hasLocationResponse = true;
        } else if (parsed.type === 'text') {
          textResponses.push(parsed.data);
        }
      }
    }

    return {
      success: hasLocationResponse,
      location: locationData,
      textResponses
    };
  } catch (error) {
    console.error('Error testing location detection:', error);
    return {
      success: false,
      error: `Error: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

// Skip this test in CI environments without a running server
describe.skip('Chat API Location Detection', () => {
  test.each(TEST_QUERIES)('should detect location in: "$message"', async ({ message, expectedLocation, shouldDetectLocation }) => {
    // This test requires the server to be running locally
    const result = await testLocationDetection(message);
    
    if (shouldDetectLocation) {
      expect(result.success).toBe(true);
      expect(result.location).toBeDefined();
      if (expectedLocation) {
        expect(result.location?.name).toContain(expectedLocation);
      }
      expect(result.location?.lat).toBeGreaterThan(-90);
      expect(result.location?.lat).toBeLessThan(90);
      expect(result.location?.lng).toBeGreaterThan(-180);
      expect(result.location?.lng).toBeLessThan(180);
    } else {
      // If location shouldn't be detected, either success should be false or location should be null
      if (result.success) {
        expect(result.location).toBeNull();
      }
    }
  }, 30000); // Increase timeout for AI processing
});