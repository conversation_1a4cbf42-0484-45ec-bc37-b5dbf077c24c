/**
 * Search Functionality Test Suite
 * 
 * This test suite focuses on testing the search functionality,
 * which is a critical user journey in the RoomLama platform.
 */

import { describe, test, expect, jest, beforeAll } from '@jest/globals';
import fetch from 'node-fetch';
import { Response } from 'node-fetch';

// Test configurations
const API_URL = 'http://localhost:5000/api';
const TIMEOUT = 15000; // 15 seconds timeout

// Helper function to validate property objects
function validateProperty(property: any): boolean {
  // Basic validation to ensure the property has the required fields
  const requiredFields = ['id', 'name', 'description', 'basePrice', 'currency', 'latitude', 'longitude'];
  return requiredFields.every(field => field in property && property[field] !== null && property[field] !== undefined);
}

/**
 * Search for properties with given parameters
 */
async function searchProperties(params: {
  location?: string;
  checkIn?: string;
  checkOut?: string;
  guests?: number;
  rooms?: number;
  filters?: Record<string, any>;
}): Promise<{
  success: boolean;
  properties?: any[];
  totalCount?: number;
  error?: string;
}> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params.location) queryParams.set('location', params.location);
    if (params.checkIn) queryParams.set('checkIn', params.checkIn);
    if (params.checkOut) queryParams.set('checkOut', params.checkOut);
    if (params.guests) queryParams.set('guests', params.guests.toString());
    if (params.rooms) queryParams.set('rooms', params.rooms.toString());
    
    // Add filters if provided
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          // Handle array values like amenities or property types
          value.forEach(item => queryParams.append(`${key}[]`, item));
        } else if (typeof value === 'object' && value !== null) {
          // Handle objects like price range
          Object.entries(value).forEach(([subKey, subValue]) => {
            queryParams.set(`${key}[${subKey}]`, subValue.toString());
          });
        } else {
          // Handle simple values
          queryParams.set(key, value.toString());
        }
      });
    }
    
    // Make the API request
    const url = `${API_URL}/properties/search?${queryParams.toString()}`;
    console.log(`Testing search API with URL: ${url}`);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    const data = await response.json();
    
    return {
      success: true,
      properties: data.properties,
      totalCount: data.totalCount
    };
  } catch (error) {
    console.error('Error in search properties test:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get detailed property information
 */
async function getPropertyDetails(propertyId: number): Promise<{
  success: boolean;
  property?: any;
  error?: string;
}> {
  try {
    const url = `${API_URL}/properties/${propertyId}/content`;
    console.log(`Testing property details API with URL: ${url}`);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    const property = await response.json();
    
    return {
      success: true,
      property
    };
  } catch (error) {
    console.error('Error in get property details test:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get property availability
 */
async function getPropertyAvailability(propertyId: number, checkIn: string, checkOut: string, guests: number = 2, rooms: number = 1): Promise<{
  success: boolean;
  availability?: any;
  error?: string;
}> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams({
      checkIn,
      checkOut,
      guests: guests.toString(),
      rooms: rooms.toString()
    });
    
    const url = `${API_URL}/properties/${propertyId}/availability?${queryParams.toString()}`;
    console.log(`Testing property availability API with URL: ${url}`);
    
    const response = await fetch(url);
    
    // Note: 404 or 500 might be valid responses if no availability exists
    // We'll consider this a "success" from a test perspective
    const data = await response.json();
    
    return {
      success: true,
      availability: data
    };
  } catch (error) {
    console.error('Error in get property availability test:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Skip in CI environments or when server is not running locally
describe.skip('Search Functionality Tests', () => {
  // Check if server is running before tests
  let serverRunning = false;
  
  beforeAll(async () => {
    try {
      const response = await fetch(`${API_URL}/config`);
      serverRunning = response.ok;
      console.log(`Server running: ${serverRunning}`);
    } catch (error) {
      console.warn('Server is not running, skipping tests');
      serverRunning = false;
    }
  });
  
  test('should search for properties with basic location parameter', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const location = 'Miami Beach';
    const result = await searchProperties({ location });
    
    expect(result.success).toBe(true);
    expect(result.properties).toBeDefined();
    expect(Array.isArray(result.properties)).toBe(true);
    expect(result.properties!.length).toBeGreaterThan(0);
    
    // Validate first property
    expect(validateProperty(result.properties![0])).toBe(true);
    
    // Ensure location is related to the search
    // (This might be approximate due to how the backend handles locations)
    const locationMatches = result.properties!.some(property => 
      property.city?.toLowerCase().includes('miami') || 
      property.address?.toLowerCase().includes('miami')
    );
    expect(locationMatches).toBe(true);
  }, TIMEOUT);
  
  test('should search with date and guest parameters', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const checkIn = tomorrow.toISOString().split('T')[0];
    
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    const checkOut = nextWeek.toISOString().split('T')[0];
    
    const result = await searchProperties({ 
      location: 'New York',
      checkIn,
      checkOut,
      guests: 2,
      rooms: 1
    });
    
    expect(result.success).toBe(true);
    expect(result.properties).toBeDefined();
    expect(Array.isArray(result.properties)).toBe(true);
    expect(result.properties!.length).toBeGreaterThan(0);
    
    // Check if the returned properties have the necessary fields
    for (const property of result.properties!.slice(0, 3)) {
      expect(validateProperty(property)).toBe(true);
    }
  }, TIMEOUT);
  
  test('should filter properties by price range', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await searchProperties({ 
      location: 'Los Angeles',
      filters: {
        priceRange: { min: 100, max: 300 }
      }
    });
    
    expect(result.success).toBe(true);
    expect(result.properties).toBeDefined();
    expect(Array.isArray(result.properties)).toBe(true);
    
    // Check if returned properties are within the price range
    for (const property of result.properties!) {
      expect(property.basePrice).toBeGreaterThanOrEqual(100);
      expect(property.basePrice).toBeLessThanOrEqual(300);
    }
  }, TIMEOUT);
  
  test('should filter properties by amenities', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const requestedAmenities = ['pool', 'wifi'];
    
    const result = await searchProperties({ 
      location: 'San Francisco',
      filters: {
        amenities: requestedAmenities
      }
    });
    
    expect(result.success).toBe(true);
    expect(result.properties).toBeDefined();
    expect(Array.isArray(result.properties)).toBe(true);
    
    // Check if returned properties have the requested amenities
    // Note: Depending on the backend implementation, this might be a partial match
    for (const property of result.properties!) {
      if (property.amenities && Array.isArray(property.amenities)) {
        const hasRequestedAmenities = requestedAmenities.some(amenity => 
          property.amenities.includes(amenity)
        );
        expect(hasRequestedAmenities).toBe(true);
      }
    }
  }, TIMEOUT);
  
  test('should get detailed property information', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // First get a list of properties
    const searchResult = await searchProperties({ location: 'Chicago' });
    expect(searchResult.success).toBe(true);
    expect(searchResult.properties!.length).toBeGreaterThan(0);
    
    // Get the first property ID
    const propertyId = searchResult.properties![0].id;
    
    // Get detailed information for that property
    const detailsResult = await getPropertyDetails(propertyId);
    
    expect(detailsResult.success).toBe(true);
    expect(detailsResult.property).toBeDefined();
    expect(detailsResult.property.id).toBe(propertyId);
    
    // Check for detailed fields that might not be in search results
    expect(detailsResult.property.description).toBeDefined();
    expect(detailsResult.property.amenities).toBeDefined();
    expect(Array.isArray(detailsResult.property.amenities)).toBe(true);
    expect(detailsResult.property.images).toBeDefined();
    expect(Array.isArray(detailsResult.property.images)).toBe(true);
  }, TIMEOUT);
  
  test('should check property availability', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // First get a list of properties
    const searchResult = await searchProperties({ location: 'Orlando' });
    expect(searchResult.success).toBe(true);
    expect(searchResult.properties!.length).toBeGreaterThan(0);
    
    // Get the first property ID
    const propertyId = searchResult.properties![0].id;
    
    // Set dates for availability check
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const checkIn = nextMonth.toISOString().split('T')[0];
    
    const checkout = new Date(nextMonth);
    checkout.setDate(checkout.getDate() + 3);
    const checkOut = checkout.toISOString().split('T')[0];
    
    // Check availability
    const availabilityResult = await getPropertyAvailability(
      propertyId,
      checkIn,
      checkOut,
      2,
      1
    );
    
    // The test is successful regardless of whether rooms are available
    // We're testing the API functionality, not actual availability
    expect(availabilityResult.success).toBe(true);
    expect(availabilityResult.availability).toBeDefined();
  }, TIMEOUT);
});