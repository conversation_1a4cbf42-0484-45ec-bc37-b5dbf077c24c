/**
 * AI Location Detection Test
 * 
 * This test specifically targets issues with the AI's ability to detect location data
 * in user messages and properly format responses.
 */

import { describe, test, expect, jest, beforeAll, afterAll } from '@jest/globals';
import fetch from 'node-fetch';
import * as streamParser from '../../utils/streamParser';

// Test configurations
const API_URL = 'http://localhost:5000/api/chat';
const TIMEOUT = 30000; // 30 seconds timeout for AI to process

// Different types of location query patterns to test
const TEST_CASES = [
  {
    description: 'Direct location query',
    message: 'I want to find hotels in Miami Beach',
    shouldDetectLocation: true,
    expectedLocation: 'Miami Beach'
  },
  {
    description: 'Location with landmark',
    message: 'Are there any hotels near Central Park in New York?',
    shouldDetectLocation: true,
    expectedLocation: 'Central Park'
  },
  {
    description: 'Ambiguous location references',
    message: 'Looking for beachfront properties with ocean views',
    shouldDetectLocation: false
  },
  {
    description: 'Multiple locations in query',
    message: 'I\'m traveling from San Francisco to Los Angeles, need hotels in LA',
    shouldDetectLocation: true,
    expectedLocation: 'Los Angeles'
  },
  {
    description: 'Misspelled location',
    message: 'Looking for hotels in Miamy Beech',
    shouldDetectLocation: true,
    expectedLocation: 'Miami Beach'
  },
  {
    description: 'Unusual formatting',
    message: 'HOTELS IN NYC PLEASE!!!',
    shouldDetectLocation: true,
    expectedLocation: 'NYC'
  },
  {
    description: 'Nested location in requirements',
    message: 'I need a luxury hotel with a spa that\'s in downtown Chicago',
    shouldDetectLocation: true,
    expectedLocation: 'Chicago'
  }
];

/**
 * Send a test message to the chat API and analyze the response stream
 */
async function testLocationDetection(message: string): Promise<{
  success: boolean;
  location?: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
  textResponses: string[];
  error?: string;
  locationDetected: boolean;
}> {
  const sessionId = `test-${Date.now()}`;
  const textResponses: string[] = [];
  let location = undefined;
  let locationDetected = false;
  
  try {
    console.log(`Testing message: "${message}"`);
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, sessionId }),
    });

    if (!response.ok || !response.body) {
      return { 
        success: false,
        locationDetected: false,
        textResponses: [],
        error: `API request failed with status ${response.status}` 
      };
    }

    // Create a reader for the response stream
    const reader = response.body.getReader();
    let done = false;

    while (!done) {
      const { done: streamDone, value } = await reader.read();
      
      if (streamDone) {
        done = true;
        continue;
      }

      // Process the chunk
      const chunk = new TextDecoder().decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (!line.trim()) continue;
        
        // Parse the SSE data
        const result = streamParser.safeParseSSEData(line);
        
        if (!result.valid || !result.parsed) continue;
        
        // Check if it's the done marker
        if (result.parsed.type === 'control' && result.parsed.action === 'done') {
          done = true;
          continue;
        }
        
        // Handle different response types
        if (result.parsed.type === 'location') {
          location = result.parsed.data;
          locationDetected = true;
          console.log('Location detected:', location);
        } else if (result.parsed.type === 'text') {
          textResponses.push(result.parsed.data);
        } else if (result.parsed.type === 'action' && 
                  result.parsed.data && 
                  result.parsed.data.type === 'location') {
          // Alternative location format
          location = result.parsed.data.data;
          locationDetected = true;
          console.log('Location action detected:', location);
        }
      }
    }

    return {
      success: true,
      location,
      textResponses,
      locationDetected
    };
    
  } catch (error) {
    console.error('Error in location detection test:', error);
    return {
      success: false,
      locationDetected: false,
      textResponses: [],
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Skip in CI environments or when server is not running locally
describe.skip('AI Location Detection Integration Tests', () => {
  // Validate server is running before running tests
  let serverRunning = false;
  
  beforeAll(async () => {
    try {
      const response = await fetch('http://localhost:5000/api/config');
      serverRunning = response.ok;
    } catch (error) {
      console.warn('Server is not running, skipping tests');
      serverRunning = false;
    }
  });
  
  test.each(TEST_CASES)('$description: "$message"', async ({ message, shouldDetectLocation, expectedLocation }) => {
    // Skip if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await testLocationDetection(message);
    
    // Test assertions
    expect(result.success).toBe(true);
    
    if (shouldDetectLocation) {
      expect(result.locationDetected).toBe(true);
      expect(result.location).toBeDefined();
      
      if (expectedLocation && result.location) {
        // Should contain the expected location name
        expect(result.location.name).toEqual(
          expect.stringContaining(expectedLocation)
        );
      }
      
      // Validate proper coordinates
      if (result.location) {
        expect(result.location.lat).toBeGreaterThan(-90);
        expect(result.location.lat).toBeLessThan(90);
        expect(result.location.lng).toBeGreaterThan(-180);
        expect(result.location.lng).toBeLessThan(180);
      }
    } else {
      // For queries that shouldn't detect location
      if (result.locationDetected) {
        console.warn(
          `Warning: Unexpected location detected: "${result.location?.name}" for query: "${message}"`
        );
      }
    }
    
    // Check if text responses were received
    expect(result.textResponses.length).toBeGreaterThan(0);
  }, TIMEOUT);
});