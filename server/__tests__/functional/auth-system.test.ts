/**
 * Authentication System Test Suite
 * 
 * This test suite validates the authentication system functionality
 * including user registration, login, password management, and access control.
 */

import { describe, test, expect, jest, beforeAll, afterAll } from '@jest/globals';
import fetch from 'node-fetch';

// Test configurations
const API_URL = 'http://localhost:5000/api';
const TIMEOUT = 10000; // 10 seconds timeout

// Helper function to generate a unique test user
function generateTestUser() {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000);
  
  return {
    email: `test.user.${timestamp}.${random}@example.com`,
    password: `TestPass${random}!`,
    firstName: 'Test',
    lastName: 'User'
  };
}

// Test users
const TEST_USER = generateTestUser();
const UPDATE_TEST_USER = generateTestUser();
const PASSWORD_TEST_USER = generateTestUser();

// Store auth tokens for cleanup
const authTokens: Record<string, string> = {};

/**
 * Register a new user
 */
async function registerUser(userData: {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}): Promise<{
  success: boolean;
  token?: string;
  user?: any;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || `Registration failed with status ${response.status}`
      };
    }

    // Store token for cleanup
    if (data.token) {
      authTokens[userData.email] = data.token;
    }

    return {
      success: true,
      token: data.token,
      user: data.user
    };
  } catch (error) {
    console.error('Error in register user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Login a user
 */
async function loginUser(credentials: {
  email: string;
  password: string;
}): Promise<{
  success: boolean;
  token?: string;
  user?: any;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || `Login failed with status ${response.status}`
      };
    }

    // Store token for cleanup
    if (data.token) {
      authTokens[credentials.email] = data.token;
    }

    return {
      success: true,
      token: data.token,
      user: data.user
    };
  } catch (error) {
    console.error('Error in login user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get current user profile
 */
async function getCurrentUser(token: string): Promise<{
  success: boolean;
  user?: any;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || `Get current user failed with status ${response.status}`
      };
    }

    return {
      success: true,
      user: data
    };
  } catch (error) {
    console.error('Error in get current user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Update user profile
 */
async function updateUserProfile(token: string, updates: any): Promise<{
  success: boolean;
  user?: any;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/users/profile`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updates),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || `Update profile failed with status ${response.status}`
      };
    }

    return {
      success: true,
      user: data
    };
  } catch (error) {
    console.error('Error in update user profile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Change user password
 */
async function changePassword(token: string, passwordData: {
  currentPassword: string;
  newPassword: string;
}): Promise<{
  success: boolean;
  message?: string;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/auth/change-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(passwordData),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || `Change password failed with status ${response.status}`
      };
    }

    return {
      success: true,
      message: data.message
    };
  } catch (error) {
    console.error('Error in change password:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Access a protected resource
 */
async function accessProtectedResource(token?: string): Promise<{
  success: boolean;
  authorized: boolean;
  data?: any;
  error?: string;
}> {
  try {
    // Try to access user bookings which should be protected
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(`${API_URL}/bookings`, {
      headers
    });

    // For this test, we consider 401/403 as expected results when unauthorized
    if (response.status === 401 || response.status === 403) {
      return {
        success: true,
        authorized: false,
        error: 'Unauthorized access, as expected'
      };
    }

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        authorized: false,
        error: data.message || `Access failed with status ${response.status}`
      };
    }

    return {
      success: true,
      authorized: true,
      data
    };
  } catch (error) {
    console.error('Error in access protected resource:', error);
    return {
      success: false,
      authorized: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Skip in CI environments or when server is not running locally
describe.skip('Authentication System Tests', () => {
  // Check if server is running before tests
  let serverRunning = false;
  
  beforeAll(async () => {
    try {
      const response = await fetch(`${API_URL}/config`);
      serverRunning = response.ok;
      console.log(`Server running: ${serverRunning}`);
    } catch (error) {
      console.warn('Server is not running, skipping tests');
      serverRunning = false;
    }
  });
  
  afterAll(async () => {
    // Cleanup could include deleting test users if the API supports it
    console.log('Test complete, cleanup if needed');
  });
  
  test('should register a new user', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await registerUser(TEST_USER);
    
    expect(result.success).toBe(true);
    expect(result.token).toBeDefined();
    expect(result.user).toBeDefined();
    expect(result.user.email).toBe(TEST_USER.email);
    expect(result.user.firstName).toBe(TEST_USER.firstName);
    expect(result.user.lastName).toBe(TEST_USER.lastName);
    
    // Password should not be returned
    expect(result.user.password).toBeUndefined();
  }, TIMEOUT);
  
  test('should reject duplicate email registration', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // Try to register again with the same email
    const result = await registerUser(TEST_USER);
    
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error).toContain('email');
  }, TIMEOUT);
  
  test('should successfully login a registered user', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await loginUser({
      email: TEST_USER.email,
      password: TEST_USER.password
    });
    
    expect(result.success).toBe(true);
    expect(result.token).toBeDefined();
    expect(result.user).toBeDefined();
    expect(result.user.email).toBe(TEST_USER.email);
  }, TIMEOUT);
  
  test('should reject login with incorrect password', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await loginUser({
      email: TEST_USER.email,
      password: 'WrongPassword123!'
    });
    
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  }, TIMEOUT);
  
  test('should retrieve authenticated user profile', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // First login to get a token
    const loginResult = await loginUser({
      email: TEST_USER.email,
      password: TEST_USER.password
    });
    
    expect(loginResult.success).toBe(true);
    
    // Then get user profile
    const result = await getCurrentUser(loginResult.token!);
    
    expect(result.success).toBe(true);
    expect(result.user).toBeDefined();
    expect(result.user.email).toBe(TEST_USER.email);
    expect(result.user.firstName).toBe(TEST_USER.firstName);
    expect(result.user.lastName).toBe(TEST_USER.lastName);
  }, TIMEOUT);
  
  test('should update user profile information', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // Register a new user for this test
    const registerResult = await registerUser(UPDATE_TEST_USER);
    expect(registerResult.success).toBe(true);
    
    // Update profile information
    const updatedInfo = {
      firstName: 'Updated',
      lastName: 'Name',
      phone: '+1234567890',
      preferences: {
        newsletterOptIn: true,
        currency: 'EUR'
      }
    };
    
    const result = await updateUserProfile(registerResult.token!, updatedInfo);
    
    expect(result.success).toBe(true);
    expect(result.user).toBeDefined();
    expect(result.user.firstName).toBe(updatedInfo.firstName);
    expect(result.user.lastName).toBe(updatedInfo.lastName);
    
    // Verify changes persisted by getting profile again
    const verificationResult = await getCurrentUser(registerResult.token!);
    
    expect(verificationResult.success).toBe(true);
    expect(verificationResult.user.firstName).toBe(updatedInfo.firstName);
    expect(verificationResult.user.lastName).toBe(updatedInfo.lastName);
  }, TIMEOUT);
  
  test('should change user password', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // Register a new user for this test
    const registerResult = await registerUser(PASSWORD_TEST_USER);
    expect(registerResult.success).toBe(true);
    
    // Change password
    const newPassword = 'NewPassword123!';
    const changeResult = await changePassword(registerResult.token!, {
      currentPassword: PASSWORD_TEST_USER.password,
      newPassword
    });
    
    expect(changeResult.success).toBe(true);
    
    // Try to login with the old password (should fail)
    const oldPasswordLogin = await loginUser({
      email: PASSWORD_TEST_USER.email,
      password: PASSWORD_TEST_USER.password
    });
    
    expect(oldPasswordLogin.success).toBe(false);
    
    // Try to login with the new password (should succeed)
    const newPasswordLogin = await loginUser({
      email: PASSWORD_TEST_USER.email,
      password: newPassword
    });
    
    expect(newPasswordLogin.success).toBe(true);
    expect(newPasswordLogin.token).toBeDefined();
  }, TIMEOUT);
  
  test('should restrict access to protected resources', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // Try to access protected resource without a token
    const unauthenticatedResult = await accessProtectedResource();
    
    expect(unauthenticatedResult.success).toBe(true); // Test was successful
    expect(unauthenticatedResult.authorized).toBe(false); // Access was denied as expected
    
    // Login to get a valid token
    const loginResult = await loginUser({
      email: TEST_USER.email,
      password: TEST_USER.password
    });
    
    expect(loginResult.success).toBe(true);
    
    // Try to access protected resource with a valid token
    const authenticatedResult = await accessProtectedResource(loginResult.token);
    
    expect(authenticatedResult.success).toBe(true);
    expect(authenticatedResult.authorized).toBe(true);
  }, TIMEOUT);
  
  test('should reject invalid or expired tokens', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // Try to access protected resource with an invalid token
    const invalidTokenResult = await accessProtectedResource('invalid.token.string');
    
    expect(invalidTokenResult.success).toBe(true); // Test was successful
    expect(invalidTokenResult.authorized).toBe(false); // Access was denied as expected
  }, TIMEOUT);
});