/**
 * AI Property Recommendation Test
 * 
 * This test focuses on the AI's ability to recommend properties based on
 * specific user preferences and produce well-formatted property data.
 */

import { describe, test, expect, jest, beforeAll } from '@jest/globals';
import fetch from 'node-fetch';
import * as streamParser from '../../utils/streamParser';
import { Property } from '../../db/schema';

// Test configurations
const API_URL = 'http://localhost:5000/api/chat';
const TIMEOUT = 60000; // 60 seconds timeout for AI to process and recommend properties

// Test cases for property recommendations
const TEST_CASES = [
  {
    description: 'Basic property recommendation',
    message: 'Can you recommend a hotel in Miami Beach for next weekend?',
    shouldRecommendProperties: true,
    minimumProperties: 1,
    locationContext: 'Miami Beach'
  },
  {
    description: 'Specific amenities recommendation',
    message: 'I need a hotel in New York with a pool and free breakfast',
    shouldRecommendProperties: true,
    minimumProperties: 1,
    requiredAmenities: ['pool', 'breakfast'],
    locationContext: 'New York'
  },
  {
    description: 'Budget constraint recommendation',
    message: 'Find me affordable hotels under $150 per night in Chicago',
    shouldRecommendProperties: true,
    minimumProperties: 1,
    maxPrice: 150,
    locationContext: 'Chicago'
  },
  {
    description: 'Family-friendly properties',
    message: 'Looking for family-friendly resorts in Orlando near theme parks',
    shouldRecommendProperties: true,
    minimumProperties: 1,
    locationContext: 'Orlando'
  },
  {
    description: 'Luxury properties with specific features',
    message: 'Show me luxury beachfront hotels with spa services in Cancun',
    shouldRecommendProperties: true,
    minimumProperties: 1,
    requiredAmenities: ['spa', 'beach'],
    locationContext: 'Cancun'
  },
  {
    description: 'Properties with multiple specific constraints',
    message: 'I need a 4-star hotel in San Francisco with parking, gym, and room service under $300',
    shouldRecommendProperties: true,
    minimumProperties: 1,
    requiredAmenities: ['parking', 'gym', 'room service'],
    maxPrice: 300,
    minRating: 4,
    locationContext: 'San Francisco'
  }
];

/**
 * Send a test message to the chat API and analyze the property recommendations
 */
async function testPropertyRecommendations(message: string): Promise<{
  success: boolean;
  properties: Property[];
  textResponses: string[];
  error?: string;
  propertiesRecommended: boolean;
  location?: {
    name: string;
    lat: number;
    lng: number;
  };
}> {
  const sessionId = `test-${Date.now()}`;
  const textResponses: string[] = [];
  let properties: Property[] = [];
  let propertiesRecommended = false;
  let location = undefined;
  
  try {
    console.log(`Testing recommendation message: "${message}"`);
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, sessionId }),
    });

    if (!response.ok || !response.body) {
      return { 
        success: false,
        propertiesRecommended: false,
        properties: [],
        textResponses: [],
        error: `API request failed with status ${response.status}` 
      };
    }

    // Create a reader for the response stream
    const reader = response.body.getReader();
    let done = false;

    while (!done) {
      const { done: streamDone, value } = await reader.read();
      
      if (streamDone) {
        done = true;
        continue;
      }

      // Process the chunk
      const chunk = new TextDecoder().decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (!line.trim()) continue;
        
        // Parse the SSE data
        const result = streamParser.safeParseSSEData(line);
        
        if (!result.valid || !result.parsed) continue;
        
        // Check if it's the done marker
        if (result.parsed.type === 'control' && result.parsed.action === 'done') {
          done = true;
          continue;
        }
        
        // Handle different response types
        if (result.parsed.type === 'properties') {
          properties = result.parsed.data;
          propertiesRecommended = true;
          console.log(`Properties recommended: ${properties.length}`);
        } else if (result.parsed.type === 'text') {
          textResponses.push(result.parsed.data);
        } else if (result.parsed.type === 'location') {
          location = result.parsed.data;
        }
      }
    }

    return {
      success: true,
      properties,
      textResponses,
      propertiesRecommended,
      location
    };
    
  } catch (error) {
    console.error('Error in property recommendation test:', error);
    return {
      success: false,
      propertiesRecommended: false,
      properties: [],
      textResponses: [],
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Validate a property has all required fields and structure
 */
function validatePropertyStructure(property: Property): {
  valid: boolean;
  missingFields: string[];
} {
  const requiredFields = [
    'id', 'name', 'description', 'latitude', 'longitude', 
    'address', 'city', 'country', 'basePrice', 'currency'
  ];
  
  const missingFields = requiredFields.filter(field => 
    !(field in property) || property[field as keyof Property] === undefined ||
    property[field as keyof Property] === null || property[field as keyof Property] === ''
  );
  
  return {
    valid: missingFields.length === 0,
    missingFields
  };
}

// Skip in CI environments or when server is not running locally
describe.skip('AI Property Recommendation Integration Tests', () => {
  // Validate server is running before running tests
  let serverRunning = false;
  
  beforeAll(async () => {
    try {
      const response = await fetch('http://localhost:5000/api/config');
      serverRunning = response.ok;
    } catch (error) {
      console.warn('Server is not running, skipping tests');
      serverRunning = false;
    }
  });
  
  test.each(TEST_CASES)('$description: "$message"', async ({ 
    message, 
    shouldRecommendProperties, 
    minimumProperties,
    requiredAmenities,
    maxPrice,
    minRating,
    locationContext
  }) => {
    // Skip if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await testPropertyRecommendations(message);
    
    // Test assertions
    expect(result.success).toBe(true);
    
    if (shouldRecommendProperties) {
      expect(result.propertiesRecommended).toBe(true);
      
      if (minimumProperties) {
        expect(result.properties.length).toBeGreaterThanOrEqual(minimumProperties);
      }
      
      // Check if location context was provided
      if (locationContext && result.location) {
        expect(result.location.name.toLowerCase()).toContain(
          locationContext.toLowerCase()
        );
      }
      
      // Validate each property structure
      result.properties.forEach((property, index) => {
        const validation = validatePropertyStructure(property);
        expect(validation.valid).toBe(true, 
          `Property at index ${index} is missing fields: ${validation.missingFields.join(', ')}`
        );
        
        // Check price constraints if specified
        if (maxPrice && property.basePrice) {
          expect(property.basePrice).toBeLessThanOrEqual(maxPrice);
        }
        
        // Check rating constraints if specified
        if (minRating && property.rating) {
          expect(property.rating).toBeGreaterThanOrEqual(minRating);
        }
        
        // Check required amenities if specified
        if (requiredAmenities && property.amenities) {
          requiredAmenities.forEach(amenity => {
            // Check if any of the property's amenities contain the required amenity
            // This handles cases where the amenity might be worded slightly differently
            const hasAmenity = property.amenities.some(
              propAmenity => propAmenity.toLowerCase().includes(amenity.toLowerCase())
            );
            
            if (!hasAmenity) {
              console.warn(
                `Warning: Property "${property.name}" is missing required amenity "${amenity}"`
              );
            }
          });
        }
      });
    } else {
      // For queries that shouldn't recommend properties
      if (result.propertiesRecommended) {
        console.warn(
          `Warning: Unexpected properties recommended for query: "${message}"`
        );
      }
    }
    
    // Check if text responses were received
    expect(result.textResponses.length).toBeGreaterThan(0);
  }, TIMEOUT);
});