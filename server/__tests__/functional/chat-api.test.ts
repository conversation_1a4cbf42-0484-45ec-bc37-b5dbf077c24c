/**
 * Functional test for the Chat API
 * 
 * This test suite validates the complete chat API functionality including:
 * - Location detection in queries
 * - Property recommendations
 * - Session context maintenance
 * - Response formatting
 */

import { describe, test, expect, jest, beforeEach, afterEach, beforeAll } from '@jest/globals';
import fetch from 'node-fetch';
import { Response } from 'node-fetch';
import { safeParseSSEData } from '../../utils/streamParser';
import { TextResponse, LocationResponse, PropertiesResponse, TypedChatResponse } from '../../services/openai';
import { getStreamReader } from '../test-utils';

// Test configurations
const API_URL = 'http://localhost:5000/api/chat';
const TIMEOUT = 30000; // 30 seconds timeout for AI processing

// Test case scenarios that cover different aspects of the conversation
interface TestCase {
  description: string;
  message: string;
  expectLocation?: boolean;
  expectProperties?: boolean;
  expectedLocationName?: string;
  followUpMessage?: string; // For testing conversation context
}

const TEST_CASES: TestCase[] = [
  {
    description: 'Basic location query',
    message: 'I want to find hotels in Miami Beach',
    expectLocation: true,
    expectProperties: true,
    expectedLocationName: 'Miami Beach'
  },
  {
    description: 'Location with specific amenities',
    message: 'Looking for hotels with a pool in Los Angeles',
    expectLocation: true,
    expectProperties: true,
    expectedLocationName: 'Los Angeles'
  },
  {
    description: 'Query with specific dates',
    message: 'I need a room in New York from April 25 to April 30',
    expectLocation: true,
    expectProperties: true,
    expectedLocationName: 'New York'
  },
  {
    description: 'Ambiguous query with implicit city mention',
    message: 'What are the best areas to stay in downtown Chicago?',
    expectLocation: true,
    expectedLocationName: 'Chicago'
  },
  {
    description: 'Query with complex location reference',
    message: 'I want to stay near the Golden Gate Bridge in San Francisco',
    expectLocation: true,
    expectProperties: true,
    expectedLocationName: 'San Francisco'
  },
  {
    description: 'Multi-part conversation with context',
    message: 'I\'m planning a trip to Seattle next month',
    expectLocation: true,
    expectedLocationName: 'Seattle',
    followUpMessage: 'What areas would you recommend for a family with kids?'
  }
];

/**
 * Process a chat API response stream
 * This function handles the SSE data parsing and extraction of information
 */
async function processChatStream(response: Response): Promise<{
  textResponses: string[];
  location?: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
  recommendedProperties?: number[];
  allResponses: TypedChatResponse[];
}> {
  const textResponses: string[] = [];
  const allResponses: TypedChatResponse[] = [];
  let location = undefined;
  let recommendedProperties: number[] = [];
  
  if (!response.ok || !response.body) {
    throw new Error(`API request failed with status ${response.status}`);
  }
  
  // Use the getStreamReader utility to handle type compatibility issues
  const reader = getStreamReader(response.body);
  let done = false;
  let chunks = '';
  
  while (!done) {
    const { done: streamDone, value } = await reader.read();
    
    if (streamDone) {
      done = true;
      continue;
    }
    
    // Process this chunk
    const chunk = new TextDecoder().decode(value);
    chunks += chunk;
    
    // Split by double newlines which typically separate SSE events
    const events = chunks.split('\n\n');
    
    // All but the last event should be processed
    // The last one might be incomplete
    for (let i = 0; i < events.length - 1; i++) {
      const event = events[i];
      if (!event.trim()) continue;
      
      // Process each line in the event
      const lines = event.split('\n');
      for (const line of lines) {
        if (!line.trim() || !line.startsWith('data:')) continue;
        
        const result = safeParseSSEData(line);
        
        if (result.valid && result.parsed) {
          allResponses.push(result.parsed as TypedChatResponse);
          
          // Check for done marker
          if (result.parsed.type === 'control' && result.parsed.action === 'done') {
            done = true;
            break;
          }
          
          // Handle different response types
          if (result.parsed.type === 'text') {
            textResponses.push((result.parsed as TextResponse).data);
          } else if (result.parsed.type === 'location') {
            location = (result.parsed as LocationResponse).data;
          } else if (result.parsed.type === 'properties') {
            const propertiesResponse = result.parsed as PropertiesResponse;
            recommendedProperties = propertiesResponse.data
              .map(property => typeof property === 'number' ? property : property.id);
          } else if (result.parsed.type === 'action' && 
                   result.parsed.data && 
                   result.parsed.data.type === 'location') {
            // Alternative location format
            location = result.parsed.data.data;
          } else if (result.parsed.type === 'action' && 
                   result.parsed.data && 
                   result.parsed.data.type === 'property_list') {
            // Property list in action format
            recommendedProperties = result.parsed.data.data
              .map((property: any) => typeof property === 'number' ? property : property.id);
          }
        }
      }
    }
    
    // Keep the last potentially incomplete event for the next iteration
    chunks = events[events.length - 1];
  }
  
  return {
    textResponses,
    location,
    recommendedProperties,
    allResponses
  };
}

/**
 * Send a message to the chat API and process the response
 */
async function sendChatMessage(message: string, sessionId?: string): Promise<{
  success: boolean;
  textResponses: string[];
  location?: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
  recommendedProperties?: number[];
  allResponses: TypedChatResponse[];
  error?: string;
  sessionId: string;
}> {
  try {
    // Generate a session ID if not provided
    const chatSessionId = sessionId || `test-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    console.log(`Testing message: "${message}" (SessionID: ${chatSessionId})`);
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        sessionId: chatSessionId,
        extractLocation: true, // Important flag to ensure location detection
      }),
    });
    
    const result = await processChatStream(response);
    
    return {
      success: true,
      ...result,
      sessionId: chatSessionId
    };
  } catch (error) {
    console.error('Error in chat API test:', error);
    return {
      success: false,
      textResponses: [],
      allResponses: [],
      error: error instanceof Error ? error.message : String(error),
      sessionId: sessionId || ''
    };
  }
}

// Skip in CI environments or when server is not running locally
describe.skip('Chat API Functional Tests', () => {
  // Check if server is running before tests
  let serverRunning = false;
  
  beforeAll(async () => {
    try {
      // Check if the server is running by making a simple request
      const response = await fetch('http://localhost:5000/api/config');
      serverRunning = response.ok;
      console.log(`Server running: ${serverRunning}`);
    } catch (error) {
      console.warn('Server is not running, skipping tests');
      serverRunning = false;
    }
  });
  
  // Run each test case as a separate test
  test.each(TEST_CASES)('$description: "$message"', async (testCase) => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await sendChatMessage(testCase.message);
    
    // General test assertions
    expect(result.success).toBe(true);
    expect(result.textResponses.length).toBeGreaterThan(0);
    
    // Location detection assertions
    if (testCase.expectLocation) {
      expect(result.location).toBeDefined();
      
      if (result.location && testCase.expectedLocationName) {
        // Expect location name to contain the expected string (case insensitive)
        expect(result.location.name.toLowerCase())
          .toContain(testCase.expectedLocationName.toLowerCase());
      }
      
      // Validate proper coordinates for detected locations
      if (result.location) {
        expect(result.location.lat).toBeGreaterThan(-90);
        expect(result.location.lat).toBeLessThan(90);
        expect(result.location.lng).toBeGreaterThan(-180);
        expect(result.location.lng).toBeLessThan(180);
      }
    }
    
    // Property recommendation assertions
    if (testCase.expectProperties) {
      // Either we explicitly get properties or they're mentioned in text
      if (result.recommendedProperties && result.recommendedProperties.length > 0) {
        expect(result.recommendedProperties.length).toBeGreaterThan(0);
        
        // Validate property IDs are numbers
        result.recommendedProperties.forEach(id => {
          expect(typeof id).toBe('number');
        });
      } else {
        // If no explicit property recommendations, text should mention hotels or accommodations
        const propertiesInText = result.textResponses.some(text => 
          /hotel|accommodation|property|room|stay|booking/i.test(text)
        );
        expect(propertiesInText).toBe(true);
      }
    }
    
    // Test conversation context with follow-up message
    if (testCase.followUpMessage) {
      // Send a follow-up message using the same session ID
      const followUpResult = await sendChatMessage(testCase.followUpMessage, result.sessionId);
      
      expect(followUpResult.success).toBe(true);
      expect(followUpResult.textResponses.length).toBeGreaterThan(0);
      
      // Check that context is maintained by looking for location mentions
      // Or explicit preservation of context
      const locationMentioned = followUpResult.textResponses.some(text => 
        testCase.expectedLocationName && text.includes(testCase.expectedLocationName)
      );
      
      const contextPreserved = followUpResult.textResponses.some(text => 
        /previously mentioned|you mentioned|in .* as you asked|referring to/i.test(text)
      );
      
      expect(locationMentioned || contextPreserved).toBe(true);
    }
  }, TIMEOUT);
  
  // Additional test for conversation context across multiple messages
  test('maintains conversation context across multiple interactions', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // Create a unique session for this conversation
    const sessionId = `test-conversation-${Date.now()}`;
    
    // First message: Set location
    const firstResult = await sendChatMessage('I want to visit Paris next month', sessionId);
    expect(firstResult.success).toBe(true);
    expect(firstResult.location?.name.toLowerCase()).toContain('paris');
    
    // Second message: Add date preferences
    const secondResult = await sendChatMessage('I\'d like to stay from June 15 to June 20', sessionId);
    expect(secondResult.success).toBe(true);
    
    // Paris should still be in context
    const parisStillInContext = secondResult.textResponses.some(text => 
      text.toLowerCase().includes('paris')
    );
    expect(parisStillInContext).toBe(true);
    
    // Third message: Ask for specific amenities
    const thirdResult = await sendChatMessage('Can you recommend hotels with a view of the Eiffel Tower?', sessionId);
    expect(thirdResult.success).toBe(true);
    
    // Should maintain both Paris and dates in context
    const fullContextPreserved = thirdResult.textResponses.some(text => 
      text.toLowerCase().includes('paris') && 
      (text.includes('June') || text.includes('15') || text.includes('20'))
    );
    
    expect(fullContextPreserved).toBe(true);
  }, TIMEOUT);
  
  // Test specific edge cases
  test('handles multiple locations in a query', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await sendChatMessage('I\'m traveling from Boston to Chicago, need hotels in Chicago');
    
    expect(result.success).toBe(true);
    expect(result.location).toBeDefined();
    
    // Should prioritize Chicago as the destination
    if (result.location) {
      expect(result.location.name.toLowerCase()).toContain('chicago');
    }
  }, TIMEOUT);
  
  test('handles misspelled location names', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    const result = await sendChatMessage('Looking for hotels in San Fransisco');
    
    expect(result.success).toBe(true);
    expect(result.location).toBeDefined();
    
    // Should correct to "San Francisco"
    if (result.location) {
      expect(result.location.name.toLowerCase()).toContain('francisco');
    }
  }, TIMEOUT);
});