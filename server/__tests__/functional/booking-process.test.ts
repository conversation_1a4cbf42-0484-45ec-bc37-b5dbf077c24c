/**
 * Booking Process Test Suite
 * 
 * This test suite validates the complete booking process flow,
 * which is one of the most critical user journeys in the RoomLama platform.
 */

import { describe, test, expect, jest, beforeAll, beforeEach } from '@jest/globals';
import fetch from 'node-fetch';
import { Response } from 'node-fetch';

// Test configurations
const API_URL = 'http://localhost:5000/api';
const TIMEOUT = 15000; // 15 seconds timeout

// Mock user data for testing
const TEST_USER = {
  email: `test.user.${Date.now()}@example.com`,
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User'
};

// Helper function to register a test user
async function registerTestUser(): Promise<{
  success: boolean;
  token?: string;
  userId?: number;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_USER.email,
        password: TEST_USER.password,
        firstName: TEST_USER.firstName,
        lastName: TEST_USER.lastName
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `Registration failed with status ${response.status}`);
    }

    return {
      success: true,
      token: data.token,
      userId: data.user?.id
    };
  } catch (error) {
    console.error('Error in register test user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Helper function to login a test user
async function loginTestUser(): Promise<{
  success: boolean;
  token?: string;
  userId?: number;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_USER.email,
        password: TEST_USER.password
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `Login failed with status ${response.status}`);
    }

    return {
      success: true,
      token: data.token,
      userId: data.user.id
    };
  } catch (error) {
    console.error('Error in login test user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Helper function to get a property with availability
async function findPropertyWithAvailability(): Promise<{
  success: boolean;
  propertyId?: number;
  checkIn?: string;
  checkOut?: string;
  rateId?: string | number;
  error?: string;
}> {
  try {
    // Get a list of properties
    const searchResponse = await fetch(`${API_URL}/properties/search?location=Miami`);
    
    if (!searchResponse.ok) {
      throw new Error(`Property search failed with status ${searchResponse.status}`);
    }
    
    const searchData = await searchResponse.json();
    
    if (!searchData.properties || searchData.properties.length === 0) {
      throw new Error('No properties found in search results');
    }
    
    // Set dates for availability check (1 month from now)
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const checkIn = nextMonth.toISOString().split('T')[0];
    
    const checkout = new Date(nextMonth);
    checkout.setDate(checkout.getDate() + 3);
    const checkOut = checkout.toISOString().split('T')[0];
    
    // Check availability for each property until we find one with available rates
    for (const property of searchData.properties.slice(0, 5)) { // Try first 5 properties
      try {
        const propertyId = property.id;
        const queryParams = new URLSearchParams({
          checkIn,
          checkOut,
          guests: '2',
          rooms: '1'
        });
        
        const availabilityResponse = await fetch(
          `${API_URL}/properties/${propertyId}/availability?${queryParams.toString()}`
        );
        
        // Some properties might not have availability
        if (!availabilityResponse.ok) continue;
        
        const availabilityData = await availabilityResponse.json();
        
        // Check if there are available rate plans
        if (availabilityData.ratePlans && Object.keys(availabilityData.ratePlans).length > 0) {
          // Get the first available rate plan
          const rateId = Object.keys(availabilityData.ratePlans)[0];
          
          return {
            success: true,
            propertyId,
            checkIn,
            checkOut,
            rateId
          };
        }
      } catch (error) {
        // Skip this property and try the next one
        console.warn(`Skipping property ${property.id} due to error:`, error);
      }
    }
    
    throw new Error('No properties with availability found');
  } catch (error) {
    console.error('Error finding property with availability:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Helper function to initiate a booking
async function initiateBooking(
  token: string, 
  propertyId: number, 
  checkIn: string, 
  checkOut: string,
  rateId: string | number
): Promise<{
  success: boolean;
  bookingId?: string;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/bookings/initiate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        propertyId,
        checkIn,
        checkOut,
        guests: 2,
        rooms: 1,
        rateId,
        guestDetails: {
          firstName: TEST_USER.firstName,
          lastName: TEST_USER.lastName,
          email: TEST_USER.email,
          phone: '+1234567890'
        }
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `Booking initiation failed with status ${response.status}`);
    }

    return {
      success: true,
      bookingId: data.bookingId
    };
  } catch (error) {
    console.error('Error in initiate booking:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Helper function to add payment details to a booking
// Note: In test mode, we use test payment details
async function addPaymentDetails(
  token: string, 
  bookingId: string
): Promise<{
  success: boolean;
  status?: string;
  error?: string;
}> {
  try {
    // This would typically involve Stripe or another payment processor
    // For testing, we use a simplified mock approach
    const response = await fetch(`${API_URL}/bookings/${bookingId}/payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        paymentMethod: 'test_card',
        paymentToken: 'pm_card_visa', // Stripe test token
        savePaymentMethod: false
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `Payment failed with status ${response.status}`);
    }

    return {
      success: true,
      status: data.status
    };
  } catch (error) {
    console.error('Error in add payment details:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Helper function to confirm a booking
async function confirmBooking(
  token: string, 
  bookingId: string
): Promise<{
  success: boolean;
  reservationId?: string;
  status?: string;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/bookings/${bookingId}/confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `Booking confirmation failed with status ${response.status}`);
    }

    return {
      success: true,
      reservationId: data.reservationId,
      status: data.status
    };
  } catch (error) {
    console.error('Error in confirm booking:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Helper function to get booking details
async function getBookingDetails(
  token: string, 
  bookingId: string
): Promise<{
  success: boolean;
  booking?: any;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_URL}/bookings/${bookingId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `Get booking details failed with status ${response.status}`);
    }

    return {
      success: true,
      booking: data
    };
  } catch (error) {
    console.error('Error in get booking details:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// Skip in CI environments or when server is not running locally
describe.skip('Booking Process Tests', () => {
  // Check if server is running before tests
  let serverRunning = false;
  let testUserToken: string;
  
  beforeAll(async () => {
    try {
      const response = await fetch(`${API_URL}/config`);
      serverRunning = response.ok;
      console.log(`Server running: ${serverRunning}`);
      
      if (serverRunning) {
        // Register a test user for all booking tests
        const registration = await registerTestUser();
        
        if (registration.success) {
          testUserToken = registration.token!;
        } else {
          // Try to log in in case the user already exists
          const login = await loginTestUser();
          
          if (login.success) {
            testUserToken = login.token!;
          } else {
            console.warn('Failed to create or log in test user:', login.error);
          }
        }
      }
    } catch (error) {
      console.warn('Server is not running, skipping tests');
      serverRunning = false;
    }
  });
  
  test('should complete a full booking flow', async () => {
    // Skip test if server is not running
    if (!serverRunning) {
      console.warn('Skipping test because server is not running');
      return;
    }
    
    // Skip if test user authentication failed
    if (!testUserToken) {
      console.warn('Skipping test because test user authentication failed');
      return;
    }
    
    // Step 1: Find a property with availability
    const propertyResult = await findPropertyWithAvailability();
    expect(propertyResult.success).toBe(true);
    
    if (!propertyResult.success) {
      console.warn('Skipping remaining test because no property with availability was found');
      return;
    }
    
    const { propertyId, checkIn, checkOut, rateId } = propertyResult;
    
    // Step 2: Initiate a booking
    const bookingResult = await initiateBooking(
      testUserToken,
      propertyId!,
      checkIn!,
      checkOut!,
      rateId!
    );
    
    expect(bookingResult.success).toBe(true);
    expect(bookingResult.bookingId).toBeDefined();
    
    if (!bookingResult.success) {
      console.warn('Skipping remaining test because booking initiation failed');
      return;
    }
    
    const bookingId = bookingResult.bookingId!;
    
    // Step 3: Add payment details
    const paymentResult = await addPaymentDetails(testUserToken, bookingId);
    
    expect(paymentResult.success).toBe(true);
    
    if (!paymentResult.success) {
      console.warn('Skipping remaining test because payment failed');
      return;
    }
    
    // Step 4: Confirm the booking
    const confirmationResult = await confirmBooking(testUserToken, bookingId);
    
    expect(confirmationResult.success).toBe(true);
    expect(confirmationResult.reservationId).toBeDefined();
    expect(confirmationResult.status).toBe('confirmed');
    
    if (!confirmationResult.success) {
      console.warn('Booking confirmation failed');
      return;
    }
    
    // Step 5: Verify the booking details
    const bookingDetailsResult = await getBookingDetails(testUserToken, bookingId);
    
    expect(bookingDetailsResult.success).toBe(true);
    expect(bookingDetailsResult.booking).toBeDefined();
    expect(bookingDetailsResult.booking.propertyId).toBe(propertyId);
    expect(bookingDetailsResult.booking.checkIn).toBe(checkIn);
    expect(bookingDetailsResult.booking.checkOut).toBe(checkOut);
    expect(bookingDetailsResult.booking.status).toBe('confirmed');
  }, TIMEOUT * 2); // Double timeout since this is a multi-step process
});