/**
 * Test Utilities
 * 
 * Common utility functions and type helpers to simplify test writing
 * and fix common TypeScript issues.
 */

/**
 * Type assertion helper for API responses
 * Use this to safely type unknown data from API responses
 */
export function assertType<T>(data: unknown): T {
  return data as T;
}

/**
 * Helper for working with API responses that return unknown data
 */
export function parseApiResponse<T>(response: unknown): T {
  return response as T;
}

/**
 * Type-safe data extraction from unknown fetch response
 */
export async function extractJsonData<T>(response: Response): Promise<T> {
  const data = await response.json();
  return data as T;
}

/**
 * Type-safe conversion for ReadableStream
 * (Fixes compatibility issues between node-fetch and standard fetch types)
 */
export function getStreamReader(body: any): ReadableStreamDefaultReader<Uint8Array> {
  if (body && typeof body.getReader === 'function') {
    return body.getReader();
  }
  
  throw new Error('Response body is not a valid ReadableStream');
}

/**
 * Basic location data interface for tests
 */
export interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
}

/**
 * Basic property data interface for tests
 */
export interface PropertyData {
  id: number;
  name: string;
  description?: string;
  basePrice?: number;
  currency?: string;
  latitude?: number;
  longitude?: number;
  [key: string]: any;
}

/**
 * Create a typed mock for fetch
 */
export function createTypedFetchMock<T>(responseData: T) {
  return jest.fn().mockResolvedValue({
    ok: true,
    status: 200,
    json: async () => responseData,
    headers: {
      get: (name: string) => name === 'content-type' ? 'application/json' : null
    }
  });
}

/**
 * Create a typed mock for a failed fetch
 */
export function createErrorFetchMock(status: number = 500, errorMessage: string = 'Server error') {
  return jest.fn().mockResolvedValue({
    ok: false,
    status,
    statusText: errorMessage,
    json: async () => ({ error: errorMessage })
  });
}

/**
 * Wait for a specified time (useful for tests that need to wait for async operations)
 */
export function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Create a mock for EventSource for stream testing
 */
export class MockEventSource {
  onmessage: ((event: { data: string }) => void) | null = null;
  onerror: ((error: Event) => void) | null = null;
  
  constructor(url: string, mockResponses: Array<{ data: string, delay: number }> = []) {
    setTimeout(() => {
      this.processResponses(mockResponses);
    }, 10);
  }
  
  close(): void {
    // Mock close method
  }
  
  private processResponses(responses: Array<{ data: string, delay: number }>): void {
    let cumulativeDelay = 0;
    
    responses.forEach(response => {
      cumulativeDelay += response.delay;
      
      setTimeout(() => {
        if (this.onmessage) {
          this.onmessage({ data: response.data });
        }
      }, cumulativeDelay);
    });
  }
}