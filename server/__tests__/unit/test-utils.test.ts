import { describe, test, expect } from '@jest/globals';
import { 
  assertType, 
  parseApiResponse 
} from '../test-utils';

describe('Test Utilities', () => {
  test('assertType should correctly type cast unknown data', () => {
    const untyped: unknown = { id: 1, name: 'Test' };
    const typed = assertType<{ id: number, name: string }>(untyped);
    
    expect(typed.id).toBe(1);
    expect(typed.name).toBe('Test');
  });
  
  test('parseApiResponse should correctly type cast responses', () => {
    const response: unknown = { data: [{ id: 1 }, { id: 2 }] };
    const parsed = parseApiResponse<{ data: Array<{ id: number }> }>(response);
    
    expect(parsed.data.length).toBe(2);
    expect(parsed.data[0].id).toBe(1);
  });
});