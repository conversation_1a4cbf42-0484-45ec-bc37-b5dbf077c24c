import { describe, test, expect, jest, beforeEach, afterEach } from '@jest/globals';
import OpenAI from 'openai';
import { 
  enhancePropertySearch, 
  summarizeConversation, 
  needsSummarization,
  getConversationContext,
  addMessageToConversation
} from '../../services/openai';
import { contextService } from '../../services/contextService';
import { ConversationContext, ChatMessage } from '../../services/openai';
import type { Property } from '@db/schema';

// Mock OpenAI
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => {
    return {
      chat: {
        completions: {
          create: jest.fn()
        }
      },
      models: {
        list: jest.fn().mockResolvedValue({ data: [{ id: 'test-model' }] })
      }
    };
  });
});

// Mock contextService
jest.mock('../../services/contextService', () => ({
  contextService: {
    getContext: jest.fn(),
    addMessage: jest.fn(),
    updateConversationContext: jest.fn()
  }
}));

const MockOpenAI = OpenAI as jest.MockedClass<typeof OpenAI>;

describe('OpenAI Service', () => {
  let mockProperties: Property[];
  let mockChatMessages: ChatMessage[];
  let mockConversationContext: ConversationContext;
  
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup mock data
    mockProperties = [
      {
        id: 1,
        name: 'Beachfront Resort',
        description: 'Luxury beachfront resort with ocean views',
        basePrice: 250,
        currency: 'USD',
        amenities: ['pool', 'spa', 'restaurant'],
        images: ['image1.jpg', 'image2.jpg'],
        checkIn: '15:00',
        checkOut: '11:00',
        rating: 4.7,
        reviewCount: 230,
        latitude: 25.7617,
        longitude: -80.1918,
        address: '123 Ocean Drive',
        city: 'Miami Beach',
        state: 'FL',
        country: 'USA',
        postalCode: '33139',
        propertyType: 'resort',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 2,
        name: 'Downtown Boutique Hotel',
        description: 'Stylish boutique hotel in the heart of downtown',
        basePrice: 180,
        currency: 'USD',
        amenities: ['wifi', 'breakfast', 'bar'],
        images: ['image3.jpg', 'image4.jpg'],
        checkIn: '14:00',
        checkOut: '12:00',
        rating: 4.5,
        reviewCount: 150,
        latitude: 25.7743,
        longitude: -80.1937,
        address: '456 Downtown Blvd',
        city: 'Miami',
        state: 'FL',
        country: 'USA',
        postalCode: '33131',
        propertyType: 'hotel',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    mockChatMessages = [
      {
        role: 'user',
        content: 'I want to find a hotel in Miami Beach',
        timestamp: Date.now() - 10000
      },
      {
        role: 'assistant',
        content: 'I can help you find hotels in Miami Beach. What dates are you planning to stay?',
        timestamp: Date.now() - 8000
      },
      {
        role: 'user',
        content: 'Next week from Monday to Friday',
        timestamp: Date.now() - 5000
      }
    ];
    
    mockConversationContext = {
      summary: 'User is looking for a hotel in Miami Beach for next week.',
      location: {
        name: 'Miami Beach',
        lat: 25.7617,
        lng: -80.1918
      },
      lastSummarizedAt: Date.now() - 60000,
      messageCount: 2
    };
    
    // Mock openAI chat completions response
    const mockOpenAIInstance = MockOpenAI.mock.instances[0];
    
    // @ts-ignore - Jest mock typing issue
    mockOpenAIInstance.chat.completions.create.mockImplementation((params) => {
      if (params.messages[0].role === 'system' && params.messages[0].content.includes('You are RoomLamAI')) {
        // This is the enhancePropertySearch call
        return Promise.resolve({
          choices: [{
            message: {
              content: JSON.stringify({
                properties: [1, 2],
                explanation: 'Here are some beachfront properties in Miami Beach.'
              })
            }
          }]
        });
      } else if (params.messages[0].role === 'system' && params.messages[0].content.includes('conversation summarizer')) {
        // This is the summarizeConversation call
        return Promise.resolve({
          choices: [{
            message: {
              content: JSON.stringify({
                summary: 'User wants to book a hotel in Miami Beach for Monday to Friday next week.',
                location: {
                  name: 'Miami Beach',
                  lat: 25.7617,
                  lng: -80.1918
                },
                dateRange: {
                  checkIn: '2025-04-22',
                  checkOut: '2025-04-26'
                },
                preferences: {
                  amenities: ['beach access', 'pool'],
                  propertyTypes: ['hotel', 'resort'],
                  priceRange: null,
                  guestCount: null
                }
              })
            }
          }]
        });
      }
      
      // Default response
      return Promise.resolve({
        choices: [{
          message: {
            content: JSON.stringify({})
          }
        }]
      });
    });
    
    // Mock contextService responses
    // @ts-ignore - Jest mock typing issue
    contextService.getContext.mockImplementation((sessionId) => {
      return {
        conversation: mockConversationContext,
        messages: mockChatMessages,
        lastUpdated: Date.now()
      };
    });
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  describe('enhancePropertySearch', () => {
    test('should return property recommendations based on user query', async () => {
      const query = 'I want a beachfront hotel in Miami';
      
      const result = await enhancePropertySearch(query, mockProperties);
      
      expect(result.properties).toHaveLength(2);
      expect(result.properties[0].id).toBe(1);
      expect(result.properties[1].id).toBe(2);
      expect(result.explanation).toBe('Here are some beachfront properties in Miami Beach.');
      
      const mockOpenAIInstance = MockOpenAI.mock.instances[0];
      // @ts-ignore - Jest mock typing issue
      expect(mockOpenAIInstance.chat.completions.create).toHaveBeenCalledTimes(1);
    });
    
    test('should handle empty properties array', async () => {
      const query = 'I want a beachfront hotel in Miami';
      
      const result = await enhancePropertySearch(query, []);
      
      expect(result.properties).toHaveLength(0);
      expect(result.explanation).toBe('Here are some beachfront properties in Miami Beach.');
    });
  });
  
  describe('summarizeConversation', () => {
    test('should summarize conversation and update context', async () => {
      const result = await summarizeConversation(mockChatMessages, mockConversationContext);
      
      expect(result.summary).toBe('User wants to book a hotel in Miami Beach for Monday to Friday next week.');
      expect(result.location?.name).toBe('Miami Beach');
      expect(result.dateRange?.checkIn).toBe('2025-04-22');
      expect(result.dateRange?.checkOut).toBe('2025-04-26');
      expect(result.preferences?.amenities).toContain('beach access');
      expect(result.preferences?.propertyTypes).toContain('hotel');
      
      const mockOpenAIInstance = MockOpenAI.mock.instances[0];
      // @ts-ignore - Jest mock typing issue
      expect(mockOpenAIInstance.chat.completions.create).toHaveBeenCalledTimes(1);
    });
    
    test('should skip summarization for too few messages', async () => {
      const result = await summarizeConversation(
        mockChatMessages.slice(0, 1), 
        mockConversationContext
      );
      
      // Should keep existing context but update timestamps
      expect(result.summary).toBe(mockConversationContext.summary);
      expect(result.lastSummarizedAt).toBeGreaterThan(mockConversationContext.lastSummarizedAt);
      
      const mockOpenAIInstance = MockOpenAI.mock.instances[0];
      // @ts-ignore - Jest mock typing issue
      expect(mockOpenAIInstance.chat.completions.create).not.toHaveBeenCalled();
    });
  });
  
  describe('needsSummarization', () => {
    test('should determine if summarization is needed based on message count threshold', () => {
      // Current context has 2 messages, adding 5 more would exceed the threshold
      const result = needsSummarization(mockConversationContext, 7);
      expect(result).toBe(true);
    });
    
    test('should determine if summarization is needed based on time threshold', () => {
      // Simulate context that was last summarized more than 5 minutes ago
      const oldContext = {
        ...mockConversationContext,
        lastSummarizedAt: Date.now() - (6 * 60 * 1000)
      };
      
      const result = needsSummarization(oldContext, 3);
      expect(result).toBe(true);
    });
    
    test('should not need summarization if thresholds are not exceeded', () => {
      // Current context has 2 messages, adding 1 more would not exceed the threshold
      // and the last summarization was just done
      const recentContext = {
        ...mockConversationContext,
        lastSummarizedAt: Date.now() - 1000
      };
      
      const result = needsSummarization(recentContext, 3);
      expect(result).toBe(false);
    });
  });
  
  describe('getConversationContext', () => {
    test('should retrieve conversation context for a session', () => {
      const result = getConversationContext('test-session-id');
      
      expect(result.context).toEqual(mockConversationContext);
      expect(result.messages).toEqual(mockChatMessages);
      
      // @ts-ignore - Jest mock typing issue
      expect(contextService.getContext).toHaveBeenCalledWith('test-session-id');
    });
  });
  
  describe('addMessageToConversation', () => {
    test('should add a message to the conversation context', () => {
      const newMessage = {
        role: 'user',
        content: 'I prefer a hotel with a pool'
      };
      
      addMessageToConversation('test-session-id', newMessage);
      
      // @ts-ignore - Jest mock typing issue
      expect(contextService.addMessage).toHaveBeenCalledWith('test-session-id', {
        ...newMessage,
        timestamp: expect.any(Number)
      });
    });
  });
});