import { describe, test, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { Request, Response } from 'express';
import { contextService } from '../../services/contextService';
import { TypedChatResponse, TextResponse, LocationResponse } from '../../services/openai';

// Mock dependencies
jest.mock('../../services/contextService', () => ({
  contextService: {
    getContext: jest.fn(),
    addMessage: jest.fn(),
    updateConversationContext: jest.fn()
  }
}));

// Mock AI client
const mockAIClient = {
  chat: {
    completions: {
      create: jest.fn()
    }
  }
};

// Mock the AI response stream
function mockAIStream(messages: string[] | { type: string; data: any }[]) {
  const mockStream = {
    [Symbol.asyncIterator]: async function* () {
      for (const message of messages) {
        if (typeof message === 'string') {
          yield { choices: [{ delta: { content: message } }] };
        } else {
          // JSON format
          yield { choices: [{ delta: { content: JSON.stringify(message) } }] };
        }
      }
    }
  };
  
  return mockStream;
}

// Create mock request and response objects
function createMockRequestResponse() {
  const req = {
    body: {
      message: '',
      sessionId: 'test-session-id'
    }
  } as unknown as Request;

  const resWrite = jest.fn();
  const resEnd = jest.fn();
  
  const res = {
    setHeader: jest.fn(),
    status: jest.fn().mockReturnThis(),
    write: resWrite,
    end: resEnd,
    flush: jest.fn()
  } as unknown as Response;
  
  return { req, res, resWrite, resEnd };
}

describe('Chat API', () => {
  let originalFetch: typeof global.fetch;
  
  beforeEach(() => {
    originalFetch = global.fetch;
    global.fetch = jest.fn();
    jest.clearAllMocks();
    
    // Mock contextService
    (contextService.getContext as jest.Mock).mockReturnValue({
      sessionId: 'test-session-id',
      conversation: { 
        summary: 'Test conversation', 
        lastSummarizedAt: Date.now(),
        messageCount: 0
      },
      messages: [],
      searchContext: {
        recentSearches: [],
        viewedProperties: [],
        comparedProperties: [],
        bookingAttempts: [],
        filters: {}
      },
      lastUpdated: Date.now()
    });
  });
  
  afterEach(() => {
    global.fetch = originalFetch;
  });

  describe('handleLocationDetection', () => {
    test('should detect location in user messages', async () => {
      const { req, res, resWrite } = createMockRequestResponse();
      req.body.message = "I'm looking for hotels in Miami Beach";
      
      // Mock the geocoding API
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [{
            geometry: {
              location: {
                lat: 25.7907,
                lng: -80.1300
              }
            },
            formatted_address: 'Miami Beach, FL, USA',
            types: ['locality', 'political']
          }]
        })
      });
      
      // Mock our AI client to return location data
      (mockAIClient.chat.completions.create as jest.Mock).mockResolvedValueOnce(
        mockAIStream([
          { type: 'text', data: 'I can help you find hotels in Miami Beach.' },
          { 
            type: 'location', 
            data: {
              name: 'Miami Beach',
              lat: 25.7907,
              lng: -80.1300,
              placeType: 'locality'
            }
          }
        ])
      );
      
      // Import the route handler (we just want to test the logic, not express)
      const chatHandler = jest.fn(async (req: Request, res: Response) => {
        // Basic implementation of what the handler would do
        try {
          const message = req.body.message;
          const sessionId = req.body.sessionId || 'default-session';
          
          // Set up SSE response
          res.setHeader('Content-Type', 'text/event-stream');
          res.setHeader('Cache-Control', 'no-cache');
          res.setHeader('Connection', 'keep-alive');
          
          // Get context
          const context = contextService.getContext(sessionId);
          
          // Simulate a response with location data 
          const locationResponse: LocationResponse = {
            type: 'location',
            data: {
              name: 'Miami Beach',
              lat: 25.7907,
              lng: -80.1300,
              placeType: 'locality'
            }
          };
          
          // Send location response first
          res.write(`data: ${JSON.stringify(locationResponse)}\n\n`);
          
          // Then send a text response
          const textResponse: TextResponse = {
            type: 'text',
            data: "I can help you find hotels in Miami Beach. It's a beautiful coastal resort city in Florida."
          };
          
          res.write(`data: ${JSON.stringify(textResponse)}\n\n`);
          
          // End the stream
          res.write('data: [DONE]\n\n');
          res.end();
          
        } catch (error) {
          console.error('Error in chat handler:', error);
          res.status(500).end();
        }
      });
      
      // Call the handler
      await chatHandler(req, res);
      
      // First call should be the location data
      expect(resWrite).toHaveBeenCalledTimes(3);
      const firstCallData = resWrite.mock.calls[0][0];
      expect(firstCallData).toContain('type":"location"');
      expect(firstCallData).toContain('Miami Beach');
      expect(firstCallData).toContain('25.7907');
      expect(firstCallData).toContain('-80.1300');
      
      // Second call should be text response
      const secondCallData = resWrite.mock.calls[1][0];
      expect(secondCallData).toContain('type":"text"');
    });
  });
});