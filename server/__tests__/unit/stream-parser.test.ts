import { describe, test, expect, jest } from '@jest/globals';
import { safeParseSSEData, processStreamChunk } from '../../utils/streamParser';
import { TypedChatResponse } from '../../services/openai';

describe('Stream Parser', () => {
  describe('safeParseSSEData', () => {
    test('should parse valid JSON data', () => {
      const validJson = 'data: {"type":"text","data":"Hello, how can I help you?"}\n\n';
      const result = safeParseSSEData(validJson);
      
      expect(result.valid).toBe(true);
      expect(result.parsed).toEqual({
        type: 'text',
        data: 'Hello, how can I help you?'
      });
    });

    test('should handle the DONE marker', () => {
      const doneMarker = 'data: [DONE]\n\n';
      const result = safeParseSSEData(doneMarker);
      
      expect(result.valid).toBe(true);
      expect(result.parsed).toEqual({ 
        type: 'control', 
        action: 'done' 
      });
    });

    test('should handle invalid JSON', () => {
      const invalidJson = 'data: {"type":"text","data":Hello world}\n\n';
      const result = safeParseSSEData(invalidJson);
      
      expect(result.valid).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should handle non-JSON format', () => {
      const nonJsonFormat = 'data: This is just plain text\n\n';
      const result = safeParseSSEData(nonJsonFormat);
      
      expect(result.valid).toBe(false);
      expect(result.invalidReason).toContain('Not JSON format');
    });

    test('should handle empty data', () => {
      const emptyData = 'data: \n\n';
      const result = safeParseSSEData(emptyData);
      
      expect(result.valid).toBe(false);
      expect(result.invalidReason).toBe('Empty data');
    });

    test('should detect unbalanced JSON structure', () => {
      const unbalancedJson = 'data: {"type":"text","data":"unclosed string}\n\n';
      const result = safeParseSSEData(unbalancedJson);
      
      expect(result.valid).toBe(false);
      expect(result.invalidReason).toBe('Unbalanced JSON structure');
    });
  });

  describe('processStreamChunk', () => {
    test('should process a valid chunk with a text response', () => {
      const chunk = 'data: {"type":"text","data":"Hello world"}\n\n';
      const result = processStreamChunk(chunk);
      
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'text',
        data: 'Hello world'
      });
    });

    test('should process a valid chunk with a location response', () => {
      const chunk = 'data: {"type":"location","data":{"name":"New York","lat":40.7128,"lng":-74.0060}}\n\n';
      const result = processStreamChunk(chunk);
      
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'location',
        data: {
          name: 'New York',
          lat: 40.7128,
          lng: -74.0060
        }
      });
    });

    test('should handle a done marker', () => {
      const chunk = 'data: [DONE]\n\n';
      const result = processStreamChunk(chunk);
      
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'control',
        action: 'done'
      });
    });

    test('should handle invalid data', () => {
      const chunk = 'data: {"type":text,"data""Hello world"}\n\n';
      const result = processStreamChunk(chunk);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });

    test('should handle missing required properties', () => {
      const chunk = 'data: {"message":"Hello world"}\n\n';
      const result = processStreamChunk(chunk);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Missing required properties (type, data)');
    });
  });
});