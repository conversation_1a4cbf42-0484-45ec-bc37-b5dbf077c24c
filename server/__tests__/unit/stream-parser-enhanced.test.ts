/**
 * Enhanced Stream Parser Tests
 * 
 * This test suite provides comprehensive testing for the stream parser functionality
 * with a focus on handling complex scenarios and edge cases.
 */

import { describe, test, expect, jest } from '@jest/globals';
import { safeParseSSEData, processStreamChunk } from '../../utils/streamParser';
import { TypedChatResponse, LocationResponse, TextResponse, PropertiesResponse, ActionResponse } from '../../services/openai';

describe('Enhanced Stream Parser', () => {
  describe('safeParseSSEData with complex payloads', () => {
    test('should parse location data with complex nested structure', () => {
      const locationData = `data: {"type":"location","data":{"name":"San Francisco near Fisherman's Wharf","lat":37.8080,"lng":-122.4177,"placeType":"point_of_interest","context":{"district":"Fisherman's Wharf","city":"San Francisco","state":"California","country":"USA"}}}\n\n`;
      
      const result = safeParseSSEData(locationData);
      
      expect(result.valid).toBe(true);
      expect(result.parsed).toEqual({
        type: 'location',
        data: {
          name: "San Francisco near Fisherman's Wharf",
          lat: 37.8080,
          lng: -122.4177,
          placeType: "point_of_interest",
          context: {
            district: "Fisherman's Wharf",
            city: "San Francisco",
            state: "California",
            country: "USA"
          }
        }
      });
    });
    
    test('should parse property recommendations with extended data', () => {
      const propertiesData = `data: {"type":"properties","data":[{"id":1234,"name":"Golden Gate Hotel","description":"Luxury hotel with Bay views","basePrice":299,"currency":"USD","rating":4.8},{"id":5678,"name":"Fisherman's Retreat","description":"Cozy hotel near the pier","basePrice":199,"currency":"USD","rating":4.5}]}\n\n`;
      
      const result = safeParseSSEData(propertiesData);
      
      expect(result.valid).toBe(true);
      expect(result.parsed).toEqual({
        type: 'properties',
        data: [
          {
            id: 1234,
            name: "Golden Gate Hotel",
            description: "Luxury hotel with Bay views",
            basePrice: 299,
            currency: "USD",
            rating: 4.8
          },
          {
            id: 5678,
            name: "Fisherman's Retreat",
            description: "Cozy hotel near the pier",
            basePrice: 199,
            currency: "USD",
            rating: 4.5
          }
        ]
      });
    });
    
    test('should parse action data with complex action structures', () => {
      const actionData = `data: {"type":"action","data":{"type":"filter_properties","label":"Filter Properties","data":{"amenities":["pool","spa","gym"],"propertyTypes":["hotel","resort"],"priceRange":[150,300],"minRating":4}}}\n\n`;
      
      const result = safeParseSSEData(actionData);
      
      expect(result.valid).toBe(true);
      expect(result.parsed).toEqual({
        type: 'action',
        data: {
          type: "filter_properties",
          label: "Filter Properties",
          data: {
            amenities: ["pool", "spa", "gym"],
            propertyTypes: ["hotel", "resort"],
            priceRange: [150, 300],
            minRating: 4
          }
        }
      });
    });
    
    test('should parse multi-line text response', () => {
      const multilineText = `data: {"type":"text","data":"Here are some great options in San Francisco:\\n\\n1. The Golden Gate Hotel - Located near the bridge\\n2. Fisherman's Retreat - Perfect for seafood lovers\\n3. Downtown Luxury - In the heart of the city"}\n\n`;
      
      const result = safeParseSSEData(multilineText);
      
      expect(result.valid).toBe(true);
      expect(result.parsed).toEqual({
        type: 'text',
        data: "Here are some great options in San Francisco:\n\n1. The Golden Gate Hotel - Located near the bridge\n2. Fisherman's Retreat - Perfect for seafood lovers\n3. Downtown Luxury - In the heart of the city"
      });
    });
    
    test('should handle JSON with special characters and unicode', () => {
      const specialCharsJson = `data: {"type":"text","data":"✈️ Let's find you a great hotel in París! The city has amazing cafés & restaurants. Enjoy your séjour! 🇫🇷"}\n\n`;
      
      const result = safeParseSSEData(specialCharsJson);
      
      expect(result.valid).toBe(true);
      expect(result.parsed).toEqual({
        type: 'text',
        data: "✈️ Let's find you a great hotel in París! The city has amazing cafés & restaurants. Enjoy your séjour! 🇫🇷"
      });
    });
    
    test('should detect and handle truncated JSON', () => {
      const truncatedJson = `data: {"type":"text","data":"This message is cut off mid-sen`;
      
      const result = safeParseSSEData(truncatedJson);
      
      expect(result.valid).toBe(false);
      expect(result.invalidReason).toBe('Unbalanced JSON structure');
    });
  });
  
  describe('processStreamChunk with type validation', () => {
    test('should validate and process location responses', () => {
      const locationChunk = `data: {"type":"location","data":{"name":"Miami Beach","lat":25.7907,"lng":-80.1300}}\n\n`;
      
      const result = processStreamChunk(locationChunk);
      
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'location',
        data: {
          name: 'Miami Beach',
          lat: 25.7907,
          lng: -80.1300
        }
      });
      
      // Type validation test
      const locationResponse = result.chunk as LocationResponse;
      expect(locationResponse.type).toBe('location');
      expect(typeof locationResponse.data.name).toBe('string');
      expect(typeof locationResponse.data.lat).toBe('number');
      expect(typeof locationResponse.data.lng).toBe('number');
    });
    
    test('should validate and process text responses', () => {
      const textChunk = `data: {"type":"text","data":"Here are some great hotel options for your stay."}\n\n`;
      
      const result = processStreamChunk(textChunk);
      
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'text',
        data: 'Here are some great hotel options for your stay.'
      });
      
      // Type validation test
      const textResponse = result.chunk as TextResponse;
      expect(textResponse.type).toBe('text');
      expect(typeof textResponse.data).toBe('string');
    });
    
    test('should validate and process property responses', () => {
      const propertiesChunk = `data: {"type":"properties","data":[{"id":1234,"name":"Beach Resort"}]}\n\n`;
      
      const result = processStreamChunk(propertiesChunk);
      
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'properties',
        data: [
          {
            id: 1234,
            name: 'Beach Resort'
          }
        ]
      });
      
      // Type validation test
      const propertiesResponse = result.chunk as PropertiesResponse;
      expect(propertiesResponse.type).toBe('properties');
      expect(Array.isArray(propertiesResponse.data)).toBe(true);
      expect(propertiesResponse.data[0].id).toBe(1234);
    });
    
    test('should validate and process action responses', () => {
      const actionChunk = `data: {"type":"action","data":{"type":"map_view","label":"View on Map","data":{"lat":25.7907,"lng":-80.1300,"zoom":12}}}\n\n`;
      
      const result = processStreamChunk(actionChunk);
      
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'action',
        data: {
          type: 'map_view',
          label: 'View on Map',
          data: {
            lat: 25.7907,
            lng: -80.1300,
            zoom: 12
          }
        }
      });
      
      // Type validation test
      const actionResponse = result.chunk as ActionResponse;
      expect(actionResponse.type).toBe('action');
      expect(actionResponse.data.type).toBe('map_view');
      expect(actionResponse.data.label).toBe('View on Map');
      expect(typeof actionResponse.data.data).toBe('object');
    });
    
    test('should identify missing required data fields', () => {
      // Missing data field in location response
      const invalidLocationChunk = `data: {"type":"location"}\n\n`;
      
      const result = processStreamChunk(invalidLocationChunk);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Missing required properties (type, data)');
    });
    
    test('should identify invalid response types', () => {
      // Type that doesn't exist in our type definition
      const invalidTypeChunk = `data: {"type":"unknown_type","data":"This is an unknown type"}\n\n`;
      
      const result = processStreamChunk(invalidTypeChunk);
      
      // Should still parse but identify as an unknown type
      expect(result.isValid).toBe(true);
      expect(result.chunk).toEqual({
        type: 'unknown_type',
        data: 'This is an unknown type'
      });
      
      // Real-world validation would reject this as not matching TypedChatResponse
      const isValidResponseType = ['text', 'properties', 'location', 'action', 'error'].includes(result.chunk.type);
      expect(isValidResponseType).toBe(false);
    });
    
    test('should handle chunked responses in sequence', () => {
      // Simulate multiple chunks being processed in sequence
      const chunks = [
        `data: {"type":"text","data":"I'm searching for hotels in "}\n\n`,
        `data: {"type":"location","data":{"name":"Miami Beach","lat":25.7907,"lng":-80.1300}}\n\n`,
        `data: {"type":"text","data":"I found several great options."}\n\n`,
        `data: {"type":"properties","data":[{"id":1234,"name":"Beach Resort"},{"id":5678,"name":"Ocean View Hotel"}]}\n\n`,
        `data: [DONE]\n\n`
      ];
      
      const results = chunks.map(chunk => processStreamChunk(chunk));
      
      // All chunks should be valid
      results.forEach((result, index) => {
        expect(result.isValid).toBe(true);
        
        if (index === chunks.length - 1) {
          // Last chunk should be DONE marker
          expect(result.chunk.type).toBe('control');
          expect(result.chunk.action).toBe('done');
        } else {
          // Check type matches expected pattern
          const expectedTypes = ['text', 'location', 'text', 'properties'];
          expect(result.chunk.type).toBe(expectedTypes[index]);
        }
      });
    });
  });
});