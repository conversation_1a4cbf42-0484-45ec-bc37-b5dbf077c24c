/**
 * Location detection test script with TypeScript
 * This script tests the chat API's ability to detect and process location data
 */

import fetch from 'node-fetch';
import { 
  TypedChatResponse, 
  LocationResponse, 
  TextResponse,
  ActionResponse
} from './services/openai';

// Test messages with location requests
const TEST_MESSAGES: string[] = [
  "I want to find hotels in Miami Beach",
  "Are there any good places to stay in New York City?",
  "I'm looking for accommodation near the Eiffel Tower in Paris",
  "Show me hotels in San Francisco near Fisherman's Wharf",
  "I need a place to stay in Tokyo"
];

// Location data interface for storing test results
interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
}

interface TestResult {
  success: boolean;
  location?: LocationData;
  error?: string;
  textResponse?: string[];
}

// Function to safely parse SSE data with type checking
function parseSSEData(data: string): TypedChatResponse | null | { done: boolean } {
  if (!data.startsWith('data: ')) {
    return null;
  }
  
  const jsonStr = data.slice(6); // Remove 'data: ' prefix
  
  if (jsonStr === '[DONE]') {
    return { done: true };
  }
  
  try {
    return JSON.parse(jsonStr) as TypedChatResponse;
  } catch (error) {
    console.error('Failed to parse SSE data:', jsonStr);
    return null;
  }
}

// Type guard for location response
function isLocationResponse(response: TypedChatResponse): response is LocationResponse {
  return response.type === 'location';
}

// Type guard for text response
function isTextResponse(response: TypedChatResponse): response is TextResponse {
  return response.type === 'text';
}

// Type guard for action response with location data
function isLocationActionResponse(response: TypedChatResponse): boolean {
  return response.type === 'action' && 
         (response as ActionResponse).data?.type === 'location';
}

// Function to extract location data from various response types
function extractLocationData(response: TypedChatResponse): LocationData | null {
  if (isLocationResponse(response)) {
    return response.data;
  } else if (isLocationActionResponse(response)) {
    const actionResponse = response as ActionResponse;
    return actionResponse.data.data;
  }
  return null;
}

// Function to test chat with location detection
async function testLocationDetection(message: string): Promise<TestResult> {
  console.log(`\n\nTesting message: "${message}"`);
  console.log('----------------------------------------');
  
  try {
    const response = await fetch('http://localhost:5000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message,
        sessionId: `test-${Date.now()}`,
        context: {},
        extractLocation: true // Add extractLocation flag to enable location detection
      })
    });
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    // Safely handle the SSE response
    let detectedLocation: LocationData | null = null;
    let responseParts: string[] = [];
    
    // Read the response body directly as text
    const responseText = await response.text();
    
    // Process SSE format data (data: {...}\n\n)
    // SSE messages are separated by double newlines
    const events = responseText.split('\n\n');
    
    // Process each SSE event
    for (const event of events) {
      // Skip empty events or non-data events
      if (!event.trim() || !event.startsWith('data:')) continue;
      
      // Extract the JSON part from 'data: {...}'
      const jsonStr = event.substring(5).trim();
      
      // Check for end of stream marker
      if (jsonStr === '[DONE]' || jsonStr === 'DONE') continue;
      
      try {
        // Parse the JSON string into an object
        const parsed = JSON.parse(jsonStr);
        console.log('Received event:', parsed.type);
        
        // Handle location type responses
        if (parsed.type === 'location' && parsed.data) {
          const locData = parsed.data;
          
          // Verify we have all required fields for a location
          if (locData.name && typeof locData.lat === 'number' && typeof locData.lng === 'number') {
            detectedLocation = {
              name: locData.name,
              lat: locData.lat,
              lng: locData.lng,
              placeType: locData.placeType || 'unknown'
            };
            
            console.log('📍 LOCATION DETECTED:', {
              name: detectedLocation.name,
              coordinates: `${detectedLocation.lat}, ${detectedLocation.lng}`,
              placeType: detectedLocation.placeType
            });
          }
        } 
        // Handle traditional text responses
        else if (parsed.type === 'text' && typeof parsed.data === 'string') {
          responseParts.push(parsed.data);
        }
        // Handle location inside action responses
        else if (parsed.type === 'action' && 
                 parsed.data && 
                 parsed.data.type === 'location' && 
                 parsed.data.data) {
          
          const locData = parsed.data.data;
          
          if (locData.name && typeof locData.lat === 'number' && typeof locData.lng === 'number') {
            detectedLocation = {
              name: locData.name,
              lat: locData.lat,
              lng: locData.lng,
              placeType: locData.placeType || 'unknown'
            };
            
            console.log('📍 LOCATION IN ACTION DETECTED:', {
              name: detectedLocation.name,
              coordinates: `${detectedLocation.lat}, ${detectedLocation.lng}`,
              placeType: detectedLocation.placeType
            });
          }
        }
      } catch (e) {
        console.error('Error parsing event:', e);
        console.error('Raw event:', event);
      }
    }
    
    if (detectedLocation) {
      console.log('✅ Test PASSED: Successfully detected location');
    } else {
      console.log('❌ Test FAILED: No location detected in response');
    }
    
    // Print combined text response
    console.log('\nText Response:');
    console.log(responseParts.join(' '));
    
    return { 
      success: !!detectedLocation, 
      location: detectedLocation || undefined,
      textResponse: responseParts
    };
  } catch (error) {
    console.error('Error during test:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    };
  }
}

// Interface for storing all test results
interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  locations: Array<{
    message: string;
    location: LocationData;
  }>;
}

// Run tests for all messages
async function runAllTests(): Promise<TestSummary> {
  console.log('Starting location detection tests...');
  
  const results: TestSummary = {
    total: TEST_MESSAGES.length,
    passed: 0,
    failed: 0,
    locations: []
  };
  
  for (const message of TEST_MESSAGES) {
    const result = await testLocationDetection(message);
    
    if (result.success && result.location) {
      results.passed++;
      results.locations.push({
        message,
        location: result.location
      });
    } else {
      results.failed++;
    }
  }
  
  // Print summary
  console.log('\n\n=============== TEST SUMMARY ===============');
  console.log(`Total tests: ${results.total}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success rate: ${(results.passed / results.total * 100).toFixed(2)}%`);
  
  if (results.locations.length > 0) {
    console.log('\nDetected locations:');
    results.locations.forEach((item, index) => {
      console.log(`${index + 1}. "${item.message}" → ${item.location.name} (${item.location.lat}, ${item.location.lng})`);
    });
  }
  
  return results;
}

// Execute all tests
// Check if this file is being executed directly
const isRunningDirectly = process.argv[1]?.endsWith('location-detection-test.ts');
if (isRunningDirectly) {
  runAllTests().catch(console.error);
}

export { testLocationDetection, runAllTests };