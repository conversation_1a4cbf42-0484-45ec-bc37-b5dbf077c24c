/**
 * Functional test for the chat API with location queries
 * This test script validates that the chat endpoint correctly handles location data
 */

const fetch = require('node-fetch');
const { v4: uuidv4 } = require('uuid');

// Configuration
const API_URL = 'http://localhost:5000/api/chat';
const TEST_TIMEOUT = 60000; // 60 seconds

// Test queries
const LOCATION_QUERIES = [
  "Tell me about hotels in Miami Beach",
  "What are some good hotels in New York?",
  "I'm looking for a place to stay in San Francisco",
  "Hotels near Disney World in Orlando",
  "Find me accommodation in Las Vegas"
];

/**
 * Parse SSE data from the chat stream
 */
function parseSSEData(data) {
  if (!data.startsWith('data: ')) {
    return { valid: false };
  }

  const content = data.slice(6).trim();
  
  if (content === '[DONE]') {
    return { valid: true, done: true };
  }
  
  try {
    const parsed = JSON.parse(content);
    return { valid: true, data: parsed };
  } catch (err) {
    return { valid: false, error: err.message };
  }
}

/**
 * Test a single chat query
 */
async function testChatQuery(query) {
  console.log(`\n------- Testing query: "${query}" -------`);
  
  const sessionId = `functional-test-${uuidv4()}`;
  
  const response = await fetch(API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      message: query,
      sessionId
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`❌ HTTP Error: ${response.status}`);
    console.error(`Error details: ${errorText}`);
    return { success: false, error: `HTTP ${response.status}: ${errorText}` };
  }

  if (!response.headers.get('content-type')?.includes('text/event-stream')) {
    console.error('❌ Not an event stream response');
    return { success: false, error: 'Not an event stream' };
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  let chunks = [];
  let receivedLocation = false;
  let receivedProperties = false;
  let locationData = null;
  
  try {
    // Process the stream
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        break;
      }
      
      const chunk = decoder.decode(value);
      const events = chunk.split('\n\n').filter(Boolean);
      
      for (const event of events) {
        const result = parseSSEData(event);
        
        if (!result.valid) {
          console.log(`⚠️ Invalid SSE data: ${event}`);
          continue;
        }
        
        if (result.done) {
          console.log('✅ Stream completed');
          continue;
        }
        
        chunks.push(result.data);
        
        // Check for location data
        if (result.data.type === 'location') {
          receivedLocation = true;
          locationData = result.data.data;
          console.log('✅ Received location data:', locationData);
        }
        
        // Check for action with location type
        if (result.data.type === 'action' && result.data.data?.type === 'location') {
          receivedLocation = true;
          locationData = result.data.data.data;
          console.log('✅ Received location action:', locationData);
        }
        
        // Check for property data
        if (result.data.type === 'properties') {
          receivedProperties = true;
          console.log(`✅ Received ${result.data.data.length} properties`);
        }
      }
    }
    
    console.log('\nTest summary:');
    console.log(`- Total chunks: ${chunks.length}`);
    console.log(`- Received location: ${receivedLocation}`);
    console.log(`- Received properties: ${receivedProperties}`);
    
    if (locationData) {
      console.log(`- Location: ${locationData.name} (${locationData.lat}, ${locationData.lng})`);
    }
    
    return {
      success: true,
      receivedLocation,
      receivedProperties,
      totalChunks: chunks.length,
      locationData
    };
    
  } catch (error) {
    console.error('❌ Error processing stream:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Run all test queries and report results
 */
async function runAllTests() {
  console.log('\n====== CHAT LOCATION FUNCTIONAL TESTS ======\n');
  
  const results = [];
  let passCount = 0;
  let failCount = 0;
  
  for (const query of LOCATION_QUERIES) {
    try {
      const result = await testChatQuery(query);
      results.push({ query, ...result });
      
      if (result.success) {
        passCount++;
      } else {
        failCount++;
      }
      
    } catch (error) {
      console.error(`Test failed for "${query}":`, error);
      results.push({ query, success: false, error: error.message });
      failCount++;
    }
  }
  
  console.log('\n====== TEST RESULTS SUMMARY ======');
  console.log(`Tests passed: ${passCount}/${LOCATION_QUERIES.length}`);
  console.log(`Tests failed: ${failCount}/${LOCATION_QUERIES.length}`);
  
  for (const result of results) {
    console.log(`- "${result.query}": ${result.success ? '✅ PASS' : '❌ FAIL'}`);
    if (!result.success && result.error) {
      console.log(`  Error: ${result.error}`);
    }
  }
  
  return {
    total: LOCATION_QUERIES.length,
    passed: passCount,
    failed: failCount,
    results
  };
}

// Run all tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(summary => {
      console.log('\nAll tests completed.');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(err => {
      console.error('Test execution failed:', err);
      process.exit(1);
    });
}

module.exports = {
  testChatQuery,
  runAllTests
};