/**
 * Location validation test utility
 * This script validates location support in the chat API response
 */

import { contextService } from '../services/contextService.js';
import {
  getConversationContext,
  handleChatStream,
  TypedChatResponse,
  LocationResponse
} from '../services/openai.js';
import { logError } from '../utils/logger.js';

// Test location queries
const TEST_QUERIES = [
  "Tell me about hotels in Miami Beach",
  "What's a good hotel in San Francisco?",
  "I need accommodations in Las Vegas",
  "Find me a place to stay in Los Angeles",
  "Hotel recommendations for Chicago"
];

/**
 * Test if a location response can be properly typed
 */
function validateLocationResponse(response: any): { 
  valid: boolean; 
  error?: string;
  location?: { name: string; lat: number; lng: number; placeType?: string };
} {
  try {
    // Check general response structure
    if (!response || typeof response !== 'object') {
      return { valid: false, error: 'Response is not an object' };
    }
    
    // Validate if it's a location response type
    if (response.type === 'location') {
      const locationResponse = response as LocationResponse;
      
      // Validate location data
      if (!locationResponse.data || typeof locationResponse.data !== 'object') {
        return { valid: false, error: 'Location data is missing or invalid' };
      }
      
      const { name, lat, lng, placeType } = locationResponse.data;
      
      // Validate required fields
      if (typeof name !== 'string' || name.trim() === '') {
        return { valid: false, error: 'Location name is missing or invalid' };
      }
      
      if (typeof lat !== 'number' || isNaN(lat)) {
        return { valid: false, error: 'Latitude is missing or invalid' };
      }
      
      if (typeof lng !== 'number' || isNaN(lng)) {
        return { valid: false, error: 'Longitude is missing or invalid' };
      }
      
      // Valid location response
      return { 
        valid: true, 
        location: { name, lat, lng, placeType }
      };
    }
    
    // Check for action type that contains location data
    if (response.type === 'action' && 
        response.data && 
        typeof response.data === 'object' && 
        response.data.type === 'location') {
      
      const locationData = response.data.data;
      
      // Validate location data in action
      if (!locationData || typeof locationData !== 'object') {
        return { valid: false, error: 'Location data in action is missing or invalid' };
      }
      
      const { name, lat, lng, placeType } = locationData;
      
      // Validate required fields
      if (typeof name !== 'string' || name.trim() === '') {
        return { valid: false, error: 'Location name in action is missing or invalid' };
      }
      
      if (typeof lat !== 'number' || isNaN(lat)) {
        return { valid: false, error: 'Latitude in action is missing or invalid' };
      }
      
      if (typeof lng !== 'number' || isNaN(lng)) {
        return { valid: false, error: 'Longitude in action is missing or invalid' };
      }
      
      // Valid location action
      return { 
        valid: true, 
        location: { name, lat, lng, placeType }
      };
    }
    
    // Not a location response
    return { valid: false, error: 'Not a location response' };
    
  } catch (error) {
    return { 
      valid: false, 
      error: `Error validating location: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Test location handling for a specific query
 */
export async function testLocationForQuery(query: string): Promise<{
  success: boolean;
  locationFound: boolean;
  locationData?: { name: string; lat: number; lng: number; placeType?: string };
  chunks: number;
  error?: string;
}> {
  console.log(`\n--- Testing location handling for: "${query}" ---`);
  
  const sessionId = `location-test-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
  const requestId = `request-${Date.now()}`;
  
  try {
    // Set up session context
    const sessionContext = contextService.getContext(sessionId);
    const conversation = getConversationContext(sessionId);
    
    // Add the query message
    contextService.addMessage(sessionId, {
      role: 'user',
      content: query,
      timestamp: Date.now()
    });
    
    // Prepare chat context for the request
    const chatContext = {
      conversation: conversation.context,
      messages: sessionContext.messages,
      searchContext: sessionContext.searchContext,
      userPreferences: sessionContext.userPreferences
    };
    
    // Create mock request
    const mockRequest = {
      headers: {},
      ip: '127.0.0.1'
    };
    
    // Process the chat stream
    const chatStream = handleChatStream(query, chatContext, mockRequest);
    
    let chunkCount = 0;
    let foundLocation = false;
    let locationData = null;
    
    // Process each response chunk
    for await (const chunk of chatStream) {
      chunkCount++;
      
      if (chunk) {
        // Check if this is a valid response chunk  
        if (typeof chunk === 'object' && 'type' in chunk && 'data' in chunk) {
          // Validate as location response
          const validation = validateLocationResponse(chunk);
          
          if (validation.valid && validation.location) {
            foundLocation = true;
            locationData = validation.location;
            console.log('✅ Found location data:', locationData);
            break; // Found what we were looking for
          }
        }
      }
    }
    
    if (foundLocation) {
      console.log('✅ Location successfully identified');
      return {
        success: true,
        locationFound: true,
        locationData: locationData!,
        chunks: chunkCount
      };
    } else {
      console.log('❌ No location data found in response');
      return {
        success: true,
        locationFound: false,
        chunks: chunkCount
      };
    }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('❌ Error testing location:', errorMessage);
    
    logError(requestId, 'Location validation test error', {
      query,
      error: errorMessage
    });
    
    return {
      success: false,
      locationFound: false,
      chunks: 0,
      error: errorMessage
    };
  }
}

/**
 * Run all location validation tests
 */
export async function runAllLocationTests(): Promise<{
  success: boolean;
  total: number;
  passed: number;
  failed: number;
  locationFound: number;
  results: Array<{
    query: string;
    success: boolean;
    locationFound: boolean;
    locationData?: any;
    error?: string;
  }>;
}> {
  console.log('\n===== RUNNING LOCATION VALIDATION TESTS =====');
  
  const results = [];
  let passCount = 0;
  let failCount = 0;
  let locationFoundCount = 0;
  
  for (const query of TEST_QUERIES) {
    try {
      const result = await testLocationForQuery(query);
      
      if (result.success) {
        passCount++;
      } else {
        failCount++;
      }
      
      if (result.locationFound) {
        locationFoundCount++;
      }
      
      results.push({
        query,
        success: result.success,
        locationFound: result.locationFound,
        locationData: result.locationData,
        error: result.error
      });
      
    } catch (error) {
      failCount++;
      console.error(`Error running test for "${query}":`, error);
      results.push({
        query,
        success: false,
        locationFound: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
  
  console.log('\n===== LOCATION TEST RESULTS =====');
  console.log(`Tests passed: ${passCount}/${TEST_QUERIES.length}`);
  console.log(`Tests failed: ${failCount}/${TEST_QUERIES.length}`);
  console.log(`Location found: ${locationFoundCount}/${TEST_QUERIES.length}`);
  
  for (const result of results) {
    const status = result.success 
      ? (result.locationFound ? '✅ LOCATION FOUND' : '⚠️ NO LOCATION')
      : '❌ FAILED';
      
    console.log(`- "${result.query}": ${status}`);
    
    if (result.locationFound && result.locationData) {
      console.log(`  Location: ${result.locationData.name} (${result.locationData.lat}, ${result.locationData.lng})`);
    }
    
    if (!result.success && result.error) {
      console.log(`  Error: ${result.error}`);
    }
  }
  
  return {
    success: failCount === 0,
    total: TEST_QUERIES.length,
    passed: passCount,
    failed: failCount,
    locationFound: locationFoundCount,
    results
  };
}

// Run tests directly if this module is executed
runAllLocationTests()
  .then(summary => {
    console.log('\nAll location tests completed.');
    console.log('Summary:', JSON.stringify(summary, null, 2));
    process.exit(summary.failed > 0 ? 1 : 0);
  })
  .catch(err => {
    console.error('Test execution failed:', err);
    process.exit(1);
  });