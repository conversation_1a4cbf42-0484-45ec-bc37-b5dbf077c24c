/**
 * Simple Node script to test location support in the chat API
 * This script doesn't rely on TypeScript typing
 */

const fetch = require('node-fetch');
const readline = require('readline');

// Configuration
const API_URL = 'http://localhost:5000/api/chat';

// Create a readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Parse SSE data from the response
 */
function parseSSEData(data) {
  if (!data.startsWith('data: ')) {
    return null;
  }
  
  const content = data.slice(6).trim();
  
  if (content === '[DONE]') {
    return { type: 'control', action: 'done' };
  }
  
  try {
    return JSON.parse(content);
  } catch (e) {
    console.error('Error parsing SSE data:', e.message);
    return null;
  }
}

/**
 * Check if a chunk is a location response
 */
function isLocationResponse(chunk) {
  // Direct location response
  if (chunk && chunk.type === 'location' && chunk.data) {
    return true;
  }
  
  // Action with location type
  if (chunk && chunk.type === 'action' && 
      chunk.data && chunk.data.type === 'location') {
    return true;
  }
  
  return false;
}

/**
 * Extract location data from a response chunk
 */
function extractLocationData(chunk) {
  if (chunk.type === 'location') {
    return chunk.data;
  }
  
  if (chunk.type === 'action' && chunk.data.type === 'location') {
    return chunk.data.data;
  }
  
  return null;
}

/**
 * Send a chat message and process the stream
 */
async function testChatWithLocation(message) {
  console.log(`\nSending query: "${message}"`);
  
  const sessionId = `test-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        sessionId
      })
    });
    
    if (!response.ok) {
      console.error(`Error: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.error('Error details:', errorText);
      return { success: false, error: errorText };
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    console.log('\nReceiving stream:');
    
    let foundLocation = false;
    let locationData = null;
    let chunkCount = 0;
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        break;
      }
      
      const chunk = decoder.decode(value);
      const events = chunk.split('\n\n').filter(Boolean);
      
      for (const event of events) {
        const parsedChunk = parseSSEData(event);
        chunkCount++;
        
        if (!parsedChunk) {
          continue;
        }
        
        if (parsedChunk.type === 'control' && parsedChunk.action === 'done') {
          console.log('✅ Stream completed');
          continue;
        }
        
        // Check for location data
        if (isLocationResponse(parsedChunk)) {
          foundLocation = true;
          locationData = extractLocationData(parsedChunk);
          console.log('\n🌎 LOCATION FOUND:', JSON.stringify(locationData, null, 2));
        } else {
          process.stdout.write('.');
        }
      }
    }
    
    console.log(`\n\nProcessed ${chunkCount} chunks`);
    
    if (foundLocation) {
      console.log('✅ Location data found in response');
      console.log('Location:', locationData);
      return { success: true, locationFound: true, locationData };
    } else {
      console.log('⚠️ No location data found in response');
      return { success: true, locationFound: false };
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Run a test with a custom query or default to a predefined one
 */
function runTest() {
  rl.question('Enter a location query (or press Enter for default): ', async (input) => {
    const query = input.trim() || "Tell me about hotels in Miami Beach";
    
    try {
      const result = await testChatWithLocation(query);
      console.log('\nTest result:', result.success ? 'SUCCESS' : 'FAILED');
      
      rl.question('Run another test? (y/n): ', (answer) => {
        if (answer.trim().toLowerCase() === 'y') {
          runTest();
        } else {
          rl.close();
          console.log('Test completed. Exiting...');
        }
      });
      
    } catch (error) {
      console.error('Test execution error:', error);
      rl.close();
    }
  });
}

// Start the test
console.log('==== LOCATION CHAT TEST ====');
console.log('This test checks if the chat API properly handles location data in responses');
runTest();