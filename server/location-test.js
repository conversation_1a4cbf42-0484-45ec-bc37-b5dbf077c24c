/**
 * Simple standalone test for location detection issues
 */
import fetch from 'node-fetch';

// Configuration
const API_URL = 'http://localhost:5000/api/chat';
const TEST_QUERY = 'I want to find hotels in Miami Beach';
const SESSION_ID = `test-${Date.now()}`;

/**
 * Safely parse SSE data
 */
function safeParseSSEData(rawData) {
  if (!rawData.trim()) return { valid: false };
  
  try {
    // Try to parse as JSON
    const parsed = JSON.parse(rawData);
    return { valid: true, parsed };
  } catch (error) {
    console.error('Error parsing JSON data:', error.message);
    console.log('Raw data was:', rawData);
    return { valid: false, error };
  }
}

/**
 * Run the location detection test
 */
async function runLocationTest() {
  console.log(`Testing location detection with query: "${TEST_QUERY}"`);
  
  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: TEST_QUERY,
        sessionId: SESSION_ID
      })
    });
    
    if (!response.ok) {
      console.error(`API request failed with status ${response.status}`);
      return;
    }
    
    // Get the response text
    const responseText = await response.text();
    
    console.log('\nProcessing SSE data chunks...\n');
    
    // Split and process the SSE data chunks
    const chunks = responseText.split('\n\n')
      .filter(chunk => chunk.trim() !== '' && chunk.startsWith('data:'))
      .map(chunk => chunk.replace(/^data: /, '').trim());
      
    let locationFound = false;
    
    // Process each chunk
    chunks.forEach((chunk, index) => {
      console.log(`\n--- Chunk ${index + 1} ---`);
      const result = safeParseSSEData(chunk);
      
      if (!result.valid) {
        console.log('Invalid chunk (not JSON)');
        return;
      }
      
      // Log the type and structure
      console.log(`Type: ${result.parsed.type}`);
      
      // Check for location data
      if (result.parsed.type === 'location') {
        locationFound = true;
        console.log('!!! LOCATION FOUND !!!');
        console.log(JSON.stringify(result.parsed.data, null, 2));
      } else if (result.parsed.type === 'action' && 
                result.parsed.data && 
                result.parsed.data.type === 'location') {
        locationFound = true;
        console.log('!!! LOCATION ACTION FOUND !!!');
        console.log(JSON.stringify(result.parsed.data.data, null, 2));
      } else if (result.parsed.type === 'text') {
        console.log(`Text: ${result.parsed.data.substring(0, 50)}...`);
      } else {
        console.log(JSON.stringify(result.parsed, null, 2));
      }
    });
    
    console.log('\n=== SUMMARY ===');
    console.log(`Total chunks: ${chunks.length}`);
    console.log(`Location found: ${locationFound ? 'YES' : 'NO'}`);
    
    if (!locationFound) {
      console.log('\nProblem detected: Location not found in response');
      console.log('Possible issues:');
      console.log('1. AI model not properly detecting location entities');
      console.log('2. Location detection logic in server/routes.ts might be failing');
      console.log('3. Response format from AI not matching expected schema');
    }
    
  } catch (error) {
    console.error('Error running test:', error);
  }
}

// Run the test
runLocationTest();