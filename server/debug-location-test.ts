/**
 * Debug location detection issue - simplified test
 */

import fetch from 'node-fetch';

async function debugLocationTest() {
  console.log('Running debug location test...');
  
  try {
    // Make a simpler request that should succeed
    const response = await fetch('http://localhost:5000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'I want to find hotels in Miami Beach',
        sessionId: `test-session-${Date.now()}`,
        extractLocation: true
      })
    });
    
    console.log(`Response status: ${response.status}`);
    
    if (response.ok) {
      const text = await response.text();
      console.log('Response text:', text);
    } else {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the debug test
debugLocationTest().catch(console.error);