/// <reference types="@types/jest" />
import { jest } from '@jest/globals';
import { getPropertyAvailability } from '../travsrv.js';
import '@testing-library/jest-dom';
import { mockHotelResponse } from './mockData/hotelResponse.js';

// Add jest.isolateModules to prevent teardown issues
jest.isolateModules(() => {
  // Mock fetch globally
  const globalFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
  global.fetch = globalFetch;

  describe('TravSrv Service', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe('getPropertyAvailability', () => {
      const mockParams = {
        inDate: '2025-01-28',
        outDate: '2025-01-30',
        adults: 2,
        rooms: 1,
        currency: 'USD'
      };

      it('should correctly parse hotel availability response', async () => {
        globalFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockHotelResponse)
        } as Response);

        const result = await getPropertyAvailability('19127', mockParams);

        console.log('Result:', JSON.stringify(result, null, 2));
        
        // Verify the response structure
        expect(result).toBeDefined();
        expect(result).not.toBeNull();
        
        // Verify rates data - should include all rate plans from mock
        expect(result?.ratePlans && Object.keys(result.ratePlans).length).toBe(7); // All rate plans from mock
        
        // Get the first rate plan
        const firstRatePlanCode = '906233979;18542182_MERCHANT_MERCHANT__0d13b433-8dd5-9099-d5d6-e4624d4cbee6';
        const firstRatePlan = result?.ratePlans?.[firstRatePlanCode];
        expect(firstRatePlan).toEqual(expect.objectContaining({
          code: firstRatePlanCode,
          description: 'Best Available',
          commissionStatus: 'Commissionable'
        }));

        // Verify the first room in the first rate plan
        const firstRoom = Object.values(firstRatePlan?.rooms || {})[0];
        expect(firstRoom).toBeDefined();
        expect(firstRoom?.rate).toBe(318.32);  // First night's rate
        expect(firstRoom?.totalAmount).toBe(1315.44);  // Total amount including taxes
        expect(firstRoom?.currency).toBe('USD');
      });

      it('should handle API errors gracefully', async () => {
        globalFetch.mockRejectedValueOnce(new Error('API Error'));

        try {
          await getPropertyAvailability('19127', mockParams);
          fail('Should have thrown an error');
        } catch (err: unknown) {
          expect(err).toBeInstanceOf(Error);
          if (err instanceof Error) {
            expect(err.message).toBe('API Error');
          }
        }
      });

      it('should retry on 429 status', async () => {
        globalFetch
          .mockResolvedValueOnce({
            ok: false,
            status: 429
          } as Response)
          .mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve(mockHotelResponse)
          } as Response);

        const result = await getPropertyAvailability('19127', mockParams);
        expect(result).toBeDefined();
        expect(globalFetch).toHaveBeenCalledTimes(2);
      });

      it('should return null when no hotel data is returned', async () => {
        globalFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ ArnResponse: { Availability: { } } })
        } as Response);

        const result = await getPropertyAvailability('565', mockParams);
        expect(result).toBeNull();
      });
    });
  });
}); 