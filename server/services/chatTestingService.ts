/**
 * Chat Testing Service
 * 
 * This service provides comprehensive testing utilities for the AI chat functionality,
 * including conversation path testing, response validation, and performance monitoring.
 */
import { db } from '../../db/index.js';
import logger from '../utils/logger.js';
import { 
  handleChatStream, 
  getConversationContext,
  addMessageToConversation,
  needsSummarization,
  summarizeConversation,
  ChatMessage,
  ConversationContext,
  TypedChatResponse,
  TextResponse,
  LocationResponse,
  PropertiesResponse,
  ActionResponse
} from './openai.js';
import { contextService, SessionContext } from './contextService.js';
import { validateLocationCoordinates } from './enhancedLocationService.js';

interface ChatTestCase {
  id: string;
  name: string;
  description: string;
  messages: Array<{
    role: 'user' | 'system';
    content: string;
  }>;
  expectedResponses: Array<{
    type: string;
    validationRules: Record<string, any>;
  }>;
  context?: {
    location?: {
      name: string;
      lat?: number;
      lng?: number;
    };
    dateRange?: {
      checkIn: string;
      checkOut: string;
    };
    preferences?: Record<string, any>;
  };
}

interface TestResult {
  testCaseId: string;
  timestamp: number;
  success: boolean;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  responses: TypedChatResponse[];
  validationResults: Array<{
    expectedType: string;
    actualType: string;
    rules: Record<string, any>;
    matches: boolean;
    failedRules: string[];
  }>;
  errors?: string[];
  metrics: {
    totalDuration: number;
    firstResponseTime: number;
    messagesPerMinute: number;
    totalTokens: number;
  };
}

// Define standard test cases for common scenarios
const standardTestCases: ChatTestCase[] = [
  {
    id: 'location-detection',
    name: 'Location Detection Test',
    description: 'Tests the chat system\'s ability to detect and process location information',
    messages: [
      {
        role: 'user',
        content: 'I want to find a hotel in Miami Beach'
      }
    ],
    expectedResponses: [
      {
        type: 'location',
        validationRules: {
          location: {
            name: (value: string) => value.toLowerCase().includes('miami beach'),
            lat: (value: number) => value > 25 && value < 26,
            lng: (value: number) => value > -81 && value < -80
          }
        }
      }
    ]
  },
  {
    id: 'date-extraction',
    name: 'Date Extraction Test',
    description: 'Tests the chat system\'s ability to extract date ranges from user messages',
    messages: [
      {
        role: 'user',
        content: 'I need a room from June 15 to June 20'
      }
    ],
    expectedResponses: [
      {
        type: 'text',
        validationRules: {
          content: (value: string) => value.includes('June')
        }
      }
    ],
    context: {
      location: {
        name: 'New York',
        lat: 40.7128,
        lng: -74.0060
      }
    }
  },
  {
    id: 'property-recommendations',
    name: 'Property Recommendations Test',
    description: 'Tests the chat system\'s ability to recommend properties based on user preferences',
    messages: [
      {
        role: 'user',
        content: 'I want a hotel with a pool and ocean view'
      }
    ],
    expectedResponses: [
      {
        type: 'properties',
        validationRules: {
          properties: (value: any[]) => Array.isArray(value) && value.length > 0
        }
      }
    ],
    context: {
      location: {
        name: 'Miami Beach',
        lat: 25.7617,
        lng: -80.1918
      },
      dateRange: {
        checkIn: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        checkOut: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }
    }
  },
  {
    id: 'amenity-understanding',
    name: 'Amenity Understanding Test',
    description: 'Tests the chat system\'s ability to understand and filter by amenities',
    messages: [
      {
        role: 'user',
        content: 'I need free wifi and parking'
      }
    ],
    expectedResponses: [
      {
        type: 'text',
        validationRules: {
          content: (value: string) => 
            value.toLowerCase().includes('wifi') && 
            value.toLowerCase().includes('parking')
        }
      }
    ],
    context: {
      location: {
        name: 'Chicago',
        lat: 41.8781,
        lng: -87.6298
      }
    }
  },
  {
    id: 'error-handling',
    name: 'Error Handling Test',
    description: 'Tests the chat system\'s ability to handle errors gracefully',
    messages: [
      {
        role: 'user',
        content: '@#$%^&*('
      }
    ],
    expectedResponses: [
      {
        type: 'text',
        validationRules: {
          content: (value: string) => value.length > 20
        }
      }
    ]
  }
];

/**
 * Run a specific test case
 */
export async function runTestCase(testCase: ChatTestCase): Promise<TestResult> {
  const testSessionId = `test-${testCase.id}-${Date.now()}`;
  const startTime = Date.now();
  const result: TestResult = {
    testCaseId: testCase.id,
    timestamp: startTime,
    success: false,
    messages: [],
    responses: [],
    validationResults: [],
    errors: [],
    metrics: {
      totalDuration: 0,
      firstResponseTime: 0,
      messagesPerMinute: 0,
      totalTokens: 0
    }
  };
  
  try {
    // Set up initial context if provided
    if (testCase.context) {
      const initialContext: ConversationContext = {
        summary: `Test conversation for ${testCase.name}`,
        lastSummarizedAt: Date.now(),
        messageCount: 0
      };
      
      if (testCase.context.location) {
        initialContext.location = {
          name: testCase.context.location.name,
          lat: testCase.context.location.lat || 0,
          lng: testCase.context.location.lng || 0
        };
      }
      
      if (testCase.context.dateRange) {
        initialContext.dateRange = testCase.context.dateRange;
      }
      
      if (testCase.context.preferences) {
        initialContext.preferences = testCase.context.preferences as any;
      }
      
      // Initialize the conversation context
      // Note: In a production system, we would use a proper context API
      // For testing purposes, we'll initialize basic context data
      addMessageToConversation(testSessionId, {
        role: 'system',
        content: `Test initialization for ${testCase.name}`,
        timestamp: Date.now()
      });
    }
    
    let firstResponseTime = 0;
    let responseCount = 0;
    
    // Process each message in the test case
    for (const message of testCase.messages) {
      // Add message to the conversation
      result.messages.push({
        role: message.role,
        content: message.content
      });
      
      // Add the message to the conversation context
      if (message.role === 'user') {
        addMessageToConversation(testSessionId, {
          role: 'user',
          content: message.content,
          timestamp: Date.now()
        });
        
        // Get the conversation context
        const conversation = getConversationContext(testSessionId);
        
        // Create a mock request object
        const mockReq = {
          headers: {},
          ip: '127.0.0.1'
        };
        
        // Handle the chat stream
        const messageStartTime = Date.now();
        let isFirstResponse = true;
        
        // Process the stream
        for await (const response of handleChatStream(
          message.content,
          {
            conversation: conversation.context,
            messages: conversation.messages,
            searchContext: contextService.getContext(testSessionId).searchContext
          },
          mockReq
        )) {
          // Record the first response time
          if (isFirstResponse) {
            firstResponseTime = Date.now() - messageStartTime;
            isFirstResponse = false;
          }
          
          // Add the response to the result
          result.responses.push(response);
          responseCount++;
          
          // Add assistant messages to the conversation for text responses
          if (response.type === 'text') {
            result.messages.push({
              role: 'assistant',
              content: (response as TextResponse).data.content
            });
            
            // Add the message to the conversation context
            addMessageToConversation(testSessionId, {
              role: 'assistant',
              content: (response as TextResponse).data.content,
              timestamp: Date.now()
            });
          }
        }
      } else {
        // For system messages, just record them
        continue;
      }
    }
    
    // Calculate metrics
    const totalDuration = Date.now() - startTime;
    const messagesPerMinute = (result.messages.length / totalDuration) * 60000;
    
    // Estimate token count (very rough estimation)
    let totalTokens = 0;
    for (const message of result.messages) {
      // Estimate about 1 token per 4 characters
      totalTokens += Math.ceil(message.content.length / 4);
    }
    
    result.metrics = {
      totalDuration,
      firstResponseTime: firstResponseTime || 0,
      messagesPerMinute,
      totalTokens
    };
    
    // Validate responses against expected responses
    result.validationResults = validateResponses(result.responses, testCase.expectedResponses);
    
    // Determine overall success
    result.success = result.validationResults.every(vr => vr.matches);
    
    return result;
  } catch (error) {
    logger.error('Test case execution failed', {
      testCaseId: testCase.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    result.success = false;
    result.errors = [error instanceof Error ? error.message : 'Unknown error'];
    
    return result;
  } finally {
    // Clean up the test session
    contextService.clearContext(testSessionId);
  }
}

/**
 * Validate responses against expected response types and rules
 */
function validateResponses(
  actualResponses: TypedChatResponse[],
  expectedResponses: Array<{ type: string, validationRules: Record<string, any> }>
): Array<{
  expectedType: string;
  actualType: string;
  rules: Record<string, any>;
  matches: boolean;
  failedRules: string[];
}> {
  const results = [];
  
  // Group responses by type for easier matching
  const responsesByType: Record<string, TypedChatResponse[]> = {};
  for (const response of actualResponses) {
    if (!responsesByType[response.type]) {
      responsesByType[response.type] = [];
    }
    responsesByType[response.type].push(response);
  }
  
  // Check each expected response
  for (const expected of expectedResponses) {
    const matchingResponses = responsesByType[expected.type] || [];
    
    if (matchingResponses.length === 0) {
      // No matching response type found
      results.push({
        expectedType: expected.type,
        actualType: 'not_found',
        rules: expected.validationRules,
        matches: false,
        failedRules: ['Response type not found']
      });
      continue;
    }
    
    // Validate each matching response against the rules
    let bestMatch = {
      response: null as any,
      failedRules: [] as string[],
      matches: false
    };
    
    for (const response of matchingResponses) {
      const failedRules = [];
      
      // Check each validation rule
      for (const [key, validator] of Object.entries(expected.validationRules)) {
        // Handle nested paths like 'location.name'
        const value = getNestedValue(response.data, key);
        
        // Apply the validator
        if (typeof validator === 'function') {
          if (!validator(value)) {
            failedRules.push(`Rule for '${key}' failed`);
          }
        } else if (validator !== value) {
          failedRules.push(`Expected '${key}' to be ${validator}, got ${value}`);
        }
      }
      
      // If this response has fewer failed rules, use it as the best match
      if (bestMatch.response === null || failedRules.length < bestMatch.failedRules.length) {
        bestMatch = {
          response,
          failedRules,
          matches: failedRules.length === 0
        };
        
        // If we found a perfect match, no need to check further
        if (bestMatch.matches) break;
      }
    }
    
    results.push({
      expectedType: expected.type,
      actualType: bestMatch.response ? bestMatch.response.type : 'unknown',
      rules: expected.validationRules,
      matches: bestMatch.matches,
      failedRules: bestMatch.failedRules
    });
  }
  
  return results;
}

/**
 * Get a nested value from an object using a dot-notation path
 */
function getNestedValue(obj: any, path: string): any {
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (current === null || current === undefined) {
      return undefined;
    }
    current = current[part];
  }
  
  return current;
}

/**
 * Run all standard test cases
 */
export async function runAllTests(): Promise<Record<string, TestResult>> {
  const results: Record<string, TestResult> = {};
  
  for (const testCase of standardTestCases) {
    logger.info(`Running test case: ${testCase.name}`, { testCaseId: testCase.id });
    results[testCase.id] = await runTestCase(testCase);
  }
  
  return results;
}

/**
 * Create a custom test case
 */
export function createTestCase(
  name: string,
  description: string,
  messages: Array<{ role: 'user' | 'system'; content: string }>,
  expectedResponses: Array<{ type: string; validationRules: Record<string, any> }>,
  context?: any
): ChatTestCase {
  return {
    id: `custom-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    name,
    description,
    messages,
    expectedResponses,
    context
  };
}

/**
 * Run tests for conversation paths to ensure the AI behaves correctly
 * in multi-turn conversations
 */
export async function testConversationPaths(): Promise<Record<string, TestResult>> {
  const conversationTestCases: ChatTestCase[] = [
    {
      id: 'location-dates-amenities',
      name: 'Location -> Dates -> Amenities Flow',
      description: 'Tests a natural conversation flow from location to dates to amenities',
      messages: [
        {
          role: 'user',
          content: 'I want to visit Miami Beach'
        },
        {
          role: 'user',
          content: 'I\'ll be staying for a week starting June 15'
        },
        {
          role: 'user',
          content: 'I need a hotel with a pool and ocean view'
        }
      ],
      expectedResponses: [
        {
          type: 'location',
          validationRules: {
            location: {
              name: (value: string) => value.toLowerCase().includes('miami beach')
            }
          }
        },
        {
          type: 'text',
          validationRules: {
            content: (value: string) => value.includes('June')
          }
        },
        {
          type: 'properties',
          validationRules: {
            properties: (value: any[]) => Array.isArray(value) && value.length > 0
          }
        }
      ]
    },
    {
      id: 'vague-to-specific',
      name: 'Vague to Specific Requirements Flow',
      description: 'Tests how the AI handles increasingly specific requirements',
      messages: [
        {
          role: 'user',
          content: 'I need a place to stay'
        },
        {
          role: 'user',
          content: 'I\'m going to New York'
        },
        {
          role: 'user',
          content: 'I want something affordable in Manhattan'
        },
        {
          role: 'user',
          content: 'It should be near Times Square'
        }
      ],
      expectedResponses: [
        {
          type: 'text',
          validationRules: {
            content: (value: string) => value.length > 20
          }
        },
        {
          type: 'location',
          validationRules: {
            location: {
              name: (value: string) => value.toLowerCase().includes('new york')
            }
          }
        },
        {
          type: 'text',
          validationRules: {
            content: (value: string) => 
              value.toLowerCase().includes('manhattan') ||
              value.toLowerCase().includes('affordable')
          }
        },
        {
          type: 'text',
          validationRules: {
            content: (value: string) => value.toLowerCase().includes('times square')
          }
        }
      ]
    }
  ];
  
  const results: Record<string, TestResult> = {};
  
  for (const testCase of conversationTestCases) {
    logger.info(`Running conversation test: ${testCase.name}`, { testCaseId: testCase.id });
    results[testCase.id] = await runTestCase(testCase);
  }
  
  return results;
}

/**
 * Test location detection accuracy
 */
export async function testLocationDetection(): Promise<{
  overall: { total: number, passed: number, failed: number },
  locationResults: Record<string, { valid: boolean, expected: any, actual: any }>
}> {
  const testLocations = [
    { query: 'New York', expected: { name: 'New York', lat: 40.7128, lng: -74.0060 } },
    { query: 'Miami Beach', expected: { name: 'Miami Beach', lat: 25.7617, lng: -80.1918 } },
    { query: 'Los Angeles', expected: { name: 'Los Angeles', lat: 34.0522, lng: -118.2437 } },
    { query: 'Chicago', expected: { name: 'Chicago', lat: 41.8781, lng: -87.6298 } },
    { query: 'Las Vegas', expected: { name: 'Las Vegas', lat: 36.1699, lng: -115.1398 } },
    { query: 'Hotels near Central Park', expected: { name: 'Central Park', lat: 40.7812, lng: -73.9665 } },
    { query: 'Accommodations in South Beach', expected: { name: 'South Beach', lat: 25.7825, lng: -80.1340 } },
    { query: 'Places to stay in Downtown Seattle', expected: { name: 'Downtown Seattle', lat: 47.6062, lng: -122.3321 } }
  ];
  
  const results: Record<string, { valid: boolean, expected: any, actual: any }> = {};
  let passed = 0;
  let failed = 0;
  
  for (const test of testLocations) {
    // Create a test case for this location
    const testCase: ChatTestCase = {
      id: `location-test-${test.query.replace(/\s+/g, '-').toLowerCase()}`,
      name: `Location Test: ${test.query}`,
      description: `Tests location detection for "${test.query}"`,
      messages: [
        {
          role: 'user',
          content: `I want to find accommodations in ${test.query}`
        }
      ],
      expectedResponses: [
        {
          type: 'location',
          validationRules: {}
        }
      ]
    };
    
    // Run the test
    const result = await runTestCase(testCase);
    
    // Find the location response
    const locationResponse = result.responses.find(r => r.type === 'location') as LocationResponse;
    
    if (locationResponse) {
      const location = locationResponse.data.location;
      
      // Check if the location coordinates are valid
      const isValid = validateLocationCoordinates(
        location.name,
        location.lat,
        location.lng
      );
      
      results[test.query] = {
        valid: isValid,
        expected: test.expected,
        actual: location
      };
      
      if (isValid) {
        passed++;
      } else {
        failed++;
      }
    } else {
      results[test.query] = {
        valid: false,
        expected: test.expected,
        actual: null
      };
      failed++;
    }
  }
  
  return {
    overall: {
      total: testLocations.length,
      passed,
      failed
    },
    locationResults: results
  };
}

/**
 * Monitor and report on AI chat performance
 */
export function monitorPerformance(testResults: Record<string, TestResult>): {
  averageResponseTime: number;
  successRate: number;
  errorRate: number;
  averageTokensUsed: number;
  responseTimesByType: Record<string, number>;
} {
  let totalResponseTime = 0;
  let totalSuccesses = 0;
  let totalTokens = 0;
  const responseTimesByType: Record<string, { total: number, count: number }> = {};
  
  // Collect metrics from all test results
  Object.values(testResults).forEach(result => {
    totalResponseTime += result.metrics.firstResponseTime;
    totalTokens += result.metrics.totalTokens;
    
    if (result.success) {
      totalSuccesses++;
    }
    
    // Group response times by type
    result.responses.forEach(response => {
      if (!responseTimesByType[response.type]) {
        responseTimesByType[response.type] = { total: 0, count: 0 };
      }
      responseTimesByType[response.type].total += result.metrics.firstResponseTime;
      responseTimesByType[response.type].count++;
    });
  });
  
  const testCount = Object.keys(testResults).length;
  
  // Calculate averages
  const averageResponseTime = totalResponseTime / testCount;
  const successRate = (totalSuccesses / testCount) * 100;
  const errorRate = 100 - successRate;
  const averageTokensUsed = totalTokens / testCount;
  
  // Calculate average response time by type
  const avgResponseTimesByType: Record<string, number> = {};
  Object.entries(responseTimesByType).forEach(([type, data]) => {
    avgResponseTimesByType[type] = data.total / data.count;
  });
  
  return {
    averageResponseTime,
    successRate,
    errorRate,
    averageTokensUsed,
    responseTimesByType: avgResponseTimesByType
  };
}