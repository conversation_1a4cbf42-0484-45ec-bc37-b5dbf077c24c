/**
 * Proactive Intelligence Service
 * 
 * This service enhances the AI travel planner by providing proactive insights,
 * suggestions, and alerts based on user preferences and travel context.
 */
import { db } from '../../db/index.js';
import { properties } from '../../db/schema.js';
import { eq, and, or, sql, desc, asc, between } from 'drizzle-orm';
import logger from '../utils/logger.js';
import { getLocationDetails } from './enhancedLocationService.js';
import { contextService, SessionContext } from './contextService.js';
import { ConversationContext } from './openai.js';

interface PricingInsight {
  type: 'good_deal' | 'high_price' | 'price_drop' | 'price_increase';
  message: string;
  confidence: number;
  data?: {
    currentPrice: number;
    averagePrice?: number;
    percentageDifference?: number;
    alternativeDates?: {
      checkIn: string;
      checkOut: string;
      price: number;
    }[];
  };
}

interface EventAlert {
  type: 'local_event' | 'holiday' | 'peak_season' | 'weather';
  message: string;
  startDate: string;
  endDate: string;
  impact: 'high' | 'medium' | 'low';
  affectedServices?: string[];
}

interface AlternativeSuggestion {
  type: 'location' | 'property' | 'dates';
  message: string;
  reason: string;
  data: any;
}

interface TravelInsight {
  pricingInsights: PricingInsight[];
  eventAlerts: EventAlert[];
  alternativeSuggestions: AlternativeSuggestion[];
  tips: string[];
}

/**
 * Get proactive travel insights based on user's search context and conversation
 */
export async function getTravelInsights(
  sessionId: string,
  conversation: ConversationContext,
  searchContext: SessionContext['searchContext']
): Promise<TravelInsight> {
  try {
    // Initialize insight structure
    const insights: TravelInsight = {
      pricingInsights: [],
      eventAlerts: [],
      alternativeSuggestions: [],
      tips: []
    };

    // Only generate insights if we have location data
    if (!conversation.location) {
      return insights;
    }

    // Get enhanced location details
    const locationDetails = await getLocationDetails(
      conversation.location.name,
      conversation.location.lat,
      conversation.location.lng
    );

    // Generate pricing insights
    if (conversation.dateRange) {
      const pricingInsights = await getPricingInsights(
        conversation.location.name,
        conversation.dateRange.checkIn,
        conversation.dateRange.checkOut,
        conversation.preferences?.priceRange || undefined
      );
      insights.pricingInsights = pricingInsights;
    }

    // Generate event alerts
    if (locationDetails) {
      const eventAlerts = getEventAlerts(
        locationDetails,
        conversation.dateRange?.checkIn,
        conversation.dateRange?.checkOut
      );
      insights.eventAlerts = eventAlerts;
    }

    // Generate alternative suggestions
    const alternativeSuggestions = await getAlternativeSuggestions(
      conversation,
      searchContext,
      locationDetails
    );
    insights.alternativeSuggestions = alternativeSuggestions;

    // Generate travel tips
    const tips = getTravelTips(
      conversation.location.name,
      conversation.dateRange,
      locationDetails
    );
    insights.tips = tips;

    return insights;
  } catch (error) {
    logger.error('Failed to generate travel insights', {
      sessionId,
      location: conversation.location?.name,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    // Return empty insights on error
    return {
      pricingInsights: [],
      eventAlerts: [],
      alternativeSuggestions: [],
      tips: []
    };
  }
}

/**
 * Get pricing insights for the specified location and dates
 */
async function getPricingInsights(
  locationName: string,
  checkIn: string,
  checkOut: string,
  priceRange?: [number, number]
): Promise<PricingInsight[]> {
  try {
    const insights: PricingInsight[] = [];
    
    // Calculate average price for properties in this location
    const averagePriceResult = await db.select({
      averagePrice: sql<number>`AVG(${properties.basePrice})`
    })
    .from(properties)
    .where(
      sql`LOWER(${properties.city}) LIKE ${`%${locationName.toLowerCase()}%`}`
    );
    
    const averagePrice = averagePriceResult[0]?.averagePrice || 0;
    
    // If we have a price range, compare to market average
    if (priceRange) {
      const [minPrice, maxPrice] = priceRange;
      const midPrice = (minPrice + maxPrice) / 2;
      
      // Check if the price range is significantly lower than average
      if (midPrice < averagePrice * 0.8) {
        insights.push({
          type: 'good_deal',
          message: `Your budget range is lower than average prices in ${locationName}. You're looking at good value options!`,
          confidence: 0.8,
          data: {
            currentPrice: midPrice,
            averagePrice,
            percentageDifference: ((averagePrice - midPrice) / averagePrice) * 100
          }
        });
      }
      
      // Check if the price range is significantly higher than average
      if (midPrice > averagePrice * 1.2) {
        insights.push({
          type: 'high_price',
          message: `Your budget range is higher than the average in ${locationName}. You'll be looking at premium options.`,
          confidence: 0.8,
          data: {
            currentPrice: midPrice,
            averagePrice,
            percentageDifference: ((midPrice - averagePrice) / averagePrice) * 100
          }
        });
      }
    }
    
    // Check for seasonal price fluctuations
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);
    
    // Calculate duration of stay
    const stayDuration = Math.round((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // Check if alternative dates might offer better prices
    const alternativeDates = findAlternativeDatePricing(locationName, checkInDate, stayDuration);
    
    if (alternativeDates.length > 0) {
      // Find the best alternative with the lowest price
      const bestAlternative = alternativeDates.sort((a, b) => a.price - b.price)[0];
      
      insights.push({
        type: 'price_drop',
        message: `Traveling on ${new Date(bestAlternative.checkIn).toLocaleDateString()} could save you money. Prices are typically lower then.`,
        confidence: 0.7,
        data: {
          currentPrice: averagePrice,
          alternativeDates
        }
      });
    }
    
    return insights;
  } catch (error) {
    logger.error('Failed to get pricing insights', {
      location: locationName,
      dates: `${checkIn} to ${checkOut}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return [];
  }
}

/**
 * Find alternative dates that might offer better pricing
 */
function findAlternativeDatePricing(
  locationName: string,
  checkInDate: Date,
  duration: number
): Array<{ checkIn: string, checkOut: string, price: number }> {
  // In a production system, this would query historical pricing data
  // For this implementation, we'll use seasonal pricing heuristics
  
  const alternatives = [];
  const month = checkInDate.getMonth();
  
  // Check if currently in high season for the location
  const isHighSeason = isLocationHighSeason(locationName, month);
  
  if (isHighSeason) {
    // Suggest off-season alternatives
    const offSeasonMonths = getOffSeasonMonths(locationName);
    
    for (const offMonth of offSeasonMonths) {
      // Create a date in the off-season month, same day
      const alternativeDate = new Date(checkInDate);
      alternativeDate.setMonth(offMonth);
      
      // If the alternative is in the past, adjust to next year
      if (alternativeDate < new Date()) {
        alternativeDate.setFullYear(alternativeDate.getFullYear() + 1);
      }
      
      // Calculate checkout date
      const checkoutDate = new Date(alternativeDate);
      checkoutDate.setDate(checkoutDate.getDate() + duration);
      
      // Estimate price reduction (20-30% in off-season)
      const randomReduction = 0.7 + Math.random() * 0.1; // 70-80% of high season price
      
      alternatives.push({
        checkIn: alternativeDate.toISOString().split('T')[0],
        checkOut: checkoutDate.toISOString().split('T')[0],
        price: 100 * randomReduction // Placeholder price
      });
      
      // Only suggest up to 2 alternatives
      if (alternatives.length >= 2) break;
    }
  } else {
    // If already in off-season, suggest mid-week alternatives if it's a weekend
    const dayOfWeek = checkInDate.getDay();
    if (dayOfWeek === 5 || dayOfWeek === 6) { // Friday or Saturday
      // Suggest mid-week alternative
      const alternativeDate = new Date(checkInDate);
      alternativeDate.setDate(alternativeDate.getDate() + ((2 - dayOfWeek + 7) % 7)); // Move to Tuesday
      
      // Calculate checkout date
      const checkoutDate = new Date(alternativeDate);
      checkoutDate.setDate(checkoutDate.getDate() + duration);
      
      // Estimate price reduction (10-15% for mid-week)
      const randomReduction = 0.85 + Math.random() * 0.05; // 85-90% of weekend price
      
      alternatives.push({
        checkIn: alternativeDate.toISOString().split('T')[0],
        checkOut: checkoutDate.toISOString().split('T')[0],
        price: 100 * randomReduction // Placeholder price
      });
    }
  }
  
  return alternatives;
}

/**
 * Check if a given month is high season for a location
 */
function isLocationHighSeason(locationName: string, month: number): boolean {
  const locationSeasons: Record<string, number[]> = {
    'new york': [11, 0, 6, 7], // Dec, Jan, Jul, Aug
    'miami': [0, 1, 2, 11], // Dec-Mar
    'los angeles': [5, 6, 7, 8], // Jun-Sep
    'paris': [5, 6, 7, 8], // Jun-Sep
    'london': [5, 6, 7, 8], // Jun-Sep
    'tokyo': [3, 4, 9, 10] // Apr, May, Oct, Nov
  };
  
  for (const [key, highSeasonMonths] of Object.entries(locationSeasons)) {
    if (locationName.toLowerCase().includes(key)) {
      return highSeasonMonths.includes(month);
    }
  }
  
  // Default high season is summer months
  return [5, 6, 7, 8].includes(month);
}

/**
 * Get off-season months for a location
 */
function getOffSeasonMonths(locationName: string): number[] {
  const locationOffSeasons: Record<string, number[]> = {
    'new york': [2, 3, 4, 9, 10], // Mar-May, Oct-Nov
    'miami': [5, 6, 7, 8, 9, 10], // Jun-Nov
    'los angeles': [0, 1, 2, 3, 4, 9, 10, 11], // Jan-May, Oct-Dec
    'paris': [0, 1, 2, 10, 11], // Jan-Mar, Nov-Dec
    'london': [0, 1, 2, 10, 11], // Jan-Mar, Nov-Dec
    'tokyo': [0, 1, 2, 6, 7, 8] // Jan-Mar, Jul-Sep
  };
  
  for (const [key, offSeasonMonths] of Object.entries(locationOffSeasons)) {
    if (locationName.toLowerCase().includes(key)) {
      return offSeasonMonths;
    }
  }
  
  // Default off-season is winter and fall
  return [0, 1, 2, 9, 10, 11]; // Jan-Mar, Oct-Dec
}

/**
 * Generate event alerts based on location and dates
 */
function getEventAlerts(
  locationDetails: any,
  checkIn?: string,
  checkOut?: string
): EventAlert[] {
  const alerts: EventAlert[] = [];
  
  // If no dates specified, we can't provide date-specific alerts
  if (!checkIn || !checkOut) return alerts;
  
  const checkInDate = new Date(checkIn);
  const checkOutDate = new Date(checkOut);
  
  // Check for seasonal events
  if (locationDetails.popularTimes) {
    const { season, highDemandDates, events } = locationDetails.popularTimes;
    
    // Check if stay overlaps with high demand dates
    for (const highDemandPeriod of highDemandDates) {
      // Parse date range like "December 20-31"
      const [monthStr, dayRange] = highDemandPeriod.split(' ');
      
      // Simple parsing - would be more robust in production
      const dayStart = parseInt(dayRange.split('-')[0]);
      const dayEnd = parseInt(dayRange.split('-')[1] || dayRange);
      
      const months = {
        'January': 0, 'February': 1, 'March': 2, 'April': 3,
        'May': 4, 'June': 5, 'July': 6, 'August': 7,
        'September': 8, 'October': 9, 'November': 10, 'December': 11
      };
      
      const monthIndex = months[monthStr as keyof typeof months] || 0;
      
      // Create date objects for the period
      const periodStart = new Date();
      periodStart.setMonth(monthIndex);
      periodStart.setDate(dayStart);
      
      const periodEnd = new Date();
      periodEnd.setMonth(monthIndex);
      periodEnd.setDate(dayEnd);
      
      // If period spans year boundary
      if (monthIndex === 11 && dayEnd > 25) {
        periodEnd.setFullYear(periodEnd.getFullYear() + 1);
      }
      
      // Check for overlap
      if (
        (checkInDate <= periodEnd && checkOutDate >= periodStart) ||
        (checkInDate.getMonth() === monthIndex && checkInDate.getDate() >= dayStart && checkInDate.getDate() <= dayEnd)
      ) {
        alerts.push({
          type: 'peak_season',
          message: `Your stay overlaps with a high-demand period (${highDemandPeriod}). Expect higher prices and larger crowds.`,
          startDate: periodStart.toISOString().split('T')[0],
          endDate: periodEnd.toISOString().split('T')[0],
          impact: 'high',
          affectedServices: ['Accommodations', 'Transportation', 'Attractions']
        });
      }
    }
    
    // Check for special events
    for (const event of events) {
      // For simplicity, assume events happen during stay if in same month
      // In a production system, we would have exact event dates
      if (checkInDate.getMonth() === new Date().getMonth()) {
        alerts.push({
          type: 'local_event',
          message: `There's a special event during your stay: "${event}". This might affect availability and prices.`,
          startDate: checkIn,
          endDate: checkOut,
          impact: 'medium'
        });
      }
    }
  }
  
  // Check for weather alerts
  if (locationDetails.weatherPattern) {
    const { precipitation } = locationDetails.weatherPattern;
    
    if (precipitation.includes('hurricane') || precipitation.includes('monsoon')) {
      alerts.push({
        type: 'weather',
        message: `Weather alert: ${precipitation}. Consider travel insurance for this period.`,
        startDate: checkIn,
        endDate: checkOut,
        impact: 'high',
        affectedServices: ['Transportation', 'Outdoor Activities']
      });
    } else if (
      precipitation.includes('rain') || 
      precipitation.includes('snow') ||
      precipitation.includes('storm')
    ) {
      alerts.push({
        type: 'weather',
        message: `Weather note: ${precipitation}. Pack appropriate clothing and consider indoor activities.`,
        startDate: checkIn,
        endDate: checkOut,
        impact: 'medium',
        affectedServices: ['Outdoor Activities']
      });
    }
  }
  
  return alerts;
}

/**
 * Generate alternative suggestions based on user context
 */
async function getAlternativeSuggestions(
  conversation: ConversationContext,
  searchContext: SessionContext['searchContext'],
  locationDetails: any
): Promise<AlternativeSuggestion[]> {
  const suggestions: AlternativeSuggestion[] = [];
  
  try {
    // Only suggest if we have location and preferences
    if (!conversation.location) return suggestions;
    
    // Suggest alternative locations
    if (conversation.preferences?.propertyTypes || conversation.preferences?.amenities) {
      const alternatives = await findAlternativeLocations(
        conversation.location.name,
        conversation.preferences.propertyTypes || [],
        conversation.preferences.amenities || []
      );
      
      if (alternatives.length > 0) {
        alternatives.slice(0, 2).forEach(alt => {
          suggestions.push({
            type: 'location',
            message: `Based on your preferences, you might also like ${alt.name}. It offers similar ${alt.reason}.`,
            reason: alt.reason,
            data: {
              name: alt.name,
              lat: alt.lat,
              lng: alt.lng,
              similarFeatures: alt.similarFeatures
            }
          });
        });
      }
    }
    
    // Suggest alternative property types if user is focused on a single type
    if (
      conversation.preferences?.propertyTypes &&
      conversation.preferences.propertyTypes.length === 1 &&
      searchContext.recentSearches.length > 0
    ) {
      const currentType = conversation.preferences.propertyTypes[0];
      const alternativeTypes = getSimilarPropertyTypes(currentType);
      
      if (alternativeTypes.length > 0) {
        suggestions.push({
          type: 'property',
          message: `You're focusing on ${currentType}s. You might also want to consider ${alternativeTypes.join(' or ')}.`,
          reason: `Similar experience to ${currentType}s but with different amenities and price points`,
          data: {
            currentType,
            alternativeTypes
          }
        });
      }
    }
    
    // Suggest alternative dates if we have date preferences and events are happening
    if (
      conversation.dateRange &&
      locationDetails?.popularTimes?.events &&
      locationDetails.popularTimes.events.length > 0
    ) {
      suggestions.push({
        type: 'dates',
        message: 'Consider adjusting your travel dates to experience local events or better pricing.',
        reason: 'Special events or pricing opportunities',
        data: {
          events: locationDetails.popularTimes.events,
          alternativeDates: findAlternativeDatePricing(
            conversation.location.name,
            new Date(conversation.dateRange.checkIn),
            calculateDuration(conversation.dateRange.checkIn, conversation.dateRange.checkOut)
          )
        }
      });
    }
    
    return suggestions;
  } catch (error) {
    logger.error('Failed to generate alternative suggestions', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return [];
  }
}

/**
 * Find alternative locations based on user preferences
 */
async function findAlternativeLocations(
  locationName: string,
  propertyTypes: string[],
  amenities: string[]
): Promise<Array<{
  name: string;
  lat: number;
  lng: number;
  reason: string;
  similarFeatures: string[];
}>> {
  // In a production system, this would query a location database
  // For this implementation, we'll use a curated list of alternative destinations
  
  const alternativeMap: Record<string, Array<{
    name: string;
    lat: number;
    lng: number;
    propertyTypes: string[];
    amenities: string[];
    features: string[];
  }>> = {
    'new york': [
      {
        name: 'Boston',
        lat: 42.3601,
        lng: -71.0589,
        propertyTypes: ['hotel', 'apartment', 'boutique'],
        amenities: ['wifi', 'gym', 'restaurant', 'business-center'],
        features: ['urban', 'historic', 'cultural', 'walkable']
      },
      {
        name: 'Philadelphia',
        lat: 39.9526,
        lng: -75.1652,
        propertyTypes: ['hotel', 'apartment', 'bed-and-breakfast'],
        amenities: ['wifi', 'parking', 'pool', 'pet-friendly'],
        features: ['urban', 'historic', 'affordable', 'food-scene']
      },
      {
        name: 'Washington DC',
        lat: 38.9072,
        lng: -77.0369,
        propertyTypes: ['hotel', 'apartment', 'extended-stay'],
        amenities: ['wifi', 'restaurant', 'bar', 'concierge'],
        features: ['urban', 'historic', 'cultural', 'monuments']
      }
    ],
    'miami': [
      {
        name: 'Fort Lauderdale',
        lat: 26.1224,
        lng: -80.1373,
        propertyTypes: ['hotel', 'resort', 'villa'],
        amenities: ['beach-access', 'pool', 'spa', 'water-sports'],
        features: ['beach', 'nightlife', 'boating', 'shopping']
      },
      {
        name: 'Key West',
        lat: 24.5551,
        lng: -81.7800,
        propertyTypes: ['resort', 'bed-and-breakfast', 'guesthouse'],
        amenities: ['beach-access', 'pool', 'bar', 'water-sports'],
        features: ['beach', 'relaxed', 'historic', 'nightlife']
      },
      {
        name: 'Tampa',
        lat: 27.9506,
        lng: -82.4572,
        propertyTypes: ['hotel', 'resort', 'apartment'],
        amenities: ['pool', 'parking', 'restaurant', 'family-friendly'],
        features: ['family-friendly', 'theme-parks', 'beaches', 'affordable']
      }
    ],
    'los angeles': [
      {
        name: 'San Diego',
        lat: 32.7157,
        lng: -117.1611,
        propertyTypes: ['hotel', 'resort', 'beach-house'],
        amenities: ['beach-access', 'pool', 'parking', 'family-friendly'],
        features: ['beaches', 'family-friendly', 'zoo', 'relaxed']
      },
      {
        name: 'Santa Barbara',
        lat: 34.4208,
        lng: -119.6982,
        propertyTypes: ['hotel', 'resort', 'boutique'],
        amenities: ['beach-access', 'pool', 'spa', 'wine-tours'],
        features: ['beaches', 'wine-country', 'upscale', 'relaxed']
      },
      {
        name: 'Palm Springs',
        lat: 33.8303,
        lng: -116.5453,
        propertyTypes: ['resort', 'villa', 'boutique'],
        amenities: ['pool', 'spa', 'golf', 'desert-views'],
        features: ['desert', 'golf', 'spa', 'mid-century-modern']
      }
    ]
  };
  
  // Find alternatives for the location
  for (const [key, alternatives] of Object.entries(alternativeMap)) {
    if (locationName.toLowerCase().includes(key)) {
      // Score each alternative based on matching user preferences
      const scoredAlternatives = alternatives.map(alt => {
        let score = 0;
        const matchingFeatures: string[] = [];
        
        // Score property type matches
        propertyTypes.forEach(type => {
          if (alt.propertyTypes.includes(type)) {
            score += 2;
            matchingFeatures.push(type);
          }
        });
        
        // Score amenity matches
        amenities.forEach(amenity => {
          if (alt.amenities.includes(amenity)) {
            score += 1;
            matchingFeatures.push(amenity);
          }
        });
        
        // Generate reason based on features
        let reason = 'attractions';
        if (alt.features.includes('beach')) {
          reason = 'beach access';
        } else if (alt.features.includes('cultural')) {
          reason = 'cultural attractions';
        } else if (alt.features.includes('historic')) {
          reason = 'historic sites';
        } else if (alt.features.includes('family-friendly')) {
          reason = 'family activities';
        }
        
        return {
          ...alt,
          score,
          reason,
          similarFeatures: matchingFeatures
        };
      });
      
      // Return alternatives with a score > 0, sorted by score
      return scoredAlternatives
        .filter(alt => alt.score > 0)
        .sort((a, b) => b.score - a.score)
        .map(({ name, lat, lng, reason, similarFeatures }) => ({
          name,
          lat,
          lng,
          reason,
          similarFeatures
        }));
    }
  }
  
  return [];
}

/**
 * Get similar property types to suggest as alternatives
 */
function getSimilarPropertyTypes(propertyType: string): string[] {
  const similarityMap: Record<string, string[]> = {
    'hotel': ['boutique-hotel', 'apart-hotel', 'resort'],
    'resort': ['hotel', 'all-inclusive', 'villa'],
    'apartment': ['apart-hotel', 'condo', 'vacation-rental'],
    'house': ['villa', 'cottage', 'vacation-rental'],
    'villa': ['house', 'luxury-apartment', 'resort'],
    'bed-and-breakfast': ['guesthouse', 'inn', 'boutique-hotel'],
    'hostel': ['guesthouse', 'budget-hotel', 'shared-apartment'],
    'cottage': ['cabin', 'house', 'vacation-rental'],
    'cabin': ['cottage', 'lodge', 'chalet']
  };
  
  // Normalize property type
  const normalizedType = propertyType.toLowerCase().replace(/\s+/g, '-');
  
  // Return similar types or an empty array if none found
  return similarityMap[normalizedType] || [];
}

/**
 * Generate travel tips based on location and dates
 */
function getTravelTips(
  locationName: string,
  dateRange?: { checkIn: string, checkOut: string },
  locationDetails?: any
): string[] {
  const tips: string[] = [];
  
  // Always add general booking tips
  tips.push('Book your accommodations as early as possible to secure the best rates and availability.');
  tips.push('Check cancellation policies before booking, especially during uncertain travel times.');
  
  // Add location-specific tips
  if (locationName) {
    const lowerName = locationName.toLowerCase();
    
    // City-specific tips
    if (lowerName.includes('new york')) {
      tips.push('Consider buying a MetroCard for convenient subway and bus transportation around NYC.');
      tips.push('Many museums in New York offer "pay what you wish" days - check their schedules.');
      tips.push('Midtown hotels tend to be more expensive than those in Downtown or Brooklyn.');
    } else if (lowerName.includes('miami')) {
      tips.push('Miami Beach properties often charge resort fees - check before booking.');
      tips.push('Consider staying in Downtown Miami or Brickell for better rates than beachfront properties.');
      tips.push('Rental cars can be expensive to park - check if your hotel charges for parking.');
    } else if (lowerName.includes('los angeles')) {
      tips.push('Los Angeles is spread out - consider your planned activities when choosing a location.');
      tips.push('Traffic can be heavy, especially during rush hours. Plan your transportation accordingly.');
      tips.push('Santa Monica and Beverly Hills hotels tend to be more expensive than other areas.');
    }
  }
  
  // Add seasonal tips
  if (dateRange) {
    const checkInMonth = new Date(dateRange.checkIn).getMonth();
    
    // Summer tips (May-September)
    if (checkInMonth >= 4 && checkInMonth <= 8) {
      tips.push('Summer is peak travel season in many destinations. Book well in advance.');
      tips.push('Pack lightweight clothing and sun protection for warm weather.');
    }
    // Winter tips (November-March)
    else if (checkInMonth >= 10 || checkInMonth <= 2) {
      tips.push('Winter weather can cause travel delays. Consider travel insurance.');
      tips.push('Many destinations offer lower rates during winter months (except for holiday periods).');
    }
    // Spring/Fall tips
    else {
      tips.push('Spring and fall are often "shoulder seasons" with good rates and fewer crowds.');
      tips.push('Weather can be variable during these seasons - pack layers.');
    }
  }
  
  // Add tips based on location details
  if (locationDetails) {
    // Add neighborhood tips
    if (locationDetails.neighborhoods && locationDetails.neighborhoods.length > 0) {
      const neighborhood = locationDetails.neighborhoods[0];
      tips.push(`${neighborhood.name} is known for ${neighborhood.description.toLowerCase()}. It's a great base for exploring.`);
      
      if (neighborhood.publicTransport && neighborhood.publicTransport.length > 0) {
        tips.push(`Public transportation options in ${neighborhood.name} include ${neighborhood.publicTransport.join(', ')}.`);
      }
    }
    
    // Add weather tips
    if (locationDetails.weatherPattern) {
      tips.push(`The weather is typically ${locationDetails.weatherPattern.precipitation.toLowerCase()} during this season.`);
    }
  }
  
  return tips;
}

/**
 * Calculate duration between two date strings in days
 */
function calculateDuration(checkIn: string, checkOut: string): number {
  const start = new Date(checkIn);
  const end = new Date(checkOut);
  const durationMs = end.getTime() - start.getTime();
  return Math.round(durationMs / (1000 * 60 * 60 * 24));
}