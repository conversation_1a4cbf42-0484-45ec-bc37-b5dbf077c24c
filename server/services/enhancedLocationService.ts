/**
 * Enhanced Location Service
 * 
 * This service provides improved location understanding for the AI travel planner,
 * including neighborhood-level recommendations, safety information, and 
 * improved proximity analysis.
 */
import { db } from '../../db/index.js';
import { properties } from '../../db/schema.js';
import { eq, and, or, sql } from 'drizzle-orm';
import logger from '../utils/logger.js';
import fetch from 'node-fetch';

interface NeighborhoodInfo {
  name: string;
  description: string;
  safetyScore?: number;
  walkabilityScore?: number;
  amenities: string[];
  landmarks: string[];
  publicTransport: string[];
}

interface LocationDetails {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
  neighborhoods?: NeighborhoodInfo[];
  landmarks?: {
    name: string;
    description: string;
    distance: number;
  }[];
  popularTimes?: {
    season: string;
    highDemandDates: string[];
    events: string[];
  };
  weatherPattern?: {
    season: string;
    temperature: {
      min: number;
      max: number;
      unit: string;
    };
    precipitation: string;
  };
}

const DEFAULT_RADIUS_KM = 1; // Default radius for neighborhood searches in kilometers

/**
 * Get detailed information about a location including neighborhoods and landmarks
 */
export async function getLocationDetails(locationName: string, lat: number, lng: number): Promise<LocationDetails | null> {
  try {
    // Create the base location details
    const locationDetails: LocationDetails = {
      name: locationName,
      lat,
      lng,
      placeType: determineLocationPlaceType(locationName)
    };

    // Add neighborhood information if available
    const neighborhoods = await getNeighborhoodInfo(locationName, lat, lng);
    if (neighborhoods && neighborhoods.length > 0) {
      locationDetails.neighborhoods = neighborhoods;
    }

    // Add nearby landmarks
    const landmarks = await getNearbyLandmarks(lat, lng);
    if (landmarks && landmarks.length > 0) {
      locationDetails.landmarks = landmarks;
    }

    // Add seasonal information
    locationDetails.popularTimes = getSeasonalInformation(locationName);
    
    // Add weather patterns
    locationDetails.weatherPattern = getCurrentWeatherPattern(locationName);

    return locationDetails;
  } catch (error) {
    logger.error('Failed to get enhanced location details', {
      location: locationName,
      coordinates: `${lat},${lng}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  }
}

/**
 * Determine the type of location (city, neighborhood, landmark, etc.)
 */
function determineLocationPlaceType(locationName: string): string {
  // Simple heuristic-based detection
  const lowerName = locationName.toLowerCase();
  
  if (lowerName.includes('airport')) return 'airport';
  if (lowerName.includes('station')) return 'station';
  if (lowerName.includes('museum') || lowerName.includes('gallery')) return 'attraction';
  if (lowerName.includes('park')) return 'park';
  if (lowerName.includes('beach')) return 'beach';
  if (lowerName.includes('hotel') || lowerName.includes('resort')) return 'accommodation';
  
  // Default to neighborhood for more specific areas
  return 'area';
}

/**
 * Get information about neighborhoods in and around the specified location
 */
async function getNeighborhoodInfo(locationName: string, lat: number, lng: number): Promise<NeighborhoodInfo[]> {
  // In a production system, this would connect to a neighborhood database or API
  // For this implementation, we'll return some sample data based on popular destinations
  
  // Sample neighborhoods for popular destinations
  const neighborhoodMap: Record<string, NeighborhoodInfo[]> = {
    'new york': [
      {
        name: 'Midtown',
        description: 'Central business district with iconic skyscrapers and attractions',
        safetyScore: 7.5,
        walkabilityScore: 9.0,
        amenities: ['Shopping', 'Dining', 'Entertainment'],
        landmarks: ['Times Square', 'Empire State Building', 'Rockefeller Center'],
        publicTransport: ['Subway', 'Bus']
      },
      {
        name: 'Upper East Side',
        description: 'Upscale residential area with museums and high-end shopping',
        safetyScore: 8.5,
        walkabilityScore: 8.0,
        amenities: ['Museums', 'Fine Dining', 'Luxury Shopping'],
        landmarks: ['Metropolitan Museum of Art', 'Central Park', 'Guggenheim Museum'],
        publicTransport: ['Subway', 'Bus']
      }
    ],
    'miami': [
      {
        name: 'South Beach',
        description: 'Vibrant beachfront area with Art Deco architecture and nightlife',
        safetyScore: 7.0,
        walkabilityScore: 8.5,
        amenities: ['Beaches', 'Nightlife', 'Dining'],
        landmarks: ['Ocean Drive', 'Art Deco Historic District', 'Lummus Park'],
        publicTransport: ['Bus', 'Trolley']
      },
      {
        name: 'Brickell',
        description: 'Modern financial district with luxury high-rises and upscale amenities',
        safetyScore: 8.0,
        walkabilityScore: 7.5,
        amenities: ['Fine Dining', 'Shopping', 'Nightlife'],
        landmarks: ['Brickell City Centre', 'Miami River', 'Simpson Park'],
        publicTransport: ['Metrorail', 'Metromover', 'Trolley']
      }
    ],
    'london': [
      {
        name: 'Westminster',
        description: 'Historic center with government buildings and major attractions',
        safetyScore: 8.0,
        walkabilityScore: 9.0,
        amenities: ['Historic Sites', 'Museums', 'Parks'],
        landmarks: ['Big Ben', 'Westminster Abbey', 'Buckingham Palace'],
        publicTransport: ['Tube', 'Bus', 'River Bus']
      },
      {
        name: 'Shoreditch',
        description: 'Trendy area known for street art, tech startups, and nightlife',
        safetyScore: 7.0,
        walkabilityScore: 8.5,
        amenities: ['Galleries', 'Bars', 'Markets'],
        landmarks: ['Brick Lane', 'Spitalfields Market', 'Shoreditch High Street'],
        publicTransport: ['Overground', 'Bus']
      }
    ]
  };
  
  // Find neighborhoods for the closest matching location
  for (const [key, neighborhoods] of Object.entries(neighborhoodMap)) {
    if (locationName.toLowerCase().includes(key)) {
      return neighborhoods;
    }
  }
  
  // If no specific neighborhoods found, return an empty array
  return [];
}

/**
 * Get nearby landmarks based on coordinates
 */
async function getNearbyLandmarks(lat: number, lng: number, radiusKm: number = DEFAULT_RADIUS_KM) {
  // In a production system, this would query a points-of-interest database or API
  // For this implementation, we'll use the property database to find properties that could be landmarks
  
  try {
    // Calculate the approximate bounds for the given radius
    // 0.01 degrees is roughly 1.1km at the equator, adjust based on latitude
    const latFactor = 0.01 * (radiusKm / 1.1);
    const lngFactor = latFactor / Math.cos((lat * Math.PI) / 180);
    
    const minLat = lat - latFactor;
    const maxLat = lat + latFactor;
    const minLng = lng - lngFactor;
    const maxLng = lng + lngFactor;
    
    // Query properties that might be landmarks within the area
    const nearbyProperties = await db.query.properties.findMany({
      where: and(
        sql`${properties.latitude} BETWEEN ${minLat} AND ${maxLat}`,
        sql`${properties.longitude} BETWEEN ${minLng} AND ${maxLng}`
      ),
      limit: 5
    });
    
    // Convert properties to landmarks
    return nearbyProperties.map(property => {
      // Calculate distance using Haversine formula
      const distance = calculateDistance(lat, lng, Number(property.latitude), Number(property.longitude));
      
      return {
        name: property.name,
        description: property.description || `A ${property.propertyType} in the area`,
        distance: parseFloat(distance.toFixed(2))
      };
    });
  } catch (error) {
    logger.error('Failed to get nearby landmarks', {
      coordinates: `${lat},${lng}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return [];
  }
}

/**
 * Get seasonal information for a location
 */
function getSeasonalInformation(locationName: string) {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  
  // Default seasons
  const seasons = [
    { months: [11, 0, 1], name: 'Winter' },
    { months: [2, 3, 4], name: 'Spring' },
    { months: [5, 6, 7], name: 'Summer' },
    { months: [8, 9, 10], name: 'Fall' }
  ];
  
  // Find current season
  const currentSeason = seasons.find(season => season.months.includes(currentMonth))?.name || 'Unknown';
  
  // Sample seasonal data for popular destinations
  const seasonalData: Record<string, any> = {
    'new york': {
      'Winter': {
        highDemandDates: ['December 20-31', 'New Year\'s Eve'],
        events: ['Rockefeller Center Christmas Tree', 'New Year\'s Eve in Times Square', 'NYC Winter Restaurant Week']
      },
      'Summer': {
        highDemandDates: ['July 1-5', 'Memorial Day Weekend', 'Labor Day Weekend'],
        events: ['Shakespeare in the Park', 'Governors Ball Music Festival', 'US Open Tennis']
      },
      'Spring': {
        highDemandDates: ['Easter Week', 'Mother\'s Day Weekend'],
        events: ['Cherry Blossom Festival', 'Tribeca Film Festival', 'Fleet Week']
      },
      'Fall': {
        highDemandDates: ['Thanksgiving Week', 'Columbus Day Weekend'],
        events: ['New York Fashion Week', 'New York Film Festival', 'Thanksgiving Day Parade']
      }
    },
    'miami': {
      'Winter': {
        highDemandDates: ['December 26-31', 'Martin Luther King Jr. Weekend'],
        events: ['Art Basel Miami Beach', 'Orange Bowl', 'Miami Marathon']
      },
      'Summer': {
        highDemandDates: ['Memorial Day Weekend', 'Fourth of July'],
        events: ['Miami Swim Week', 'International Hispanic Theatre Festival', 'American Black Film Festival']
      },
      'Spring': {
        highDemandDates: ['March 1-31 (Spring Break)', 'Easter Weekend'],
        events: ['Miami Music Week', 'Ultra Music Festival', 'Miami Open Tennis']
      },
      'Fall': {
        highDemandDates: ['Thanksgiving Week'],
        events: ['Miami Book Fair International', 'White Party Week', 'Art Miami']
      }
    }
  };
  
  // Look for location-specific seasonal data
  for (const [key, data] of Object.entries(seasonalData)) {
    if (locationName.toLowerCase().includes(key) && data[currentSeason]) {
      return {
        season: currentSeason,
        highDemandDates: data[currentSeason].highDemandDates || [],
        events: data[currentSeason].events || []
      };
    }
  }
  
  // Return generic seasonal data if no specific data found
  return {
    season: currentSeason,
    highDemandDates: [],
    events: []
  };
}

/**
 * Get current weather pattern for a location
 */
function getCurrentWeatherPattern(locationName: string) {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  
  // Default seasons
  const seasons = [
    { months: [11, 0, 1], name: 'Winter' },
    { months: [2, 3, 4], name: 'Spring' },
    { months: [5, 6, 7], name: 'Summer' },
    { months: [8, 9, 10], name: 'Fall' }
  ];
  
  // Find current season
  const currentSeason = seasons.find(season => season.months.includes(currentMonth))?.name || 'Unknown';
  
  // Sample weather data for popular destinations
  const weatherData: Record<string, any> = {
    'new york': {
      'Winter': {
        temperature: { min: 25, max: 40, unit: 'F' },
        precipitation: 'Snow and rain are common'
      },
      'Spring': {
        temperature: { min: 45, max: 65, unit: 'F' },
        precipitation: 'Moderate rainfall'
      },
      'Summer': {
        temperature: { min: 70, max: 85, unit: 'F' },
        precipitation: 'Occasional thunderstorms'
      },
      'Fall': {
        temperature: { min: 50, max: 70, unit: 'F' },
        precipitation: 'Light rainfall'
      }
    },
    'miami': {
      'Winter': {
        temperature: { min: 60, max: 75, unit: 'F' },
        precipitation: 'Dry season with little rainfall'
      },
      'Spring': {
        temperature: { min: 70, max: 85, unit: 'F' },
        precipitation: 'Increasing rainfall toward late spring'
      },
      'Summer': {
        temperature: { min: 75, max: 90, unit: 'F' },
        precipitation: 'Frequent afternoon thunderstorms'
      },
      'Fall': {
        temperature: { min: 70, max: 85, unit: 'F' },
        precipitation: 'Hurricane season, monitoring advisable'
      }
    }
  };
  
  // Look for location-specific weather data
  for (const [key, data] of Object.entries(weatherData)) {
    if (locationName.toLowerCase().includes(key) && data[currentSeason]) {
      return {
        season: currentSeason,
        temperature: data[currentSeason].temperature,
        precipitation: data[currentSeason].precipitation
      };
    }
  }
  
  // Return generic weather data if no specific data found
  return {
    season: currentSeason,
    temperature: { min: 60, max: 75, unit: 'F' },
    precipitation: 'Variable'
  };
}

/**
 * Calculate distance between two coordinates using the Haversine formula
 * Returns distance in kilometers
 */
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return distance;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

/**
 * Check if a location is valid and has accurate coordinates
 */
export function validateLocationCoordinates(locationName: string, lat: number, lng: number): boolean {
  // Basic validation to catch obvious errors
  if (isNaN(lat) || isNaN(lng)) return false;
  if (lat < -90 || lat > 90) return false;
  if (lng < -180 || lng > 180) return false;
  
  // Check for common location-coordinate mismatches
  // This is a simple heuristic and would be more sophisticated in production
  const locationChecks = [
    { name: 'new york', lat: { min: 40, max: 41 }, lng: { min: -74.5, max: -73.5 } },
    { name: 'miami', lat: { min: 25, max: 26 }, lng: { min: -80.5, max: -80 } },
    { name: 'los angeles', lat: { min: 33.5, max: 34.5 }, lng: { min: -118.5, max: -118 } },
    { name: 'chicago', lat: { min: 41.5, max: 42.5 }, lng: { min: -88, max: -87 } },
    { name: 'london', lat: { min: 51, max: 52 }, lng: { min: -0.5, max: 0.5 } },
    { name: 'paris', lat: { min: 48.5, max: 49.5 }, lng: { min: 2, max: 3 } }
  ];
  
  for (const check of locationChecks) {
    if (locationName.toLowerCase().includes(check.name)) {
      if (lat < check.lat.min || lat > check.lat.max || lng < check.lng.min || lng > check.lng.max) {
        logger.warn('Location coordinate mismatch detected', {
          location: locationName,
          coordinates: `${lat},${lng}`,
          expectedRange: `lat: ${check.lat.min}-${check.lat.max}, lng: ${check.lng.min}-${check.lng.max}`
        });
        return false;
      }
    }
  }
  
  return true;
}

/**
 * Get proximity information - how close a location is to points of interest
 */
export async function getProximityInfo(lat: number, lng: number, pointsOfInterest: string[]): Promise<Record<string, number>> {
  try {
    // In a production system, this would use a geospatial database or API
    // For this implementation, we'll use a simple proximity calculation
    
    // Sample POI coordinates for demonstration
    const poiCoordinates: Record<string, { lat: number, lng: number }> = {
      'beach': { lat: lat + 0.01, lng: lng + 0.02 },
      'downtown': { lat: lat - 0.01, lng: lng - 0.01 },
      'airport': { lat: lat + 0.05, lng: lng - 0.05 },
      'shopping': { lat: lat + 0.02, lng: lng + 0.01 },
      'restaurant': { lat: lat - 0.005, lng: lng + 0.005 }
    };
    
    const proximityInfo: Record<string, number> = {};
    
    for (const poi of pointsOfInterest) {
      const lowerPoi = poi.toLowerCase();
      let minDistance = Infinity;
      
      // Find the closest matching POI
      for (const [key, coords] of Object.entries(poiCoordinates)) {
        if (lowerPoi.includes(key)) {
          const distance = calculateDistance(lat, lng, coords.lat, coords.lng);
          minDistance = Math.min(minDistance, distance);
        }
      }
      
      if (minDistance !== Infinity) {
        proximityInfo[poi] = parseFloat(minDistance.toFixed(2));
      }
    }
    
    return proximityInfo;
  } catch (error) {
    logger.error('Failed to get proximity information', {
      coordinates: `${lat},${lng}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return {};
  }
}