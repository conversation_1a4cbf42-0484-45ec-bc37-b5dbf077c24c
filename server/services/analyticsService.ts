/**
 * Analytics Service
 * 
 * This service provides functions to track user activity including:
 * - Page views
 * - Search queries
 * - Property views
 * - User interactions
 * 
 * The data is stored in the database and used for analytics reporting.
 */

import { Request } from 'express';
import { db } from '../../db/index';
import { searchLogs, pageViews, propertyViews } from '../../db/schema';
import logger from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { sql, eq, and } from 'drizzle-orm';

/**
 * Extract session ID from request
 * First tries to get from session, then cookie, then creates a new one
 */
const getSessionId = (req: Request): string => {
  // Try to get from session
  if (req.session && req.session.id) {
    return req.session.id;
  }
  
  // Try to get from cookie
  if (req.cookies && req.cookies.sessionId) {
    return req.cookies.sessionId;
  }
  
  // Create a new session ID
  const sessionId = uuidv4();
  
  // Set cookie for future requests if we have a response object
  if (req.res) {
    req.res.cookie('sessionId', sessionId, {
      httpOnly: true,
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      secure: process.env.NODE_ENV === 'production'
    });
  }
  
  return sessionId;
};

/**
 * Extract user ID from request if user is authenticated
 */
const getUserId = (req: Request): number | undefined => {
  if (req.isAuthenticated() && req.user) {
    return req.user.id;
  }
  return undefined;
};

/**
 * Get client IP address from request
 */
const getClientIp = (req: Request): string => {
  return req.ip || 
    (req.headers['x-forwarded-for'] as string) || 
    req.socket.remoteAddress || 
    'unknown';
};

/**
 * Get user agent from request
 */
const getUserAgent = (req: Request): string => {
  return req.headers['user-agent'] || 'unknown';
};

/**
 * Determine device type from user agent
 */
const getDeviceType = (userAgent: string): string => {
  const ua = userAgent.toLowerCase();
  if (ua.includes('mobile')) return 'mobile';
  if (ua.includes('tablet')) return 'tablet';
  return 'desktop';
};

/**
 * Track a page view
 */
export const trackPageView = async (req: Request, pageTitle: string, duration?: number) => {
  try {
    const sessionId = getSessionId(req);
    const userId = getUserId(req);
    const pagePath = req.path;
    const referrer = req.headers.referer || '';
    const userAgent = getUserAgent(req);
    const ipAddress = getClientIp(req);
    const deviceType = getDeviceType(userAgent);
    
    await db.insert(pageViews).values({
      userId,
      sessionId,
      pagePath,
      pageTitle,
      referrer,
      duration,
      userAgent,
      ipAddress,
      deviceType,
      bounced: false // Will be updated later if this is a bounce
    });
    
    logger.debug('Page view tracked', { 
      userId, sessionId, pagePath, pageTitle, deviceType 
    });
  } catch (error) {
    logger.error('Error tracking page view', { error });
  }
};

/**
 * Track a search query
 */
export const trackSearch = async (req: Request, searchParams: {
  searchQuery?: string,
  locationName?: string,
  locationType?: string,
  latitude?: number,
  longitude?: number,
  checkIn?: Date,
  checkOut?: Date,
  guests?: number,
  rooms?: number,
  filters?: Record<string, any>,
  resultsCount?: number,
  source?: string
}) => {
  try {
    const sessionId = getSessionId(req);
    const userId = getUserId(req);
    const userAgent = getUserAgent(req);
    const ipAddress = getClientIp(req);
    
    await db.insert(searchLogs).values({
      userId,
      sessionId,
      searchQuery: searchParams.searchQuery,
      locationName: searchParams.locationName,
      locationType: searchParams.locationType,
      latitude: searchParams.latitude,
      longitude: searchParams.longitude,
      checkIn: searchParams.checkIn,
      checkOut: searchParams.checkOut,
      guests: searchParams.guests,
      rooms: searchParams.rooms,
      filters: searchParams.filters || {},
      resultsCount: searchParams.resultsCount || 0,
      source: searchParams.source || 'web',
      userAgent,
      ipAddress,
      successful: true
    });
    
    logger.debug('Search tracked', { 
      userId, sessionId, query: searchParams.searchQuery, 
      location: searchParams.locationName, resultsCount: searchParams.resultsCount 
    });
  } catch (error) {
    logger.error('Error tracking search', { error });
  }
};

/**
 * Track a property view
 */
export const trackPropertyView = async (req: Request, propertyId: number, additionalInfo?: {
  viewedPhotos?: boolean,
  viewedRates?: boolean,
  viewedReviews?: boolean,
  viewedAmenities?: boolean,
  savedProperty?: boolean,
  duration?: number,
  source?: string,
  exitAction?: string
}) => {
  try {
    const sessionId = getSessionId(req);
    const userId = getUserId(req);
    
    await db.insert(propertyViews).values({
      propertyId,
      userId,
      sessionId,
      viewedPhotos: additionalInfo?.viewedPhotos || false,
      viewedRates: additionalInfo?.viewedRates || false,
      viewedReviews: additionalInfo?.viewedReviews || false,
      viewedAmenities: additionalInfo?.viewedAmenities || false,
      savedProperty: additionalInfo?.savedProperty || false,
      duration: additionalInfo?.duration,
      source: additionalInfo?.source || 'search',
      exitAction: additionalInfo?.exitAction
    });
    
    logger.debug('Property view tracked', { 
      userId, sessionId, propertyId
    });
  } catch (error) {
    logger.error('Error tracking property view', { error });
  }
};

/**
 * Update an existing property view with additional interaction data
 */
export const updatePropertyView = async (sessionId: string, propertyId: number, updates: {
  viewedPhotos?: boolean,
  viewedRates?: boolean,
  viewedReviews?: boolean,
  viewedAmenities?: boolean,
  savedProperty?: boolean,
  duration?: number,
  exitAction?: string
}) => {
  try {
    await db
      .update(propertyViews)
      .set(updates)
      .where(and(
        eq(propertyViews.sessionId, sessionId),
        eq(propertyViews.propertyId, propertyId)
      ));
      
    logger.debug('Property view updated', { 
      sessionId, propertyId, updates 
    });
  } catch (error) {
    logger.error('Error updating property view', { error });
  }
};

/**
 * Mark a page view as a bounce
 */
export const markPageViewAsBounce = async (sessionId: string, pagePath: string) => {
  try {
    await db
      .update(pageViews)
      .set({ bounced: true })
      .where(and(
        eq(pageViews.sessionId, sessionId),
        eq(pageViews.pagePath, pagePath)
      ));
      
    logger.debug('Page view marked as bounce', { sessionId, pagePath });
  } catch (error) {
    logger.error('Error marking page view as bounce', { error });
  }
};

/**
 * Track a failed search query
 */
export const trackFailedSearch = async (req: Request, searchParams: {
  searchQuery?: string,
  locationName?: string,
  errorMessage?: string,
  source?: string
}) => {
  try {
    const sessionId = getSessionId(req);
    const userId = getUserId(req);
    const userAgent = getUserAgent(req);
    const ipAddress = getClientIp(req);
    
    await db.insert(searchLogs).values({
      userId,
      sessionId,
      searchQuery: searchParams.searchQuery,
      locationName: searchParams.locationName,
      source: searchParams.source || 'web',
      userAgent,
      ipAddress,
      successful: false
    });
    
    logger.debug('Failed search tracked', { 
      userId, sessionId, query: searchParams.searchQuery, 
      location: searchParams.locationName, error: searchParams.errorMessage 
    });
  } catch (error) {
    logger.error('Error tracking failed search', { error });
  }
};

export default {
  trackPageView,
  trackSearch,
  trackPropertyView,
  updatePropertyView,
  markPageViewAsBounce,
  trackFailedSearch,
  getSessionId,
  getUserId
};