import { db } from '../../db';
import { users, promoCodes } from '../../db/schema';
import { eq, and } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { nanoid } from 'nanoid';
import logger from '../utils/logger';

// JWT secret - ideally would be in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'travel-booking-secret-key';
const JWT_EXPIRES_IN = '7d';

export interface AuthUser {
  id: number;
  email: string;
  name: string;
  membershipType: string;
  isAdmin: boolean;
}

export interface LoginResponse {
  user: AuthUser;
  token: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  user?: AuthUser;
  token?: string;
}

/**
 * Authenticate a user with email and password
 */
export const login = async (email: string, password: string): Promise<LoginResponse | null> => {
  try {
    logger.debug('Login attempt', { email });
    
    // Find user by email
    const userResults = await db.select().from(users).where(eq(users.email, email.toLowerCase()));
    
    if (userResults.length === 0) {
      logger.debug('No user found with this email', { email });
      return null;
    }
    
    const user = userResults[0];
    logger.debug('User found', { userId: user.id, hasPassword: !!user.password });
    
    // Verify password
    try {
      const isValidPassword = await bcrypt.compare(password, user.password);
      logger.debug('Password validation result', { isValid: isValidPassword });
      
      if (!isValidPassword) {
        return null;
      }
    } catch (error) {
      logger.error('Error comparing passwords', { 
        error: error instanceof Error ? error.message : String(error),
        userId: user.id
      });
      return null;
    }
    
    // Try to extract name from preferences if available
    let displayName = 'User';
    if (user.preferences) {
      try {
        // Parse preferences if it's a string
        const preferences = typeof user.preferences === 'string' 
          ? JSON.parse(user.preferences) 
          : user.preferences;
        
        if (preferences && preferences.displayName) {
          displayName = preferences.displayName;
        }
      } catch (e) {
        // Ignore parsing errors, just use default
        logger.debug('Failed to parse user preferences', { userId: user.id });
      }
    }
    
    // Create auth user object - ensure no null values with fallbacks
    const authUser: AuthUser = {
      id: user.id,
      email: user.email,
      name: displayName,
      membershipType: user.membershipType ?? 'standard',
      isAdmin: user.isAdmin ?? false
    };
    
    // Generate JWT token
    const token = jwt.sign(authUser, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    
    // Update last login time
    await db.update(users)
      .set({ lastLoginAt: new Date() })
      .where(eq(users.id, user.id));
    
    logger.debug('User logged in', { userId: user.id });
    
    return {
      user: authUser,
      token
    };
  } catch (error) {
    logger.error('Login error', { error: error instanceof Error ? error.message : String(error) });
    return null;
  }
};

/**
 * Register a new user
 */
export const register = async (
  email: string,
  password: string,
  name: string,
  promoCode?: string
): Promise<RegisterResponse> => {
  try {
    // Check if email already exists
    const existingUser = await db.select().from(users).where(eq(users.email, email.toLowerCase()));
    
    if (existingUser.length > 0) {
      return {
        success: false,
        message: 'Email already in use'
      };
    }
    
    // Determine membership type based on promo code
    let membershipType = 'standard';
    
    if (promoCode) {
      const validPromoCode = await db.select()
        .from(promoCodes)
        .where(
          and(
            eq(promoCodes.code, promoCode),
            eq(promoCodes.isActive, true)
          )
        );
      
      if (validPromoCode.length > 0) {
        membershipType = validPromoCode[0].membershipType ?? 'premium';
        
        // Update promo code usage count
        await db.update(promoCodes)
          .set({ 
            usageCount: (validPromoCode[0].usageCount ?? 0) + 1,
            updatedAt: new Date()
          })
          .where(eq(promoCodes.id, validPromoCode[0].id));
      } else {
        return {
          success: false,
          message: 'Invalid promo code'
        };
      }
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Store user preferences as a JSON object
    const userPreferences = { displayName: name };
    
    // Create user with correctly mapped column names
    const [newUser] = await db.insert(users).values({
      email: email.toLowerCase(),
      password: hashedPassword,
      isAdmin: false,
      isVerified: false,
      membershipType,
      usedPromoCode: promoCode,
      preferences: userPreferences,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    
    // Create auth user object
    const authUser: AuthUser = {
      id: newUser.id,
      email: newUser.email,
      name: name, // Use the name provided during registration
      membershipType: newUser.membershipType ?? 'standard',
      isAdmin: newUser.isAdmin ?? false
    };
    
    // Generate JWT token
    const token = jwt.sign(authUser, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    
    logger.debug('User registered', { userId: newUser.id, membershipType });
    
    return {
      success: true,
      message: 'Registration successful',
      user: authUser,
      token
    };
  } catch (error) {
    logger.error('Registration error', { error: error instanceof Error ? error.message : String(error) });
    
    return {
      success: false,
      message: 'An error occurred during registration'
    };
  }
};

/**
 * Verify JWT token
 */
export const verifyToken = (token: string): AuthUser | null => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;
    return decoded;
  } catch (error) {
    logger.error('Token verification error', { error: error instanceof Error ? error.message : String(error) });
    return null;
  }
};

/**
 * Create a test promo code
 */
export const createTestPromoCode = async (): Promise<string> => {
  try {
    // Generate a random code
    const code = `TEST-${nanoid(6)}`;
    
    // Insert promo code
    await db.insert(promoCodes).values({
      code,
      description: 'Test promo code for premium membership',
      membershipType: 'premium',
      discountPercent: 10,
      maxUsages: 1,
      usageCount: 0,
      isActive: true,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    return code;
  } catch (error) {
    logger.error('Create promo code error', { error: error instanceof Error ? error.message : String(error) });
    throw error;
  }
};

/**
 * Get site ID based on user membership
 */
export const getSiteId = (membershipType: string | undefined): string => {
  // Site ID for guests: 30268
  // Site ID for members: 30992
  return membershipType === 'premium' ? '30992' : '30268';
};