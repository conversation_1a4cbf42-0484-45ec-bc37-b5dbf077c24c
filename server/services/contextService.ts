import { ConversationContext, ChatMessage } from './openai.js';
// Use a silent logger for context operations to prevent log messages from contaminating SSE streams
// This is necessary because log messages can appear in the SSE stream and break JSON parsing
import logger from '../utils/logger.js';

// Create a silent version of the logger that doesn't output to console for context operations
const silentLogger = {
  info: (message: string, meta?: any) => {
    // Only log to file, not to console
    if (process.env.NODE_ENV === 'development') {
      // In development, we can still write logs to file but not to console
      // This prevents "Initialize" messages from appearing in the SSE stream
    }
  },
  debug: (message: string, meta?: any) => {
    // Only log to file, not to console
    if (process.env.NODE_ENV === 'development') {
      // In development, we can still write logs to file but not to console
    }
  },
  error: logger.error // Keep error logging intact
};
import { db } from '@db/index.js';
import { users } from '@db/schema.js';
import { eq } from 'drizzle-orm';

// Extended session context with additional user journey information
export interface SessionContext {
  sessionId: string;
  userId?: number;
  conversation: ConversationContext;
  messages: ChatMessage[];
  searchContext: {
    recentSearches: Array<{
      timestamp: number;
      location: string;
      coordinates: { lat: number; lng: number };
      dates?: { checkIn: string; checkOut: string };
      guests?: number;
      rooms?: number;
    }>;
    viewedProperties: Array<{
      timestamp: number;
      propertyId: number;
      duration?: number;
    }>;
    comparedProperties: number[];
    bookingAttempts: Array<{
      timestamp: number;
      propertyId: number;
      completed: boolean;
    }>;
    filters: {
      priceRange?: [number, number];
      amenities?: string[];
      propertyTypes?: string[];
      rating?: number;
    };
  };
  userPreferences?: {
    favoriteLocations?: string[];
    preferredAmenities?: string[];
    preferredPropertyTypes?: string[];
    pricePreference?: 'budget' | 'midrange' | 'luxury';
    travelPurpose?: 'business' | 'leisure' | 'family';
  };
  lastUpdated: number;
}

// In-memory store (could be replaced with Redis in production)
const contextStore = new Map<string, SessionContext>();

export class ContextService {
  // Get or create context for a session
  getContext(sessionId: string): SessionContext {
    if (!contextStore.has(sessionId)) {
      this.initializeContext(sessionId);
    }
    return contextStore.get(sessionId)!;
  }

  // Initialize a new context for a session
  private initializeContext(sessionId: string): void {
    const newContext: SessionContext = {
      sessionId,
      conversation: {
        summary: '',
        lastSummarizedAt: Date.now(),
        messageCount: 0
      },
      messages: [],
      searchContext: {
        recentSearches: [],
        viewedProperties: [],
        comparedProperties: [],
        bookingAttempts: [],
        filters: {}
      },
      lastUpdated: Date.now()
    };
    
    contextStore.set(sessionId, newContext);
    silentLogger.info('Initialized new context for session', { 
      sessionId,
      timestamp: Date.now()
    });
  }

  // Update context with user information if available
  async associateUser(sessionId: string, userId: number): Promise<void> {
    const context = this.getContext(sessionId);
    
    if (context.userId !== userId) {
      context.userId = userId;
      context.lastUpdated = Date.now();
      
      // Fetch user preferences if available
      try {
        const user = await db.query.users.findFirst({
          where: eq(users.id, userId)
        });
        
        if (user?.preferences) {
          context.userPreferences = {
            favoriteLocations: user.preferences.preferredLocations,
            preferredAmenities: user.preferences.preferredAmenities,
            pricePreference: this.determinePricePreference(user.preferences.priceRange),
          };
        }
      } catch (error) {
        // Keep using the regular logger for errors since those won't interfere with SSE
        logger.error('Failed to fetch user preferences', { userId, error });
      }
      
      contextStore.set(sessionId, context);
      silentLogger.info(`Associated user ${userId} with session ${sessionId}`);
    }
  }

  // Add a search to the context
  addSearch(
    sessionId: string,
    location: string,
    coordinates: { lat: number; lng: number },
    dates?: { checkIn: string; checkOut: string },
    guests?: number,
    rooms?: number
  ): void {
    const context = this.getContext(sessionId);
    
    // Add to recent searches
    context.searchContext.recentSearches.unshift({
      timestamp: Date.now(),
      location,
      coordinates,
      dates,
      guests,
      rooms
    });
    
    // Keep only last 5 searches
    context.searchContext.recentSearches = context.searchContext.recentSearches.slice(0, 5);
    
    // Update conversation context with location and dates
    if (!context.conversation.location) {
      context.conversation.location = {
        name: location,
        lat: coordinates.lat,
        lng: coordinates.lng
      };
    }
    
    if (dates && !context.conversation.dateRange) {
      context.conversation.dateRange = {
        checkIn: dates.checkIn,
        checkOut: dates.checkOut
      };
    }
    
    if (guests && !context.conversation.preferences?.guestCount) {
      if (!context.conversation.preferences) {
        context.conversation.preferences = {
          amenities: [],
          propertyTypes: []
        };
      }
      context.conversation.preferences.guestCount = guests;
    }
    
    context.lastUpdated = Date.now();
    contextStore.set(sessionId, context);
    silentLogger.info('Added search to context for session', { 
      sessionId,
      location,
      timestamp: Date.now()
    });
  }

  // Record property view
  recordPropertyView(sessionId: string, propertyId: number): void {
    const context = this.getContext(sessionId);
    
    // Add to viewed properties
    context.searchContext.viewedProperties.unshift({
      timestamp: Date.now(),
      propertyId,
    });
    
    // Keep only last 10 viewed properties
    context.searchContext.viewedProperties = context.searchContext.viewedProperties.slice(0, 10);
    
    context.lastUpdated = Date.now();
    contextStore.set(sessionId, context);
    silentLogger.info('Recorded property view', { sessionId, propertyId });
  }

  // Update filters
  updateFilters(
    sessionId: string,
    filters: {
      priceRange?: [number, number];
      amenities?: string[];
      propertyTypes?: string[];
      rating?: number;
    }
  ): void {
    const context = this.getContext(sessionId);
    
    context.searchContext.filters = {
      ...context.searchContext.filters,
      ...filters
    };
    
    // Also update conversation preferences for AI context
    if (!context.conversation.preferences) {
      context.conversation.preferences = {
        amenities: [],
        propertyTypes: []
      };
    }
    
    if (filters.amenities) {
      context.conversation.preferences.amenities = filters.amenities;
    }
    
    if (filters.propertyTypes) {
      context.conversation.preferences.propertyTypes = filters.propertyTypes;
    }
    
    if (filters.priceRange) {
      context.conversation.preferences.priceRange = filters.priceRange;
    }
    
    context.lastUpdated = Date.now();
    contextStore.set(sessionId, context);
    silentLogger.info('Updated filters', { sessionId, filters });
  }

  // Add property comparison
  addToComparison(sessionId: string, propertyId: number): void {
    const context = this.getContext(sessionId);
    
    if (!context.searchContext.comparedProperties.includes(propertyId)) {
      context.searchContext.comparedProperties.push(propertyId);
      
      context.lastUpdated = Date.now();
      contextStore.set(sessionId, context);
      silentLogger.info('Added property to comparison', { sessionId, propertyId });
    }
  }

  // Record booking attempt
  recordBookingAttempt(sessionId: string, propertyId: number, completed: boolean): void {
    const context = this.getContext(sessionId);
    
    context.searchContext.bookingAttempts.unshift({
      timestamp: Date.now(),
      propertyId,
      completed
    });
    
    context.lastUpdated = Date.now();
    contextStore.set(sessionId, context);
    silentLogger.info('Recorded booking attempt', { sessionId, propertyId, completed });
  }

  // Update conversation context directly
  updateConversationContext(sessionId: string, conversation: ConversationContext): void {
    const context = this.getContext(sessionId);
    
    context.conversation = conversation;
    context.lastUpdated = Date.now();
    
    contextStore.set(sessionId, context);
    silentLogger.info('Updated conversation context', { sessionId });
  }

  // Add message to conversation
  addMessage(sessionId: string, message: ChatMessage): void {
    const context = this.getContext(sessionId);
    
    context.messages.push(message);
    context.lastUpdated = Date.now();
    
    contextStore.set(sessionId, context);
    silentLogger.debug(`Added message to context for session ${sessionId}`);
  }
  
  // Get property recommendations based on context
  getPropertySuggestions(sessionId: string): number[] {
    const context = this.getContext(sessionId);
    const suggestions: number[] = [];
    
    // This is a placeholder - in a real implementation, this would
    // use more sophisticated logic to generate recommendations
    
    // Add previously viewed properties that match current filters
    if (context.searchContext.viewedProperties.length > 0) {
      suggestions.push(...context.searchContext.viewedProperties
        .slice(0, 3)
        .map(view => view.propertyId));
    }
    
    return suggestions;
  }

  // Helper method to determine price preference category
  private determinePricePreference(
    priceRange?: { min: number; max: number }
  ): 'budget' | 'midrange' | 'luxury' {
    if (!priceRange) return 'midrange';
    
    const average = (priceRange.min + priceRange.max) / 2;
    
    if (average < 100) return 'budget';
    if (average > 300) return 'luxury';
    return 'midrange';
  }
}

// Export a singleton instance
export const contextService = new ContextService();