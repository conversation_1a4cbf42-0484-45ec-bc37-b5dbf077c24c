
import axios from 'axios';
import qs from 'qs';
import type { Property } from "@db/schema";
import { log } from "../vite";

interface ExpediaConfig {
  apiKey: string;
  secret: string;
  baseUrl: string;
}

interface ExpediaSearchParams {
  location?: string;
  checkIn?: string;
  checkOut?: string;
  guests?: string;
  regionIds?: string[];
  propertyIds?: string[];
}

interface ExpediaHotel {
  hotelId: string;
  name: string;
  address: {
    line1: string;
    city: string;
    countryCode: string;
  };
  location: {
    coordinates: {
      latitude: number;
      longitude: number;
    }
  };
  description: string;
  propertyType: string;
  images: Array<{
    url: string;
    category: string;
  }>;
  amenities: Array<{
    name: string;
    category: string;
  }>;
  rates: {
    baseRate: {
      amount: number;
      currency: string;
    }
  }[];
}

const config: ExpediaConfig = {
  apiKey: process.env.EXPEDIA_API_KEY || '',
  secret: process.env.EXPEDIA_SECRET || '',
  baseUrl: 'https://api.ean.com/v3'
};

function generateSignature(path: string, timestamp: string): string {
  const message = `${path}:${timestamp}:${config.secret}`;
  return require('crypto').createHash('sha256').update(message).digest('hex');
}

async function makeRequest(method: string, path: string, params?: any) {
  const timestamp = new Date().toISOString();
  const signature = generateSignature(path, timestamp);
  
  const requestConfig = {
    method,
    url: `${config.baseUrl}${path}`,
    headers: {
      'Authorization': `EAN apikey=${config.apiKey},signature=${signature},timestamp=${timestamp}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    },
    ...(params && { params })
  };

  try {
    const response = await axios(requestConfig);
    return response.data;
  } catch (error) {
    log(`Expedia API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
}

function transformHotelToProperty(hotel: ExpediaHotel): Property {
  return {
    id: parseInt(hotel.hotelId),
    externalId: `exp_${hotel.hotelId}`,
    name: hotel.name,
    description: hotel.description,
    type: hotel.propertyType.toLowerCase(),
    address: hotel.address.line1,
    city: hotel.address.city,
    country: hotel.address.countryCode,
    latitude: hotel.location.coordinates.latitude.toString(),
    longitude: hotel.location.coordinates.longitude.toString(),
    basePrice: hotel.rates[0]?.baseRate.amount.toString() || "0",
    images: hotel.images.map(img => img.url),
    amenities: hotel.amenities.map(amenity => amenity.name),
    createdAt: new Date()
  };
}

export async function searchProperties(params: ExpediaSearchParams): Promise<Property[]> {
  try {
    const searchParams = {
      location: params.location,
      checkInDate: params.checkIn,
      checkOutDate: params.checkOut,
      numberOfAdults: params.guests,
      ...(params.regionIds && { regionIds: params.regionIds }),
      ...(params.propertyIds && { propertyIds: params.propertyIds })
    };

    const response = await makeRequest('GET', '/properties/search', qs.stringify(searchParams));
    
    if (!response.data?.hotels) {
      return [];
    }

    return response.data.hotels.map(transformHotelToProperty);
  } catch (error) {
    log(`Expedia search error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return [];
  }
}

export async function getPropertyDetails(propertyId: string): Promise<Property | null> {
  try {
    const response = await makeRequest('GET', `/properties/${propertyId}`);
    
    if (!response.data?.hotel) {
      return null;
    }

    return transformHotelToProperty(response.data.hotel);
  } catch (error) {
    log(`Expedia property details error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return null;
  }
}

interface BookingRequest {
  propertyId: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  roomId?: string;
}

export async function createReservation(booking: BookingRequest) {
  try {
    const payload = {
      propertyId: booking.propertyId,
      checkInDate: booking.checkIn,
      checkOutDate: booking.checkOut,
      rooms: [{
        adults: booking.guests,
        children: 0,
        ...(booking.roomId && { roomId: booking.roomId })
      }],
      currency: 'USD'
    };

    const response = await makeRequest('POST', '/bookings', payload);
    
    if (!response.data?.booking) {
      throw new Error('Booking creation failed');
    }

    return {
      id: response.data.booking.id,
      confirmationNumber: response.data.booking.confirmationNumber,
      status: response.data.booking.status,
      propertyId: booking.propertyId,
      checkIn: booking.checkIn,
      checkOut: booking.checkOut,
      guests: booking.guests,
      createdAt: new Date()
    };
  } catch (error) {
    log(`Expedia booking error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
}

export async function cancelReservation(bookingId: string) {
  try {
    const response = await makeRequest('POST', `/bookings/${bookingId}/cancel`);
    
    if (!response.data?.cancellation) {
      throw new Error('Cancellation failed');
    }

    return {
      id: bookingId,
      status: 'cancelled',
      cancellationNumber: response.data.cancellation.id,
      refundAmount: response.data.cancellation.refundAmount,
      cancelledAt: new Date()
    };
  } catch (error) {
    log(`Expedia cancellation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
}

export async function getReservation(bookingId: string) {
  try {
    const response = await makeRequest('GET', `/bookings/${bookingId}`);
    
    if (!response.data?.booking) {
      return null;
    }

    return {
      id: response.data.booking.id,
      confirmationNumber: response.data.booking.confirmationNumber,
      status: response.data.booking.status,
      propertyId: response.data.booking.propertyId,
      checkIn: response.data.booking.checkInDate,
      checkOut: response.data.booking.checkOutDate,
      guests: response.data.booking.rooms[0].adults,
      createdAt: new Date(response.data.booking.createdAt)
    };
  } catch (error) {
    log(`Expedia reservation lookup error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return null;
  }
}
