/**
 * Enhanced Context Service
 * 
 * This service provides improved conversation context management
 * with server-side persistence, better state tracking, and more
 * robust error handling for AI conversations.
 */
import { db } from '../../db/index.js';
import logger from '../utils/logger.js';
import { contextService, SessionContext } from './contextService.js';
import { ConversationContext } from './openai.js';

// Interface for enhanced session data
interface EnhancedSessionData extends SessionContext {
  // Additional tracking for conversation state
  conversationState: {
    currentTopic?: string;
    activeIntents: string[];
    lastActionTimestamp: number;
    pendingActions: Array<{
      type: string;
      data: any;
      expiresAt: number;
    }>;
    conversationQuality: {
      relevanceScore: number;
      coherenceScore: number;
      engagementScore: number;
    };
  };
  // Track session persistence status
  persistence: {
    lastPersistedAt: number;
    persistenceStatus: 'initial' | 'pending' | 'saved' | 'error';
    persistenceError?: string;
  };
  // Recovery data for error handling
  recovery: {
    lastStableState?: any;
    checkpointTimestamps: number[];
    recoveryAttempts: number;
  };
  // User engagement metrics
  engagement: {
    sessionStartTime: number;
    messageCount: number;
    averageResponseTime: number;
    userRatings: Array<{
      timestamp: number;
      rating: number;
      comment?: string;
    }>;
  };
}

// In-memory cache for session data
// This provides fast access while we build permanent storage
const sessionCache = new Map<string, EnhancedSessionData>();

// Set interval for periodic persistence
const PERSISTENCE_INTERVAL = 60000; // 1 minute

// Initialize periodic session persistence
if (typeof setInterval !== 'undefined') {
  setInterval(persistAllSessions, PERSISTENCE_INTERVAL);
}

/**
 * Get enhanced session data with additional context tracking
 */
export function getEnhancedContext(sessionId: string): EnhancedSessionData {
  // Check cache first
  if (sessionCache.has(sessionId)) {
    return sessionCache.get(sessionId)!;
  }
  
  // Get base context from original service
  const baseContext = contextService.getContext(sessionId);
  
  // Create enhanced context with additional fields
  const enhancedContext: EnhancedSessionData = {
    ...baseContext,
    conversationState: {
      activeIntents: [],
      lastActionTimestamp: Date.now(),
      pendingActions: [],
      conversationQuality: {
        relevanceScore: 1.0,
        coherenceScore: 1.0,
        engagementScore: 1.0
      }
    },
    persistence: {
      lastPersistedAt: 0,
      persistenceStatus: 'initial'
    },
    recovery: {
      checkpointTimestamps: [],
      recoveryAttempts: 0
    },
    engagement: {
      sessionStartTime: Date.now(),
      messageCount: 0,
      averageResponseTime: 0,
      userRatings: []
    }
  };
  
  // Store in cache
  sessionCache.set(sessionId, enhancedContext);
  
  return enhancedContext;
}

/**
 * Update the enhanced context with new data
 */
export function updateEnhancedContext(
  sessionId: string,
  updates: Partial<EnhancedSessionData>
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Apply updates using deep merge
  const updated = deepMerge(context, updates);
  
  // Update in base service for compatibility
  contextService.setContext(sessionId, {
    sessionId,
    conversation: updated.conversation,
    messages: updated.messages,
    searchContext: updated.searchContext,
    lastUpdated: Date.now()
  });
  
  // Update cache
  sessionCache.set(sessionId, updated);
  
  // Mark as pending persistence
  updated.persistence.persistenceStatus = 'pending';
  
  // Create recovery checkpoint if enough changes
  const lastCheckpoint = updated.recovery.checkpointTimestamps[updated.recovery.checkpointTimestamps.length - 1] || 0;
  if (Date.now() - lastCheckpoint > 300000) { // 5 minutes
    updated.recovery.lastStableState = JSON.parse(JSON.stringify(updated));
    updated.recovery.checkpointTimestamps.push(Date.now());
    
    // Keep only the last 5 checkpoints
    if (updated.recovery.checkpointTimestamps.length > 5) {
      updated.recovery.checkpointTimestamps.shift();
    }
  }
  
  return updated;
}

/**
 * Record a user message and update conversation state
 */
export function recordUserMessage(
  sessionId: string,
  message: string
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Update engagement metrics
  context.engagement.messageCount++;
  
  // Update base service for compatibility
  contextService.addMessage(sessionId, {
    role: 'user',
    content: message,
    timestamp: Date.now()
  });
  
  // Update the cache with changes from base service
  const updatedBaseContext = contextService.getContext(sessionId);
  
  // Update cache with merged data
  const updated: EnhancedSessionData = {
    ...context,
    messages: updatedBaseContext.messages,
    conversation: updatedBaseContext.conversation,
    lastUpdated: updatedBaseContext.lastUpdated,
    conversationState: {
      ...context.conversationState,
      lastActionTimestamp: Date.now()
    },
    persistence: {
      ...context.persistence,
      persistenceStatus: 'pending'
    }
  };
  
  sessionCache.set(sessionId, updated);
  
  return updated;
}

/**
 * Record AI response and update conversation state
 */
export function recordAiResponse(
  sessionId: string,
  responseType: string,
  responseData: any,
  processingTime: number
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Update engagement metrics
  const currentAvgTime = context.engagement.averageResponseTime;
  const messageCount = context.engagement.messageCount;
  
  // Calculate new average response time
  const newAvgTime = currentAvgTime === 0
    ? processingTime
    : (currentAvgTime * messageCount + processingTime) / (messageCount + 1);
  
  // Update the context
  const updated = updateEnhancedContext(sessionId, {
    engagement: {
      ...context.engagement,
      averageResponseTime: newAvgTime
    },
    conversationState: {
      ...context.conversationState,
      lastActionTimestamp: Date.now()
    }
  });
  
  return updated;
}

/**
 * Add or update conversation intents
 */
export function updateConversationIntents(
  sessionId: string,
  intents: string[]
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Merge with existing intents, removing duplicates
  const existingIntents = context.conversationState.activeIntents || [];
  const uniqueIntents = [...new Set([...existingIntents, ...intents])];
  
  // Update the context
  return updateEnhancedContext(sessionId, {
    conversationState: {
      ...context.conversationState,
      activeIntents: uniqueIntents
    }
  });
}

/**
 * Add a pending action to the conversation
 */
export function addPendingAction(
  sessionId: string,
  type: string,
  data: any,
  expiresInMs: number = 30 * 60 * 1000 // Default: 30 minutes
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Create the pending action
  const pendingAction = {
    type,
    data,
    expiresAt: Date.now() + expiresInMs
  };
  
  // Add to existing pending actions
  const pendingActions = [
    ...context.conversationState.pendingActions,
    pendingAction
  ];
  
  // Clean up expired actions
  const validActions = pendingActions.filter(action => action.expiresAt > Date.now());
  
  // Update the context
  return updateEnhancedContext(sessionId, {
    conversationState: {
      ...context.conversationState,
      pendingActions: validActions
    }
  });
}

/**
 * Complete a pending action
 */
export function completePendingAction(
  sessionId: string,
  type: string,
  matchData?: any
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Find and remove the matching pending action
  const pendingActions = context.conversationState.pendingActions.filter(action => {
    // If matchData is provided, match on both type and data
    if (matchData) {
      return !(action.type === type && JSON.stringify(action.data) === JSON.stringify(matchData));
    }
    // Otherwise, just match on type
    return action.type !== type;
  });
  
  // Update the context
  return updateEnhancedContext(sessionId, {
    conversationState: {
      ...context.conversationState,
      pendingActions
    }
  });
}

/**
 * Update conversation quality scores
 */
export function updateConversationQuality(
  sessionId: string,
  qualityScores: {
    relevanceScore?: number;
    coherenceScore?: number;
    engagementScore?: number;
  }
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Update the quality scores
  const conversationQuality = {
    ...context.conversationState.conversationQuality,
    ...qualityScores
  };
  
  // Update the context
  return updateEnhancedContext(sessionId, {
    conversationState: {
      ...context.conversationState,
      conversationQuality
    }
  });
}

/**
 * Record user feedback/rating for a conversation
 */
export function recordUserRating(
  sessionId: string,
  rating: number,
  comment?: string
): EnhancedSessionData {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Add the rating
  const userRatings = [
    ...context.engagement.userRatings,
    {
      timestamp: Date.now(),
      rating,
      comment
    }
  ];
  
  // Update the context
  return updateEnhancedContext(sessionId, {
    engagement: {
      ...context.engagement,
      userRatings
    }
  });
}

/**
 * Attempt to recover from an error state
 */
export function recoverFromError(sessionId: string): EnhancedSessionData | null {
  // Get current context
  const context = getEnhancedContext(sessionId);
  
  // Check if we have a stable state to recover to
  if (!context.recovery.lastStableState) {
    logger.error('Recovery failed - no stable state available', { sessionId });
    return null;
  }
  
  // Increment recovery attempts
  const recoveryAttempts = context.recovery.recoveryAttempts + 1;
  
  // If too many attempts, create a fresh session
  if (recoveryAttempts > 3) {
    logger.warn('Too many recovery attempts, creating fresh session', { sessionId });
    
    // Create a minimal context with just essential information
    const minimalContext: EnhancedSessionData = {
      sessionId,
      conversation: {
        summary: 'Fresh session after recovery failure',
        lastSummarizedAt: Date.now(),
        messageCount: 0
      },
      messages: [],
      searchContext: {
        recentSearches: [],
        viewedProperties: [],
        comparedProperties: [],
        bookingAttempts: [],
        filters: {}
      },
      lastUpdated: Date.now(),
      conversationState: {
        activeIntents: [],
        lastActionTimestamp: Date.now(),
        pendingActions: [],
        conversationQuality: {
          relevanceScore: 1.0,
          coherenceScore: 1.0,
          engagementScore: 1.0
        }
      },
      persistence: {
        lastPersistedAt: 0,
        persistenceStatus: 'initial'
      },
      recovery: {
        checkpointTimestamps: [],
        recoveryAttempts: 0
      },
      engagement: {
        sessionStartTime: Date.now(),
        messageCount: 0,
        averageResponseTime: 0,
        userRatings: []
      }
    };
    
    // Transfer any location and date information if available
    if (context.conversation.location) {
      minimalContext.conversation.location = context.conversation.location;
    }
    
    if (context.conversation.dateRange) {
      minimalContext.conversation.dateRange = context.conversation.dateRange;
    }
    
    // Update the cache
    sessionCache.set(sessionId, minimalContext);
    
    // Update base service for compatibility
    contextService.setContext(sessionId, {
      sessionId,
      conversation: minimalContext.conversation,
      messages: [],
      searchContext: minimalContext.searchContext,
      lastUpdated: Date.now()
    });
    
    return minimalContext;
  }
  
  // Restore from the last stable state
  const stableState = context.recovery.lastStableState;
  
  // Update the recovery attempts
  stableState.recovery.recoveryAttempts = recoveryAttempts;
  
  // Update the cache
  sessionCache.set(sessionId, stableState);
  
  // Update base service for compatibility
  contextService.setContext(sessionId, {
    sessionId,
    conversation: stableState.conversation,
    messages: stableState.messages,
    searchContext: stableState.searchContext,
    lastUpdated: Date.now()
  });
  
  logger.info('Session recovered from stable state', { 
    sessionId,
    recoveryAttempt: recoveryAttempts,
    stableStateTimestamp: stableState.lastUpdated
  });
  
  return stableState;
}

/**
 * Persist all sessions to permanent storage
 */
async function persistAllSessions(): Promise<void> {
  try {
    // Get all sessions that need persistence
    const pendingSessions = Array.from(sessionCache.entries())
      .filter(([_, data]) => data.persistence.persistenceStatus === 'pending')
      .map(([id, _]) => id);
    
    if (pendingSessions.length === 0) {
      return;
    }
    
    logger.info(`Persisting ${pendingSessions.length} sessions`, {
      sessionCount: pendingSessions.length
    });
    
    // Persist each session
    for (const sessionId of pendingSessions) {
      await persistSession(sessionId);
    }
  } catch (error) {
    logger.error('Error in periodic session persistence', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Persist a single session to permanent storage
 */
async function persistSession(sessionId: string): Promise<boolean> {
  try {
    const context = sessionCache.get(sessionId);
    
    if (!context) {
      return false;
    }
    
    // In a production system, this would save to a database
    // For this implementation, we'll simulate persistence
    
    // Update persistence status
    context.persistence.lastPersistedAt = Date.now();
    context.persistence.persistenceStatus = 'saved';
    
    // Update the cache
    sessionCache.set(sessionId, context);
    
    return true;
  } catch (error) {
    logger.error('Error persisting session', {
      sessionId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    // Update persistence status to error
    const context = sessionCache.get(sessionId);
    if (context) {
      context.persistence.persistenceStatus = 'error';
      context.persistence.persistenceError = error instanceof Error ? error.message : 'Unknown error';
      sessionCache.set(sessionId, context);
    }
    
    return false;
  }
}

/**
 * Clear enhanced context for a session
 */
export function clearEnhancedContext(sessionId: string): void {
  // Clear from cache
  sessionCache.delete(sessionId);
  
  // Clear from base service
  contextService.clearContext(sessionId);
}

/**
 * Utility function to deep merge objects
 */
function deepMerge<T extends object>(target: T, source: Partial<T>): T {
  const output = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (
        typeof source[key] === 'object' && 
        source[key] !== null && 
        target.hasOwnProperty(key) && 
        typeof target[key] === 'object' &&
        target[key] !== null &&
        !Array.isArray(source[key]) &&
        !Array.isArray(target[key])
      ) {
        // @ts-ignore
        output[key] = deepMerge(target[key], source[key]);
      } else {
        // @ts-ignore
        output[key] = source[key];
      }
    }
  }
  
  return output;
}