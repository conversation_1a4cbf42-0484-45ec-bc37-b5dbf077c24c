import { Request, Response } from "express";
import { db } from "../../db";
import { testResults } from "../../db/schema";
import { eq, desc, and, sql } from "drizzle-orm";
import { nanoid } from "nanoid";
import { error as logError, info as logInfo } from "../utils/logger";

/**
 * Save a test result
 */
export const saveTestResult = async (req: Request, res: Response) => {
  try {
    const {
      sessionId = nanoid(),
      testType,
      query,
      success,
      duration,
      metrics = {},
      context = {},
      results = {},
      error
    } = req.body;

    if (!testType || !query || duration === undefined || success === undefined) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields (testType, query, success, duration)"
      });
    }

    // Get user ID from session if available
    const userId = req.user?.id;

    const savedResult = await db.insert(testResults).values({
      sessionId,
      userId: userId || null,
      testType,
      query,
      success,
      duration,
      metrics,
      context,
      results,
      error: error || null
    }).returning();

    logInfo(`Test result saved: ${testType}`, { 
      testId: savedResult[0].id, 
      success,
      query: query.substring(0, 50) // Log a snippet of the query
    });

    return res.status(201).json({
      success: true,
      message: "Test result saved successfully",
      data: savedResult[0]
    });

  } catch (error) {
    logError("Error saving test result", error);
    return res.status(500).json({
      success: false,
      message: "Error saving test result",
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Get test results with optional filtering
 */
export const getTestResults = async (req: Request, res: Response) => {
  try {
    const {
      testType,
      limit = 50,
      offset = 0,
      userId,
      sessionId,
      success
    } = req.query;

    // Build query conditions
    let conditions = [];
    
    if (testType) {
      conditions.push(eq(testResults.testType, testType as string));
    }
    
    if (userId) {
      conditions.push(eq(testResults.userId, Number(userId)));
    }
    
    if (sessionId) {
      conditions.push(eq(testResults.sessionId, sessionId as string));
    }
    
    if (success !== undefined) {
      conditions.push(eq(testResults.success, success === 'true'));
    }

    // Execute query
    const results = await db.select()
      .from(testResults)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(testResults.timestamp))
      .limit(Number(limit))
      .offset(Number(offset));

    // Get total count
    const countResult = await db.select({ count: sql`count(*)` })
      .from(testResults)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const totalCount = Number(countResult[0].count);

    return res.status(200).json({
      success: true,
      data: {
        results,
        pagination: {
          total: totalCount,
          limit: Number(limit),
          offset: Number(offset),
          pages: Math.ceil(totalCount / Number(limit)),
          currentPage: Math.floor(Number(offset) / Number(limit)) + 1
        }
      }
    });

  } catch (error) {
    logError("Error retrieving test results", error);
    return res.status(500).json({
      success: false,
      message: "Error retrieving test results",
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Get test result by ID
 */
export const getTestResultById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!id || isNaN(Number(id))) {
      return res.status(400).json({
        success: false,
        message: "Invalid test result ID"
      });
    }

    const result = await db.select()
      .from(testResults)
      .where(eq(testResults.id, Number(id)))
      .limit(1);

    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Test result not found"
      });
    }

    return res.status(200).json({
      success: true,
      data: result[0]
    });

  } catch (error) {
    logError(`Error retrieving test result ID ${req.params.id}`, error);
    return res.status(500).json({
      success: false,
      message: "Error retrieving test result",
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Get test metrics summary (aggregated stats)
 */
export const getTestMetrics = async (req: Request, res: Response) => {
  try {
    const { timeframe = '30d', testType } = req.query;
    
    let timeCondition;
    const now = new Date();
    
    // Parse timeframe
    switch(timeframe) {
      case '24h':
        timeCondition = sql`timestamp > now() - interval '1 day'`;
        break;
      case '7d':
        timeCondition = sql`timestamp > now() - interval '7 days'`;
        break;
      case '30d':
      default:
        timeCondition = sql`timestamp > now() - interval '30 days'`;
        break;
    }
    
    // Build conditions
    let conditions = [timeCondition];
    
    if (testType) {
      conditions.push(eq(testResults.testType, testType as string));
    }
    
    // Get success rate
    const successRateQuery = await db.select({
      total: sql`count(*)`,
      success: sql`sum(case when ${testResults.success} = true then 1 else 0 end)`,
      successRate: sql`(sum(case when ${testResults.success} = true then 1 else 0 end) * 100.0 / count(*))`,
      avgDuration: sql`avg(${testResults.duration})`,
    })
    .from(testResults)
    .where(and(...conditions));
    
    // Get test counts by type
    const testTypeCountsQuery = await db.select({
      testType: testResults.testType,
      count: sql`count(*)`,
    })
    .from(testResults)
    .where(and(...conditions))
    .groupBy(testResults.testType);
    
    // Get hourly trends for the timeframe
    const hourlyTrendsQuery = await db.select({
      hour: sql`date_trunc('hour', ${testResults.timestamp})`,
      count: sql`count(*)`,
      successCount: sql`sum(case when ${testResults.success} = true then 1 else 0 end)`,
      avgDuration: sql`avg(${testResults.duration})`,
    })
    .from(testResults)
    .where(and(...conditions))
    .groupBy(sql`date_trunc('hour', ${testResults.timestamp})`)
    .orderBy(sql`date_trunc('hour', ${testResults.timestamp})`);
    
    // Get common errors
    const commonErrorsQuery = await db.select({
      error: testResults.error,
      count: sql`count(*) as error_count`,
    })
    .from(testResults)
    .where(and(...conditions, sql`${testResults.error} is not null`))
    .groupBy(testResults.error)
    .orderBy(desc(sql`error_count`))
    .limit(5);
    
    return res.status(200).json({
      success: true,
      data: {
        overview: successRateQuery[0],
        testTypeCounts: testTypeCountsQuery,
        hourlyTrends: hourlyTrendsQuery,
        commonErrors: commonErrorsQuery,
        timeframe
      }
    });
    
  } catch (error) {
    logError("Error retrieving test metrics", error);
    return res.status(500).json({
      success: false,
      message: "Error retrieving test metrics",
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Delete a test result
 */
export const deleteTestResult = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!id || isNaN(Number(id))) {
      return res.status(400).json({
        success: false,
        message: "Invalid test result ID"
      });
    }

    // Verify the result exists
    const existingResult = await db.select({ id: testResults.id })
      .from(testResults)
      .where(eq(testResults.id, Number(id)))
      .limit(1);

    if (existingResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Test result not found"
      });
    }

    // Delete the result
    await db.delete(testResults)
      .where(eq(testResults.id, Number(id)));

    return res.status(200).json({
      success: true,
      message: "Test result deleted successfully"
    });

  } catch (error) {
    logError(`Error deleting test result ID ${req.params.id}`, error);
    return res.status(500).json({
      success: false,
      message: "Error deleting test result",
      error: error instanceof Error ? error.message : String(error)
    });
  }
};