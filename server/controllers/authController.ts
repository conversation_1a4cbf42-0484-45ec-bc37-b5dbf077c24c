import { Request, Response } from 'express';
import { login, register, createTestPromoCode } from '../services/authService';
import { z } from 'zod';
import logger from '../utils/logger';

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  promoCode: z.string().optional(),
});

/**
 * Handle user login
 */
export const loginUser = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = loginSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationResult.error.format()
      });
    }
    
    const { email, password } = validationResult.data;
    // Using consistent production authentication in all environments
    
    // Production login attempt
    try {
      const result = await login(email, password);
      
      if (!result) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }
      
      // Return user and token
      return res.status(200).json({
        success: true,
        user: result.user,
        token: result.token,
        message: 'Login successful'
      });
    } catch (error) {
      logger.error('Login error', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      if (process.env.NODE_ENV !== 'production') {
        // In development, if DB login fails, let's still provide a working auth experience
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password. For <NAME_EMAIL>/password123'
        });
      } else {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }
    }
  } catch (error) {
    logger.error('Login controller error', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    
    return res.status(500).json({
      success: false,
      message: 'An error occurred during login'
    });
  }
};

/**
 * Handle user registration
 */
export const registerUser = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = registerSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationResult.error.format()
      });
    }
    
    const { email, password, name, promoCode } = validationResult.data;
    
    // Using consistent production registration in all environments
    
    try {
      // Production: Register new user
      const result = await register(email, password, name, promoCode);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.message
        });
      }
      
      // Return success response
      return res.status(201).json({
        success: true,
        message: result.message,
        user: result.user,
        token: result.token
      });
    } catch (error) {
      logger.error('Registration error', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      if (process.env.NODE_ENV !== 'production') {
        // Let the user know about the development credentials
        return res.status(400).json({
          success: false,
          message: 'Registration failed. For development, use <EMAIL>/password123'
        });
      } else {
        return res.status(400).json({
          success: false,
          message: 'An error occurred during registration'
        });
      }
    }
  } catch (error) {
    logger.error('Registration controller error', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    
    return res.status(500).json({
      success: false,
      message: 'An error occurred during registration'
    });
  }
};

/**
 * Get test promo code for demo purposes
 */
export const getTestPromoCode = async (_req: Request, res: Response) => {
  try {
    const promoCode = await createTestPromoCode();
    
    return res.status(200).json({
      success: true,
      promoCode
    });
  } catch (error) {
    logger.error('Get promo code error', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    
    return res.status(500).json({
      success: false,
      message: 'An error occurred while generating promo code'
    });
  }
};

/**
 * Get current authenticated user information
 */
export const getCurrentUser = async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Not authenticated'
    });
  }
  
  return res.status(200).json({
    success: true,
    user: req.user
  });
};