import { Request, Response } from 'express';
import { db } from '../../db';
import { eq, desc, and, gte, count, sql, gt } from 'drizzle-orm';
import { format, subDays } from 'date-fns';
import logger from '../utils/logger';
import { users, properties, reservations, searchLogs, pageViews, propertyViews } from '../../db/schema';

/**
 * Get overview metrics for the admin dashboard
 */
export const getOverviewMetrics = async (req: Request, res: Response) => {
  try {
    // Get count of users
    const usersCount = await db.select({ count: count() }).from(users);
    
    // Get count of properties
    const propertiesCount = await db.select({ count: count() }).from(properties);
    
    // Get count of reservations (bookings)
    const bookingsCount = await db.select({ count: count() }).from(reservations);
    
    // Get count of searches from search_logs table
    const searchesCount = await db.select({ count: count() }).from(searchLogs);
    
    // Get count of page views
    const pageViewsCount = await db.select({ count: count() }).from(pageViews);
    
    // Get count of property views
    const propertyViewsCount = await db.select({ count: count() }).from(propertyViews);
    
    // Calculate booking conversion rate (from searches to bookings)
    const bookingConversionRate = searchesCount[0]?.count ? 
      (bookingsCount[0]?.count / searchesCount[0]?.count) * 100 : 0;
    
    // Calculate property view to booking conversion rate
    const propertyToBookingRate = propertyViewsCount[0]?.count ?
      (bookingsCount[0]?.count / propertyViewsCount[0]?.count) * 100 : 0;
    
    return res.status(200).json({
      usersCount: usersCount[0]?.count || 0,
      propertiesCount: propertiesCount[0]?.count || 0,
      searchesCount: searchesCount[0]?.count || 0,
      pageViewsCount: pageViewsCount[0]?.count || 0,
      propertyViewsCount: propertyViewsCount[0]?.count || 0,
      bookingsCount: bookingsCount[0]?.count || 0,
      bookingConversionRate: parseFloat(bookingConversionRate.toFixed(2)),
      propertyToBookingRate: parseFloat(propertyToBookingRate.toFixed(2))
    });
  } catch (error) {
    logger.error('Error getting overview metrics:', error);
    return res.status(500).json({ error: 'Failed to get overview metrics' });
  }
};

/**
 * Get user activity data
 */
export const getUserActivity = async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    
    // Get daily new user registrations
    const startDate = subDays(new Date(), days);
    
    // Format the date in a way the database understands (ISO format)
    const formattedStartDate = startDate.toISOString();
    
    // Get all users created after the start date
    const newUsers = await db
      .select({
        createdAt: users.createdAt,
      })
      .from(users)
      .where(sql`${users.createdAt} >= ${formattedStartDate}`)
      .orderBy(users.createdAt);
    
    // Group by date to get counts
    const registrationsByDay = newUsers.reduce((acc, user) => {
      // Make sure we have a valid date or use current date as fallback
      const dateToFormat = user.createdAt ? new Date(user.createdAt) : new Date();
      const date = format(dateToFormat, 'yyyy-MM-dd');
      
      if (!acc[date]) {
        acc[date] = 0;
      }
      
      acc[date]++;
      return acc;
    }, {} as Record<string, number>);
    
    // Generate date range for user registrations
    const registrations = Array.from({ length: days }, (_, i) => {
      const date = format(subDays(new Date(), days - i - 1), 'yyyy-MM-dd');
      return {
        date,
        count: registrationsByDay[date] || 0
      };
    });
    
    // Get page views as a proxy for user logins/sessions
    const pageViewsQuery = await db
      .select({
        createdAt: pageViews.createdAt,
        sessionId: pageViews.sessionId,
        userId: pageViews.userId
      })
      .from(pageViews)
      .where(sql`${pageViews.createdAt} >= ${formattedStartDate}`)
      .orderBy(pageViews.createdAt);
    
    // Count unique sessions per day as a proxy for "logins"
    const sessionsPerDay: Record<string, Set<string>> = {};
    pageViewsQuery.forEach(view => {
      if (!view.createdAt) return;
      
      const date = format(new Date(view.createdAt), 'yyyy-MM-dd');
      if (!sessionsPerDay[date]) {
        sessionsPerDay[date] = new Set();
      }
      
      // Add session ID to the set for this day (each session counts only once)
      if (view.sessionId) {
        sessionsPerDay[date].add(view.sessionId);
      }
    });
    
    // Convert to array format with counts
    const logins = Array.from({ length: days }, (_, i) => {
      const date = format(subDays(new Date(), days - i - 1), 'yyyy-MM-dd');
      const sessionsSet = sessionsPerDay[date];
      return {
        date,
        count: sessionsSet ? sessionsSet.size : 0
      };
    });
    
    // Get active users count (unique users who had activity)
    const activeUsersByDate: Record<string, Set<number>> = {};
    pageViewsQuery.forEach(view => {
      if (!view.createdAt || !view.userId) return;
      
      const date = format(new Date(view.createdAt), 'yyyy-MM-dd');
      if (!activeUsersByDate[date]) {
        activeUsersByDate[date] = new Set();
      }
      
      // Add user ID to set for this day
      activeUsersByDate[date].add(view.userId);
    });
    
    // Convert to array format
    const activeUsers = Array.from({ length: days }, (_, i) => {
      const date = format(subDays(new Date(), days - i - 1), 'yyyy-MM-dd');
      const usersSet = activeUsersByDate[date];
      return {
        date,
        count: usersSet ? usersSet.size : 0
      };
    });
    
    return res.status(200).json({
      registrations,
      logins,
      activeUsers
    });
    
  } catch (error) {
    logger.error('Error getting user activity:', error);
    return res.status(500).json({ error: 'Failed to get user activity data' });
  }
};

/**
 * Get booking activity data
 */
export const getBookingActivity = async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    
    // Get daily bookings
    const startDate = subDays(new Date(), days);
    const formattedStartDate = startDate.toISOString();
    
    // Get all reservations created after the start date
    const recentBookings = await db
      .select({
        createdAt: reservations.createdAt,
        userId: reservations.userId,
      })
      .from(reservations)
      .where(sql`${reservations.createdAt} >= ${formattedStartDate}`)
      .orderBy(reservations.createdAt);
    
    // Group by date to get counts
    const bookingsByDay = recentBookings.reduce((acc, booking) => {
      // Make sure we have a valid date or use current date as fallback
      const dateToFormat = booking.createdAt ? new Date(booking.createdAt) : new Date();
      const date = format(dateToFormat, 'yyyy-MM-dd');
      
      if (!acc[date]) {
        acc[date] = 0;
      }
      
      acc[date]++;
      return acc;
    }, {} as Record<string, number>);
    
    // Generate date range for the requested days
    const dateRange = Array.from({ length: days }, (_, i) => {
      const date = format(subDays(new Date(), days - i - 1), 'yyyy-MM-dd');
      return {
        date,
        count: bookingsByDay[date] || 0
      };
    });
    
    // For membership type breakdown, we need to join with users table
    // Get all users with bookings
    const userIds = [...new Set(recentBookings.map(booking => booking.userId))];
    
    // If there are no bookings, return empty data
    if (userIds.length === 0) {
      return res.status(200).json({
        bookingsByDay: dateRange,
        bookingsByMembershipType: []
      });
    }
    
    // Get membership types for users who made bookings
    const bookingUsers = await db
      .select({
        id: users.id,
        membershipType: users.membershipType
      })
      .from(users)
      .where(sql`${users.id} IN (${userIds.join(',')})`);
    
    // Map user IDs to membership types
    const userMembershipTypes: Record<number, string> = {};
    bookingUsers.forEach(user => {
      userMembershipTypes[user.id] = user.membershipType || 'standard';
    });
    
    // Count bookings by membership type
    const membershipCounts: Record<string, number> = {};
    recentBookings.forEach(booking => {
      const membershipType = userMembershipTypes[booking.userId] || 'standard';
      
      if (!membershipCounts[membershipType]) {
        membershipCounts[membershipType] = 0;
      }
      
      membershipCounts[membershipType]++;
    });
    
    // Format membership type data
    const bookingsByMembershipType = Object.entries(membershipCounts).map(
      ([membershipType, count]) => ({ membershipType, count })
    );
    
    return res.status(200).json({
      bookingsByDay: dateRange,
      bookingsByMembershipType
    });
    
  } catch (error) {
    logger.error('Error getting booking activity:', error);
    return res.status(500).json({ error: 'Failed to get booking activity data' });
  }
};

/**
 * Get search activity data
 */
export const getSearchActivity = async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    
    // Get search activity from the searchLogs table
    const startDate = subDays(new Date(), days);
    const formattedStartDate = startDate.toISOString();
    
    // Get all searches from the search_logs table
    const recentSearches = await db
      .select({
        createdAt: searchLogs.createdAt,
        locationName: searchLogs.locationName,
        successful: searchLogs.successful
      })
      .from(searchLogs)
      .where(sql`${searchLogs.createdAt} >= ${formattedStartDate}`)
      .orderBy(searchLogs.createdAt);
    
    // Group by date
    const searchesByDayMap = recentSearches.reduce((acc, search) => {
      // Make sure we have a valid date or use current date as fallback
      const dateToFormat = search.createdAt ? new Date(search.createdAt) : new Date();
      const date = format(dateToFormat, 'yyyy-MM-dd');
      
      if (!acc[date]) {
        acc[date] = 0;
      }
      
      // Only count successful searches
      if (search.successful) {
        acc[date]++;
      }
      
      return acc;
    }, {} as Record<string, number>);
    
    // Convert to array format for the frontend
    const searchesByDay = Array.from({ length: days }, (_, i) => {
      const date = format(subDays(new Date(), days - i - 1), 'yyyy-MM-dd');
      return {
        date,
        count: searchesByDayMap[date] || 0
      };
    });
    
    // Count searches by location for top locations
    const locationCounts: Record<string, number> = {};
    recentSearches.forEach(search => {
      if (search.locationName && search.successful) {
        const locationName = search.locationName;
        if (!locationCounts[locationName]) {
          locationCounts[locationName] = 0;
        }
        locationCounts[locationName]++;
      }
    });
    
    // Convert to array and sort by count
    const topLocations = Object.entries(locationCounts)
      .map(([location, count]) => ({ location, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Get top 5 locations
    
    // If we don't have enough data, fall back to common locations
    if (topLocations.length === 0) {
      // Get locations from the properties table as fallback
      const popularLocations = await db
        .select({
          city: properties.city,
          count: sql<number>`count(*)`.as('count')
        })
        .from(properties)
        .groupBy(properties.city)
        .orderBy(sql`count(*) DESC`)
        .limit(5);
      
      topLocations.push(...popularLocations.map(loc => ({
        location: loc.city,
        count: loc.count
      })));
    }
    
    return res.status(200).json({
      searchesByDay,
      topLocations
    });
    
  } catch (error) {
    logger.error('Error getting search activity:', error);
    return res.status(500).json({ error: 'Failed to get search activity data' });
  }
};

/**
 * Get all analytics data for admin dashboard
 */
export const getAllAnalytics = async (req: Request, res: Response) => {
  try {
    // Create a mock response object that captures returned data
    interface MockResponseData {
      data: any;
    }
    
    // Create a function that generates a mock response
    function createMockResponse(): { mockRes: Response, dataPromise: Promise<any> } {
      let resolveData: (value: any) => void;
      const dataPromise = new Promise<any>((resolve) => {
        resolveData = resolve;
      });
      
      const mockRes = {
        status: () => ({ 
          json: (data: any) => {
            resolveData(data);
            return mockRes;
          } 
        }),
      } as unknown as Response;
      
      return { mockRes, dataPromise };
    }
    
    // Generate mock responses for each endpoint
    const { mockRes: overviewMockRes, dataPromise: overviewDataPromise } = createMockResponse();
    const { mockRes: userActivityMockRes, dataPromise: userActivityDataPromise } = createMockResponse();
    const { mockRes: bookingActivityMockRes, dataPromise: bookingActivityDataPromise } = createMockResponse();
    const { mockRes: searchActivityMockRes, dataPromise: searchActivityDataPromise } = createMockResponse();
    
    // Call the endpoints with mock responses
    getOverviewMetrics(req, overviewMockRes);
    getUserActivity(req, userActivityMockRes);
    getBookingActivity(req, bookingActivityMockRes);
    getSearchActivity(req, searchActivityMockRes);
    
    // Resolve all promises
    const [overviewData, userActivityData, bookingActivityData, searchActivityData] = 
      await Promise.all([
        overviewDataPromise,
        userActivityDataPromise,
        bookingActivityDataPromise,
        searchActivityDataPromise
      ]);
    
    // Format the combined data
    const analyticsData = {
      overview: {
        totalUsers: overviewData.usersCount,
        totalProperties: overviewData.propertiesCount,
        totalSearches: overviewData.searchesCount,
        totalPageViews: overviewData.pageViewsCount,
        totalPropertyViews: overviewData.propertyViewsCount,
        totalBookings: overviewData.bookingsCount,
        bookingConversionRate: overviewData.bookingConversionRate,
        propertyToBookingRate: overviewData.propertyToBookingRate
      },
      userActivity: userActivityData,
      bookingActivity: bookingActivityData,
      searchActivity: searchActivityData,
    };
    
    return res.status(200).json(analyticsData);
    
  } catch (error) {
    logger.error('Error getting analytics data:', error);
    return res.status(500).json({ error: 'Failed to get analytics data' });
  }
};