import { Request, Response } from 'express';
import { db } from '../../db';
import { promoCodes } from '../../db/schema';
import { eq, desc, and, gte } from 'drizzle-orm';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { scrypt, randomBytes, timingSafeEqual } from 'crypto';
import { promisify } from 'util';
import logger from '../utils/logger';

const scryptAsync = promisify(scrypt);

/**
 * Hash a password with a randomly generated salt
 */
async function hashPassword(password: string) {
  const salt = randomBytes(16).toString('hex');
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString('hex')}.${salt}`;
}

/**
 * User Controllers
 */
export const getUsers = async (req: Request, res: Response) => {
  try {
    // Import users table from schema
    const { users } = await import("@db/schema");
    
    // Use the imported table for query and ordering
    const userList = await db.query.users.findMany({
      orderBy: [desc(users.createdAt)]
    });

    // Don't return password hashes
    const sanitizedUsers = userList.map(user => {
      const { password, ...sanitizedUser } = user;
      return sanitizedUser;
    });

    return res.status(200).json(sanitizedUsers);
  } catch (error) {
    logger.error('Error fetching users:', error);
    return res.status(500).json({ error: 'Failed to fetch users' });
  }
};

// Create user schema
const createUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  isAdmin: z.boolean().optional().default(false),
  isVerified: z.boolean().optional().default(true),
  membershipType: z.string().optional().default('standard'),
});

export const createUser = async (req: Request, res: Response) => {
  try {
    const validatedData = createUserSchema.parse(req.body);
    
    // Import users table from schema
    const { users } = await import("@db/schema");
    
    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, validatedData.email)
    });

    if (existingUser) {
      return res.status(400).json({ error: 'Email already registered' });
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);

    // Create user
    const [newUser] = await db.insert(users).values({
      email: validatedData.email,
      password: hashedPassword,
      isAdmin: validatedData.isAdmin,
      isVerified: validatedData.isVerified,
      membershipType: validatedData.membershipType,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Don't return password in response
    const { password, ...userWithoutPassword } = newUser;

    return res.status(201).json(userWithoutPassword);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    logger.error('Error creating user:', error);
    return res.status(500).json({ error: 'Failed to create user' });
  }
};

// Update user schema
const updateUserSchema = z.object({
  email: z.string().email('Invalid email address').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  isAdmin: z.boolean().optional(),
  isVerified: z.boolean().optional(),
  membershipType: z.string().optional(),
});

export const updateUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const validatedData = updateUserSchema.parse(req.body);
    
    // Import users table from schema
    const { users } = await import("@db/schema");

    // Check if user exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, Number(id))
    });

    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prepare update data
    const updateData: Partial<typeof users.$inferInsert> = {
      updatedAt: new Date(),
    };

    if (validatedData.email) updateData.email = validatedData.email;
    if (validatedData.isAdmin !== undefined) updateData.isAdmin = validatedData.isAdmin;
    if (validatedData.isVerified !== undefined) updateData.isVerified = validatedData.isVerified;
    if (validatedData.membershipType) updateData.membershipType = validatedData.membershipType;
    
    // If password is provided, hash it
    if (validatedData.password) {
      updateData.password = await hashPassword(validatedData.password);
    }

    // Update user
    const [updatedUser] = await db
      .update(users)
      .set(updateData)
      .where(eq(users.id, Number(id)))
      .returning();

    // Don't return password in response
    const { password, ...userWithoutPassword } = updatedUser;

    return res.status(200).json(userWithoutPassword);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    logger.error('Error updating user:', error);
    return res.status(500).json({ error: 'Failed to update user' });
  }
};

export const deleteUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Import users table from schema
    const { users } = await import("@db/schema");

    // Check if user exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.id, Number(id))
    });

    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Delete user
    await db.delete(users).where(eq(users.id, Number(id)));

    return res.status(200).json({ message: 'User deleted successfully' });
  } catch (error) {
    logger.error('Error deleting user:', error);
    return res.status(500).json({ error: 'Failed to delete user' });
  }
};

/**
 * Promo Code Controllers
 */

export const getPromoCodes = async (req: Request, res: Response) => {
  try {
    const codes = await db.query.promoCodes.findMany({
      orderBy: [desc(promoCodes.createdAt)]
    });

    return res.status(200).json(codes);
  } catch (error) {
    logger.error('Error fetching promo codes:', error);
    return res.status(500).json({ error: 'Failed to fetch promo codes' });
  }
};

// Create promo code schema
const createPromoCodeSchema = z.object({
  code: z.string().min(4).max(20),
  description: z.string().min(4),
  membershipType: z.string().default('standard'),
  discountPercent: z.number().min(0).max(100).default(0),
  maxUsages: z.number().min(1).default(1),
  isActive: z.boolean().default(true),
  expiresAt: z.string().optional(),
});

export const createPromoCode = async (req: Request, res: Response) => {
  try {
    const validatedData = createPromoCodeSchema.parse(req.body);
    
    // Check if promo code already exists
    const existingCode = await db.query.promoCodes.findFirst({
      where: eq(promoCodes.code, validatedData.code)
    });

    if (existingCode) {
      return res.status(400).json({ error: 'Promo code already exists' });
    }

    // Parse expiration date if provided
    let expiresAt: Date | null = null;
    if (validatedData.expiresAt) {
      expiresAt = new Date(validatedData.expiresAt);
    }

    // Create promo code
    const [newPromoCode] = await db.insert(promoCodes).values({
      code: validatedData.code,
      description: validatedData.description,
      membershipType: validatedData.membershipType,
      discountPercent: validatedData.discountPercent,
      maxUsages: validatedData.maxUsages,
      usageCount: 0,
      isActive: validatedData.isActive,
      createdAt: new Date(),
      updatedAt: new Date(),
      expiresAt,
      createdBy: req.user?.id || 0,
    }).returning();

    return res.status(201).json(newPromoCode);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    logger.error('Error creating promo code:', error);
    return res.status(500).json({ error: 'Failed to create promo code' });
  }
};

// Update promo code schema
const updatePromoCodeSchema = z.object({
  code: z.string().min(4).max(20).optional(),
  description: z.string().min(4).optional(),
  membershipType: z.string().optional(),
  discountPercent: z.number().min(0).max(100).optional(),
  maxUsages: z.number().min(1).optional(),
  isActive: z.boolean().optional(),
  expiresAt: z.string().optional().nullable(),
});

export const updatePromoCode = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const validatedData = updatePromoCodeSchema.parse(req.body);

    // Check if promo code exists
    const existingCode = await db.query.promoCodes.findFirst({
      where: eq(promoCodes.id, Number(id))
    });

    if (!existingCode) {
      return res.status(404).json({ error: 'Promo code not found' });
    }

    // If updating the code value, check if the new code already exists
    if (validatedData.code && validatedData.code !== existingCode.code) {
      const duplicateCode = await db.query.promoCodes.findFirst({
        where: eq(promoCodes.code, validatedData.code)
      });

      if (duplicateCode) {
        return res.status(400).json({ error: 'Promo code already exists' });
      }
    }

    // Prepare update data
    const updateData: Partial<typeof promoCodes.$inferInsert> = {
      updatedAt: new Date(),
    };

    if (validatedData.code) updateData.code = validatedData.code;
    if (validatedData.description) updateData.description = validatedData.description;
    if (validatedData.membershipType) updateData.membershipType = validatedData.membershipType;
    if (validatedData.discountPercent !== undefined) updateData.discountPercent = validatedData.discountPercent;
    if (validatedData.maxUsages !== undefined) updateData.maxUsages = validatedData.maxUsages;
    if (validatedData.isActive !== undefined) updateData.isActive = validatedData.isActive;
    
    // Parse expiration date if provided
    if (validatedData.expiresAt === null) {
      updateData.expiresAt = null;
    } else if (validatedData.expiresAt) {
      updateData.expiresAt = new Date(validatedData.expiresAt);
    }

    // Update promo code
    const [updatedPromoCode] = await db
      .update(promoCodes)
      .set(updateData)
      .where(eq(promoCodes.id, Number(id)))
      .returning();

    return res.status(200).json(updatedPromoCode);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    logger.error('Error updating promo code:', error);
    return res.status(500).json({ error: 'Failed to update promo code' });
  }
};

export const togglePromoCodeStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if promo code exists
    const existingCode = await db.query.promoCodes.findFirst({
      where: eq(promoCodes.id, Number(id))
    });

    if (!existingCode) {
      return res.status(404).json({ error: 'Promo code not found' });
    }

    // Toggle status
    const [updatedPromoCode] = await db
      .update(promoCodes)
      .set({
        isActive: !existingCode.isActive,
        updatedAt: new Date(),
      })
      .where(eq(promoCodes.id, Number(id)))
      .returning();

    return res.status(200).json(updatedPromoCode);
  } catch (error) {
    logger.error('Error toggling promo code status:', error);
    return res.status(500).json({ error: 'Failed to toggle promo code status' });
  }
};

export const deletePromoCode = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if promo code exists
    const existingCode = await db.query.promoCodes.findFirst({
      where: eq(promoCodes.id, Number(id))
    });

    if (!existingCode) {
      return res.status(404).json({ error: 'Promo code not found' });
    }

    // Delete promo code
    await db.delete(promoCodes).where(eq(promoCodes.id, Number(id)));

    return res.status(200).json({ message: 'Promo code deleted successfully' });
  } catch (error) {
    logger.error('Error deleting promo code:', error);
    return res.status(500).json({ error: 'Failed to delete promo code' });
  }
};

/**
 * Generate a test promo code - useful for development and testing
 */
export const generateTestPromoCode = async (req: Request, res: Response) => {
  try {
    // Generate a random code
    const randomCode = `TEST${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    
    // Set expiration date to 7 days from now
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);
    
    // Create test promo code
    const [testPromoCode] = await db.insert(promoCodes).values({
      code: randomCode,
      description: 'Test Promo Code',
      membershipType: 'standard',
      discountPercent: 10,
      maxUsages: 1,
      usageCount: 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      expiresAt,
      createdBy: req.user?.id || 0,
    }).returning();

    return res.status(201).json(testPromoCode);
  } catch (error) {
    logger.error('Error generating test promo code:', error);
    return res.status(500).json({ error: 'Failed to generate test promo code' });
  }
};

/**
 * Validate a promo code
 */
export const validatePromoCode = async (req: Request, res: Response) => {
  try {
    const { code } = req.params;
    
    if (!code) {
      return res.status(400).json({ error: 'Promo code is required' });
    }

    // Find the promo code
    const promoCode = await db.query.promoCodes.findFirst({
      where: eq(promoCodes.code, code.toUpperCase())
    });

    if (!promoCode) {
      return res.status(404).json({ 
        valid: false,
        error: 'Invalid promo code' 
      });
    }

    // Check if the code is active
    if (!promoCode.isActive) {
      return res.status(400).json({ 
        valid: false,
        error: 'Promo code is inactive' 
      });
    }

    // Check if the code has reached its usage limit
    if (promoCode.usageCount !== null && promoCode.maxUsages !== null && 
        promoCode.usageCount >= promoCode.maxUsages) {
      return res.status(400).json({ 
        valid: false,
        error: 'Promo code has reached its usage limit' 
      });
    }

    // Check if the code has expired
    if (promoCode.expiresAt && new Date(promoCode.expiresAt) < new Date()) {
      return res.status(400).json({ 
        valid: false,
        error: 'Promo code has expired' 
      });
    }

    // Code is valid
    return res.status(200).json({ 
      valid: true, 
      promoCode: {
        code: promoCode.code,
        membershipType: promoCode.membershipType,
        discountPercent: promoCode.discountPercent,
      }
    });
  } catch (error) {
    logger.error('Error validating promo code:', error);
    return res.status(500).json({ 
      valid: false,
      error: 'Failed to validate promo code' 
    });
  }
};

/**
 * Apply a promo code (increment usage count)
 */
export const applyPromoCode = async (req: Request, res: Response) => {
  try {
    const { code } = req.params;
    
    if (!code) {
      return res.status(400).json({ error: 'Promo code is required' });
    }

    // Find the promo code
    const promoCode = await db.query.promoCodes.findFirst({
      where: eq(promoCodes.code, code.toUpperCase())
    });

    if (!promoCode) {
      return res.status(404).json({ error: 'Invalid promo code' });
    }

    // Check if the code is active
    if (!promoCode.isActive) {
      return res.status(400).json({ error: 'Promo code is inactive' });
    }

    // Check if the code has reached its usage limit
    if (promoCode.usageCount !== null && promoCode.maxUsages !== null && 
        promoCode.usageCount >= promoCode.maxUsages) {
      return res.status(400).json({ error: 'Promo code has reached its usage limit' });
    }

    // Check if the code has expired
    if (promoCode.expiresAt && new Date(promoCode.expiresAt) < new Date()) {
      return res.status(400).json({ error: 'Promo code has expired' });
    }

    // Increment usage count
    const [updatedPromoCode] = await db
      .update(promoCodes)
      .set({
        usageCount: (promoCode.usageCount !== null ? promoCode.usageCount + 1 : 1),
        updatedAt: new Date(),
      })
      .where(eq(promoCodes.id, promoCode.id))
      .returning();

    return res.status(200).json({
      success: true,
      promoCode: {
        code: updatedPromoCode.code,
        membershipType: updatedPromoCode.membershipType,
        discountPercent: updatedPromoCode.discountPercent,
        usageCount: updatedPromoCode.usageCount,
        maxUsages: updatedPromoCode.maxUsages,
      }
    });
  } catch (error) {
    logger.error('Error applying promo code:', error);
    return res.status(500).json({ error: 'Failed to apply promo code' });
  }
};