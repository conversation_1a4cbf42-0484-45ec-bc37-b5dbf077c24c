import { Request, Response } from 'express';
import { spawn } from 'child_process';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import { db } from '../../db';
import { testResults } from '../../db/schema';
import { Socket } from 'socket.io';

// Cache to store active test processes
const activeTestRuns = new Map<string, {
  process: ReturnType<typeof spawn>,
  logs: string[],
  status: 'running' | 'completed' | 'failed',
  startTime: number,
  endTime?: number,
  testType: string,
  summary?: {
    total: number,
    passed: number,
    failed: number,
    skipped: number
  }
}>();

// Socket instance for real-time updates
let io: Socket | null = null;

// Set the socket.io instance
export const setSocketIO = (socketIO: any) => {
  io = socketIO;
};

/**
 * Start a test run
 */
export const startTestRun = async (req: Request, res: Response) => {
  try {
    // Validate request
    const { testType, options } = req.body;
    
    // Only allow specific test types
    const validTestTypes = ['unit', 'functional', 'react', 'all'];
    if (!validTestTypes.includes(testType)) {
      return res.status(400).json({
        success: false,
        message: `Invalid test type. Must be one of: ${validTestTypes.join(', ')}`
      });
    }
    
    // Generate a unique ID for this test run
    const runId = uuidv4();
    
    // Determine the command to run based on test type
    let command: string;
    let args: string[] = [];
    
    if (testType === 'all') {
      // Run the complete test suite
      command = 'bash';
      args = ['./run-tests.sh'];
    } else {
      // Run a specific test suite
      command = 'npx';
      args = ['jest', `--config=jest.config.${testType}.ts`];
      
      // Add specific test file if provided
      if (options?.testFile) {
        args.push(options.testFile);
      }
    }
    
    logger.info(`Starting test run: ${testType}`, {
      testType,
      runId,
      command,
      args
    });
    
    // Spawn the test process
    const testProcess = spawn(command, args, {
      cwd: process.cwd(),
      env: { ...process.env, FORCE_COLOR: 'true' }
    });
    
    // Store the active test run
    activeTestRuns.set(runId, {
      process: testProcess,
      logs: [],
      status: 'running',
      startTime: Date.now(),
      testType
    });
    
    // Handle test process output
    testProcess.stdout.on('data', (data) => {
      const output = data.toString();
      const testRun = activeTestRuns.get(runId);
      
      if (testRun) {
        testRun.logs.push(output);
        
        // Try to extract test summary if it contains the summary data
        if (output.includes('TEST RUN SUMMARY')) {
          const summaryMatch = output.match(/Total Test Suites: (\d+)\s+Passed: (\d+)\s+Failed: (\d+)\s+Skipped: (\d+)/s);
          if (summaryMatch) {
            testRun.summary = {
              total: parseInt(summaryMatch[1], 10),
              passed: parseInt(summaryMatch[2], 10),
              failed: parseInt(summaryMatch[3], 10),
              skipped: parseInt(summaryMatch[4], 10)
            };
          }
        }
        
        // Emit progress update via socket.io if available
        if (io) {
          io.emit('test-progress', {
            runId,
            log: output,
            status: testRun.status
          });
        }
      }
    });
    
    testProcess.stderr.on('data', (data) => {
      const output = data.toString();
      const testRun = activeTestRuns.get(runId);
      
      if (testRun) {
        testRun.logs.push(output);
        
        // Emit progress update via socket.io if available
        if (io) {
          io.emit('test-progress', {
            runId,
            log: output,
            status: testRun.status
          });
        }
      }
    });
    
    // Handle test process completion
    testProcess.on('close', async (code) => {
      const testRun = activeTestRuns.get(runId);
      
      if (testRun) {
        testRun.status = code === 0 ? 'completed' : 'failed';
        testRun.endTime = Date.now();
        
        // Calculate duration
        const duration = testRun.endTime - testRun.startTime;
        
        // Emit completion event via socket.io if available
        if (io) {
          io.emit('test-complete', {
            runId,
            status: testRun.status,
            duration,
            summary: testRun.summary,
            exitCode: code
          });
        }
        
        // Store test run results in the database
        try {
          await db.insert(testResults).values({
            testType: testRun.testType,
            query: `Test run: ${testRun.testType}`,
            sessionId: runId,
            timestamp: new Date(testRun.startTime),
            duration: duration,
            success: testRun.status === 'completed',
            userId: req.user?.id,
            metrics: JSON.stringify({
              totalTests: testRun.summary?.total || 0,
              passedTests: testRun.summary?.passed || 0,
              failedTests: testRun.summary?.failed || 0,
              skippedTests: testRun.summary?.skipped || 0,
              duration: duration
            }),
            context: JSON.stringify({
              testType: testRun.testType,
              commandLine: `${command} ${args.join(' ')}`
            }),
            results: JSON.stringify({
              logs: testRun.logs.join(''),
              exitCode: code
            }),
            error: testRun.status === 'failed' ? 'Test execution failed' : null
          });
        } catch (error) {
          logger.error('Failed to store test results in database', {
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    });
    
    // Return the test run ID immediately
    return res.status(200).json({
      success: true,
      runId,
      message: 'Test run started successfully'
    });
  } catch (error) {
    logger.error('Failed to start test run', {
      error: error instanceof Error ? error.message : String(error)
    });
    
    return res.status(500).json({
      success: false,
      message: 'Failed to start test run',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Get the status of a specific test run
 */
export const getTestRunStatus = (req: Request, res: Response) => {
  try {
    const { runId } = req.params;
    
    if (!runId) {
      return res.status(400).json({
        success: false,
        message: 'Run ID is required'
      });
    }
    
    const testRun = activeTestRuns.get(runId);
    
    if (!testRun) {
      return res.status(404).json({
        success: false,
        message: 'Test run not found'
      });
    }
    
    // Calculate duration
    const duration = testRun.endTime ? 
      testRun.endTime - testRun.startTime : 
      Date.now() - testRun.startTime;
    
    return res.status(200).json({
      success: true,
      status: testRun.status,
      logs: testRun.logs,
      duration,
      summary: testRun.summary,
      testType: testRun.testType
    });
  } catch (error) {
    logger.error('Failed to get test run status', {
      error: error instanceof Error ? error.message : String(error)
    });
    
    return res.status(500).json({
      success: false,
      message: 'Failed to get test run status',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * List all active test runs
 */
export const listTestRuns = (req: Request, res: Response) => {
  try {
    const runs = Array.from(activeTestRuns.entries()).map(([runId, run]) => {
      // Calculate duration
      const duration = run.endTime ? 
        run.endTime - run.startTime : 
        Date.now() - run.startTime;
      
      return {
        runId,
        status: run.status,
        duration,
        startTime: run.startTime,
        endTime: run.endTime,
        testType: run.testType,
        summary: run.summary
      };
    });
    
    return res.status(200).json({
      success: true,
      runs
    });
  } catch (error) {
    logger.error('Failed to list test runs', {
      error: error instanceof Error ? error.message : String(error)
    });
    
    return res.status(500).json({
      success: false,
      message: 'Failed to list test runs',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Stop a running test
 */
export const stopTestRun = (req: Request, res: Response) => {
  try {
    const { runId } = req.params;
    
    if (!runId) {
      return res.status(400).json({
        success: false,
        message: 'Run ID is required'
      });
    }
    
    const testRun = activeTestRuns.get(runId);
    
    if (!testRun) {
      return res.status(404).json({
        success: false,
        message: 'Test run not found'
      });
    }
    
    // Kill the test process
    if (testRun.status === 'running') {
      testRun.process.kill('SIGTERM');
      testRun.status = 'failed';
      testRun.endTime = Date.now();
      
      // Emit event via socket.io if available
      if (io) {
        io.emit('test-complete', {
          runId,
          status: 'failed',
          duration: testRun.endTime - testRun.startTime,
          summary: testRun.summary,
          exitCode: -1
        });
      }
    }
    
    return res.status(200).json({
      success: true,
      message: 'Test run stopped successfully'
    });
  } catch (error) {
    logger.error('Failed to stop test run', {
      error: error instanceof Error ? error.message : String(error)
    });
    
    return res.status(500).json({
      success: false,
      message: 'Failed to stop test run',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

/**
 * Cleanup old test runs (older than 1 hour)
 */
export const cleanupTestRuns = () => {
  const now = Date.now();
  const ONE_HOUR = 60 * 60 * 1000;
  
  for (const [runId, run] of activeTestRuns.entries()) {
    // If the run has ended and it's older than 1 hour, remove it
    if (run.endTime && now - run.endTime > ONE_HOUR) {
      activeTestRuns.delete(runId);
    }
    
    // If the run is still running but started more than 1 hour ago, kill it and remove it
    if (run.status === 'running' && now - run.startTime > ONE_HOUR) {
      run.process.kill('SIGTERM');
      activeTestRuns.delete(runId);
    }
  }
};