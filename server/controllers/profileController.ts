/**
 * User Profile Controller
 * 
 * Handles user profile management operations including:
 * - Getting profile data
 * - Updating profile information
 * - Managing preferences
 */

import { Request, Response } from 'express';
import { db } from '../../db';
import { users } from '../../db/schema';
import { eq } from 'drizzle-orm';
import logger from '../utils/logger';
import { comparePasswords, hashPassword } from '../auth';

/**
 * Get the current user's profile
 */
export const getProfile = async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated() || !req.user) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const userId = req.user.id;

    // Get user with full profile data
    const [userProfile] = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        phoneNumber: users.phoneNumber,
        profileImageUrl: users.profileImageUrl,
        addressLine1: users.addressLine1,
        addressLine2: users.addressLine2,
        city: users.city,
        state: users.state,
        postalCode: users.postalCode,
        country: users.country,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        membershipType: users.membershipType,
        lastLoginAt: users.lastLoginAt,
        preferences: users.preferences
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!userProfile) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.status(200).json(userProfile);
  } catch (error) {
    logger.error('Error getting user profile:', error);
    return res.status(500).json({ error: 'Failed to get profile' });
  }
};

/**
 * Update the current user's profile
 */
export const updateProfile = async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated() || !req.user) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const userId = req.user.id;
    const {
      firstName,
      lastName,
      phoneNumber,
      profileImageUrl,
      addressLine1,
      addressLine2,
      city,
      state,
      postalCode,
      country
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName) {
      return res.status(400).json({ error: 'First name and last name are required' });
    }

    // Update user profile
    const [updatedUser] = await db
      .update(users)
      .set({
        firstName,
        lastName,
        phoneNumber,
        profileImageUrl,
        addressLine1,
        addressLine2,
        city,
        state,
        postalCode,
        country,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId))
      .returning();

    if (!updatedUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Include profile fields but exclude sensitive data
    const userProfile = {
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.firstName,
      lastName: updatedUser.lastName,
      phoneNumber: updatedUser.phoneNumber,
      profileImageUrl: updatedUser.profileImageUrl,
      addressLine1: updatedUser.addressLine1,
      addressLine2: updatedUser.addressLine2,
      city: updatedUser.city,
      state: updatedUser.state,
      postalCode: updatedUser.postalCode,
      country: updatedUser.country,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      membershipType: updatedUser.membershipType,
      lastLoginAt: updatedUser.lastLoginAt,
      preferences: updatedUser.preferences
    };

    return res.status(200).json(userProfile);
  } catch (error) {
    logger.error('Error updating user profile:', error);
    return res.status(500).json({ error: 'Failed to update profile' });
  }
};

/**
 * Update the current user's preferences
 */
export const updatePreferences = async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated() || !req.user) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const userId = req.user.id;
    const { preferences } = req.body;

    // Validate preferences
    if (!preferences || typeof preferences !== 'object') {
      return res.status(400).json({ error: 'Invalid preferences format' });
    }

    // Get current user to merge preferences
    const [currentUser] = await db
      .select({
        preferences: users.preferences
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Merge existing preferences with new ones
    const currentPrefs = currentUser.preferences || {};
    const updatedPrefs = { ...currentPrefs, ...preferences };

    // Update user preferences
    const [updatedUser] = await db
      .update(users)
      .set({
        preferences: updatedPrefs,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId))
      .returning();

    return res.status(200).json({
      success: true,
      preferences: updatedUser.preferences
    });
  } catch (error) {
    logger.error('Error updating user preferences:', error);
    return res.status(500).json({ error: 'Failed to update preferences' });
  }
};

/**
 * Change user email
 */
export const changeEmail = async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated() || !req.user) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const userId = req.user.id;
    const { email, password } = req.body;

    // Validate required fields
    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Verify password (you should use your authentication service for this)
    // For security, require password verification when changing email
    if (!password) {
      return res.status(400).json({ error: 'Password is required to change email' });
    }

    // Check if email already exists
    const [existingUser] = await db
      .select({
        id: users.id
      })
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (existingUser) {
      return res.status(400).json({ error: 'Email already in use' });
    }

    // Use the imported comparePasswords function
    
    // Get current user with password
    const [currentUser] = await db
      .select({
        password: users.password
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify password
    const isPasswordValid = await comparePasswords(password, currentUser.password);
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid password' });
    }

    // Update email
    await db
      .update(users)
      .set({
        email,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));

    return res.status(200).json({
      success: true,
      message: 'Email updated successfully'
    });
  } catch (error) {
    logger.error('Error changing user email:', error);
    return res.status(500).json({ error: 'Failed to change email' });
  }
};

/**
 * Change user password
 */
export const changePassword = async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated() || !req.user) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current password and new password are required' });
    }

    // Use the imported auth functions
    
    // Get current user with password
    const [currentUser] = await db
      .select({
        password: users.password
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify current password
    const isPasswordValid = await comparePasswords(currentPassword, currentUser.password);
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Current password is incorrect' });
    }

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);

    // Update password
    await db
      .update(users)
      .set({
        password: hashedPassword,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));

    return res.status(200).json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    logger.error('Error changing user password:', error);
    return res.status(500).json({ error: 'Failed to change password' });
  }
};

export default {
  getProfile,
  updateProfile,
  updatePreferences,
  changeEmail,
  changePassword
};