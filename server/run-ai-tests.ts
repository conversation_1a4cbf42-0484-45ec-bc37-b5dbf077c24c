/**
 * AI Testing Script
 * 
 * This script runs comprehensive tests to validate AI functionality
 * focusing on the most common issues with AI integration:
 * 1. Location detection
 * 2. Property recommendations
 * 3. Response formatting
 */

import fetch from 'node-fetch';
import * as streamParser from './utils/streamParser';
import { log } from './vite';

// Test configurations
const API_URL = 'http://localhost:5000/api/chat';
const TIMEOUT = 30000; // 30 seconds per test

// Console formatting
const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test cases for location detection
const LOCATION_TESTS = [
  {
    description: 'Direct location query',
    message: 'I want to find hotels in Miami Beach',
    shouldDetectLocation: true,
    expectedLocation: 'Miami Beach'
  },
  {
    description: 'Location with landmark',
    message: 'Are there any hotels near Central Park in New York?',
    shouldDetectLocation: true,
    expectedLocation: 'Central Park'
  },
  {
    description: 'Multiple locations in query',
    message: 'I\'m traveling from San Francisco to Los Angeles, need hotels in LA',
    shouldDetectLocation: true,
    expectedLocation: 'Los Angeles'
  }
];

// Test cases for property recommendations
const PROPERTY_TESTS = [
  {
    description: 'Basic property recommendation',
    message: 'Can you recommend a hotel in Miami Beach for next weekend?',
    shouldRecommendProperties: true
  },
  {
    description: 'Specific amenities recommendation',
    message: 'I need a hotel in New York with a pool and free breakfast',
    shouldRecommendProperties: true
  }
];

/**
 * Send a test message to the chat API and analyze the response
 */
async function testChatMessage(message: string): Promise<{
  success: boolean;
  textResponses: string[];
  locationDetected: boolean;
  location?: { name: string; lat: number; lng: number; placeType?: string };
  propertiesRecommended: boolean;
  properties: any[];
  error?: string;
}> {
  const sessionId = `test-${Date.now()}`;
  const textResponses: string[] = [];
  let location = undefined;
  let locationDetected = false;
  let properties: any[] = [];
  let propertiesRecommended = false;
  
  try {
    console.log(`${COLORS.bright}${COLORS.blue}Testing message:${COLORS.reset} "${message}"`);
    
    // Create an abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        message, 
        sessionId,
        extractLocation: true // Add the extractLocation flag to enable location detection
      }),
      signal: controller.signal
    });
    
    // Clear the timeout
    clearTimeout(timeoutId);

    if (!response.ok || !response.body) {
      return { 
        success: false,
        locationDetected: false,
        propertiesRecommended: false,
        textResponses: [],
        properties: [],
        error: `API request failed with status ${response.status}` 
      };
    }

    // Handle the response text directly since Node.js streams work differently
    const responseText = await response.text();
    
    // Process the SSE data by splitting it into chunks
    const chunks = responseText.split('\n\n')
      .filter(chunk => chunk.trim() !== '' && chunk.startsWith('data:'))
      .map(chunk => chunk.replace(/^data: /, '').trim());
      
    // Process each chunk
    for (const chunk of chunks) {
      if (!chunk.trim()) continue;
      
      // Parse the SSE data
      const result = streamParser.safeParseSSEData(chunk);
      
      if (!result.valid || !result.parsed) continue;
      
      // Check if it's the done marker
      if (result.parsed.type === 'control' && result.parsed.action === 'done') {
        continue;
      }
      
      // Handle different response types
      if (result.parsed.type === 'location') {
        location = result.parsed.data;
        locationDetected = true;
        console.log(`${COLORS.green}Location detected:${COLORS.reset}`, location);
      } else if (result.parsed.type === 'text') {
        const text = result.parsed.data;
        textResponses.push(text);
        console.log(`${COLORS.dim}Text response:${COLORS.reset} ${text.substring(0, 50)}...`);
        
        // Check for property recommendations in text using patterns
        const propertyPatterns = [
          /\*\s+\*\*(.+?)\*\*\s+\((?:ID|id|Id)?\s*:\s*(\d+)\)/i,   // * **Hotel Name** (ID: 123)
          /\*\s+\*\*(.+?)\*\*\s+\((\d+)\)/i,                        // * **Hotel Name** (123)
          /\*\s+\*\*(.+?)\s+\((\d+)\)\*\*/i,                        // * **Hotel Name (123)**
          /\*\s+\*\*(\d+)\s*:\s*(.+?)\*\*/i,                        // * **123: Hotel Name**
          /\*\s+\*\*Hotel\s+(\d+)\*\*/i,                            // * **Hotel 123**
          /property\s+id\s*:\s*(\d+)/i,                             // property id: 123
          /hotel\s+id\s*:\s*(\d+)/i                                 // hotel id: 123
        ];
        
        // Check each pattern against the text
        for (const pattern of propertyPatterns) {
          const matches = text.match(pattern);
          if (matches) {
            propertiesRecommended = true;
            // Extract property IDs if possible
            try {
              const idMatch = matches[1].match(/\d+/);
              const id = idMatch ? parseInt(idMatch[0]) : parseInt(matches[2]);
              if (!isNaN(id) && !properties.some(p => p.id === id)) {
                properties.push({ id, name: matches[1] });
              }
            } catch (e) {
              // If ID parsing fails, just mark as recommended
            }
            // No need to check other patterns if one matches
            break;
          }
        }
      } else if (result.parsed.type === 'properties') {
        properties = result.parsed.data;
        propertiesRecommended = true;
        console.log(`${COLORS.green}Properties recommended:${COLORS.reset} ${properties.length}`);
      } else if (result.parsed.type === 'action' && 
                result.parsed.data && 
                result.parsed.data.type === 'location') {
        // Alternative location format (action)
        location = result.parsed.data.data;
        locationDetected = true;
        console.log(`${COLORS.green}Location action detected:${COLORS.reset}`, location);
      }
      
      // Final check for property recommendations based on accumulated text
      if (!propertiesRecommended && textResponses.length > 0) {
        const combinedText = textResponses.join(' ');
        // Look for mentions of hotels, properties, or accommodations in the text
        if (
          (combinedText.includes('hotel') || combinedText.includes('Hotel')) &&
          (combinedText.includes('recommended') || combinedText.includes('recommendation') || 
           combinedText.includes('option') || combinedText.includes('suggest'))
        ) {
          propertiesRecommended = true;
          console.log(`${COLORS.yellow}Implicit property recommendations detected${COLORS.reset}`);
        }
      }
    }

    return {
      success: true,
      textResponses,
      locationDetected,
      location,
      propertiesRecommended,
      properties
    };
    
  } catch (error) {
    console.error(`${COLORS.red}Error in test:${COLORS.reset}`, error);
    return {
      success: false,
      locationDetected: false,
      propertiesRecommended: false,
      textResponses: [],
      properties: [],
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Run the location detection tests
 */
async function runLocationTests() {
  console.log(`\n${COLORS.bright}${COLORS.magenta}=== LOCATION DETECTION TESTS ===${COLORS.reset}\n`);
  let passing = 0;
  let failing = 0;
  
  for (const test of LOCATION_TESTS) {
    console.log(`\n${COLORS.cyan}Test:${COLORS.reset} ${test.description}`);
    
    const startTime = Date.now();
    const result = await testChatMessage(test.message);
    const duration = Date.now() - startTime;
    
    if (!result.success) {
      console.log(`${COLORS.red}✗ FAILED${COLORS.reset} (${duration}ms) - Error: ${result.error}`);
      failing++;
      continue;
    }
    
    if (test.shouldDetectLocation && !result.locationDetected) {
      console.log(`${COLORS.red}✗ FAILED${COLORS.reset} (${duration}ms) - Expected location detection but none found`);
      failing++;
      continue;
    }
    
    if (!test.shouldDetectLocation && result.locationDetected) {
      console.log(`${COLORS.yellow}⚠ WARNING${COLORS.reset} (${duration}ms) - Detected unexpected location: ${result.location?.name}`);
      // Not counting as failure
    }
    
    if (test.shouldDetectLocation && result.locationDetected && test.expectedLocation && 
        result.location && !result.location.name.toLowerCase().includes(test.expectedLocation.toLowerCase())) {
      console.log(`${COLORS.yellow}⚠ WARNING${COLORS.reset} (${duration}ms) - Expected "${test.expectedLocation}" but got "${result.location.name}"`);
      // Not counting as failure if at least some location was detected
    }
    
    console.log(`${COLORS.green}✓ PASSED${COLORS.reset} (${duration}ms)`);
    passing++;
  }
  
  return { passing, failing };
}

/**
 * Run the property recommendation tests
 */
async function runPropertyTests() {
  console.log(`\n${COLORS.bright}${COLORS.magenta}=== PROPERTY RECOMMENDATION TESTS ===${COLORS.reset}\n`);
  let passing = 0;
  let failing = 0;
  
  for (const test of PROPERTY_TESTS) {
    console.log(`\n${COLORS.cyan}Test:${COLORS.reset} ${test.description}`);
    
    const startTime = Date.now();
    const result = await testChatMessage(test.message);
    const duration = Date.now() - startTime;
    
    if (!result.success) {
      console.log(`${COLORS.red}✗ FAILED${COLORS.reset} (${duration}ms) - Error: ${result.error}`);
      failing++;
      continue;
    }
    
    if (test.shouldRecommendProperties && !result.propertiesRecommended) {
      console.log(`${COLORS.red}✗ FAILED${COLORS.reset} (${duration}ms) - Expected property recommendations but none found`);
      failing++;
      continue;
    }
    
    if (test.shouldRecommendProperties && result.properties.length === 0) {
      console.log(`${COLORS.red}✗ FAILED${COLORS.reset} (${duration}ms) - Expected properties but got empty array`);
      failing++;
      continue;
    }
    
    console.log(`${COLORS.green}✓ PASSED${COLORS.reset} (${duration}ms)`);
    passing++;
  }
  
  return { passing, failing };
}

/**
 * Main function to run all tests
 */
async function runAllTests() {
  console.log(`\n${COLORS.bright}${COLORS.blue}Starting AI Integration Tests${COLORS.reset}\n`);
  
  // Check if server is running
  try {
    await fetch('http://localhost:5000/api/config');
  } catch (error) {
    console.error(`${COLORS.red}ERROR: Server is not running on port 5000. Please start the server first.${COLORS.reset}`);
    return;
  }
  
  // Run location tests
  const locationResults = await runLocationTests();
  
  // Run property tests
  const propertyResults = await runPropertyTests();
  
  // Final summary
  const totalTests = LOCATION_TESTS.length + PROPERTY_TESTS.length;
  const totalPassing = locationResults.passing + propertyResults.passing;
  const totalFailing = locationResults.failing + propertyResults.failing;
  
  console.log(`\n${COLORS.bright}${COLORS.magenta}=== TEST SUMMARY ===${COLORS.reset}\n`);
  console.log(`${COLORS.bright}Total Tests:${COLORS.reset} ${totalTests}`);
  console.log(`${COLORS.green}Passing:${COLORS.reset} ${totalPassing}`);
  console.log(`${COLORS.red}Failing:${COLORS.reset} ${totalFailing}`);
  console.log(`${COLORS.blue}Success Rate:${COLORS.reset} ${Math.round((totalPassing / totalTests) * 100)}%\n`);

  if (totalFailing > 0) {
    console.log(`${COLORS.yellow}AI integration issues detected. Please check the failing tests for details.${COLORS.reset}\n`);
  } else {
    console.log(`${COLORS.green}All AI integration tests passed!${COLORS.reset}\n`);
  }
}

// In ESM, we can check if this file is being executed directly
import { fileURLToPath } from 'url';

// Check if this module is being run directly
const isMainModule = process.argv[1] === fileURLToPath(import.meta.url);

if (isMainModule) {
  runAllTests().catch(error => {
    console.error(`${COLORS.red}Unhandled error:${COLORS.reset}`, error);
    process.exit(1);
  });
}

export { runAllTests, testChatMessage };