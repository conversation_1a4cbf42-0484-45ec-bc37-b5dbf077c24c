/**
 * Analytics Middleware
 * 
 * This middleware automatically tracks page views and API requests
 * for analytics purposes.
 */

import { Request, Response, NextFunction } from 'express';
import analyticsService from '../services/analyticsService';
import logger from '../utils/logger';

/**
 * Middleware to track page views
 * This should be applied to routes that serve HTML pages
 */
export const trackPageView = (req: Request, res: Response, next: NextFunction) => {
  // Only track GET requests
  if (req.method !== 'GET') {
    return next();
  }

  // Skip tracking for static assets and API routes
  if (req.path.startsWith('/api/') || 
      req.path.includes('.') || 
      req.path.startsWith('/static/') ||
      req.path.startsWith('/_next/') ||
      req.path.startsWith('/favicon.ico')) {
    return next();
  }

  try {
    // Get page title from query param or use path as default
    const pageTitle = (req.query.title as string) || `Page: ${req.path}`;
    
    // Track page view asynchronously to not block the request
    analyticsService.trackPageView(req, pageTitle).catch(err => {
      logger.error('Error tracking page view in middleware', { error: err });
    });
  } catch (error) {
    // Log error but don't interrupt the request flow
    logger.error('Error in page view tracking middleware', { error });
  }
  
  next();
};

/**
 * Middleware to track search queries
 * This should be applied to search-related API routes
 */
export const trackSearch = (req: Request, res: Response, next: NextFunction) => {
  // Capture the original response methods
  const originalSend = res.send;
  const originalJson = res.json;
  const originalEnd = res.end;
  
  // Start tracking time
  const startTime = Date.now();
  
  // Function to extract search parameters from the request
  const getSearchParams = () => {
    const params: any = {};
    
    // Extract parameters from query string or body
    const source = req.query.source || req.body.source || 'web';
    const searchQuery = req.query.q || req.body.q || req.query.query || req.body.query;
    const locationName = req.query.location || req.body.location || req.query.locationName || req.body.locationName;
    const locationType = req.query.locationType || req.body.locationType;
    const latitude = parseFloat(req.query.lat as string) || req.body.lat;
    const longitude = parseFloat(req.query.lng as string) || req.body.lng;
    const checkIn = req.query.checkIn || req.body.checkIn;
    const checkOut = req.query.checkOut || req.body.checkOut;
    const guests = parseInt(req.query.guests as string) || req.body.guests;
    const rooms = parseInt(req.query.rooms as string) || req.body.rooms;
    
    // Add all non-undefined parameters
    if (source) params.source = source;
    if (searchQuery) params.searchQuery = searchQuery;
    if (locationName) params.locationName = locationName;
    if (locationType) params.locationType = locationType;
    if (latitude) params.latitude = latitude;
    if (longitude) params.longitude = longitude;
    if (checkIn) params.checkIn = new Date(checkIn);
    if (checkOut) params.checkOut = new Date(checkOut);
    if (guests) params.guests = guests;
    if (rooms) params.rooms = rooms;
    
    // Extract any filters if present
    if (req.query.filters || req.body.filters) {
      try {
        params.filters = typeof req.query.filters === 'string' 
          ? JSON.parse(req.query.filters) 
          : (req.query.filters || req.body.filters);
      } catch (e) {
        logger.warn('Error parsing filters', { error: e });
      }
    }
    
    return params;
  };

  // Override the send method to track search after response is sent
  res.send = function(body: any): Response {
    try {
      // Track successful search
      const searchParams = getSearchParams();
      
      // Try to extract result count from response
      let resultsCount = 0;
      if (body) {
        try {
          const parsedBody = typeof body === 'string' ? JSON.parse(body) : body;
          if (Array.isArray(parsedBody)) {
            resultsCount = parsedBody.length;
          } else if (parsedBody.results && Array.isArray(parsedBody.results)) {
            resultsCount = parsedBody.results.length;
          } else if (parsedBody.data && Array.isArray(parsedBody.data)) {
            resultsCount = parsedBody.data.length;
          } else if (parsedBody.count || parsedBody.total) {
            resultsCount = parsedBody.count || parsedBody.total;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      // Add results count and duration
      searchParams.resultsCount = resultsCount;
      searchParams.duration = Date.now() - startTime;
      
      // Only track if we have location or query
      if (searchParams.locationName || searchParams.searchQuery) {
        analyticsService.trackSearch(req, searchParams).catch(err => {
          logger.error('Error tracking search in middleware', { error: err });
        });
      }
    } catch (error) {
      logger.error('Error in search tracking middleware', { error });
    }
    
    return originalSend.call(this, body);
  };
  
  // Override json method to ensure tracking works with res.json too
  res.json = function(body: any): Response {
    return originalJson.call(this, body);
  };
  
  next();
};

/**
 * Middleware to track property views
 */
export const trackPropertyView = (req: Request, res: Response, next: NextFunction) => {
  // Only apply to property detail routes
  if (!req.params.id && !req.query.id) {
    return next();
  }
  
  try {
    const propertyId = parseInt(req.params.id as string || req.query.id as string);
    if (!isNaN(propertyId)) {
      // Source of the property view
      const source = req.query.source as string || req.headers.referer?.includes('search') 
        ? 'search' 
        : 'direct';
      
      // Track property view asynchronously
      analyticsService.trackPropertyView(req, propertyId, { source }).catch(err => {
        logger.error('Error tracking property view in middleware', { error: err });
      });
    }
  } catch (error) {
    logger.error('Error in property view tracking middleware', { error });
  }
  
  next();
};

export default {
  trackPageView,
  trackSearch,
  trackPropertyView
};