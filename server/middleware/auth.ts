import { Request, Response, NextFunction } from 'express';

// Middleware to check if the user is authenticated
export function requireAuth(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'You must be logged in to access this resource' });
  }
  next();
}

// Middleware to check if the user is an admin
export function requireAdmin(req: Request, res: Response, next: NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'You must be logged in to access this resource' });
  }
  
  if (!req.user || !req.user.isAdmin) {
    return res.status(403).json({ error: 'You do not have permission to access this resource' });
  }
  
  next();
}