import winston from 'winston';

// Create custom logger
const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'travel-booking-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    // Add file transport in production
    ...(process.env.NODE_ENV === 'production' 
      ? [
          new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
          new winston.transports.File({ filename: 'logs/combined.log' })
        ] 
      : [])
  ]
});

// Convenience exports for common log levels
export const debug = (message: string, meta?: any) => logger.debug(message, meta);
export const info = (message: string, meta?: any) => logger.info(message, meta);
export const error = (message: string, meta?: any) => logger.error(message, meta);
export const warn = (message: string, meta?: any) => logger.warn(message, meta);

/**
 * Log an operation with structured data
 */
export function logOperation(requestId: string, operation: string, data: any) {
  logger.info(`${operation}`, {
    requestId,
    operation,
    ...data
  });
}

/**
 * Log an error with structured data
 */
export function logError(requestId: string, error: Error | string, data: any = {}) {
  const errorMessage = error instanceof Error ? error.message : error;
  const errorStack = error instanceof Error ? error.stack : undefined;

  logger.error(`Error: ${errorMessage}`, {
    requestId,
    error: errorMessage,
    stack: errorStack,
    ...data
  });
}

export default logger;