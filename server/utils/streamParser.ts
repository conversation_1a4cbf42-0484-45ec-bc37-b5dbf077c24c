/**
 * Utility functions for parsing and validating SSE data streams
 */

import logger from './logger';

/**
 * Safely parse JSON data from SSE
 * @param rawData The raw data string to parse
 * @returns An object containing parsing status and result
 */
export function safeParseSSEData(rawData: string): { 
  valid: boolean; 
  parsed?: any; 
  error?: Error;
  invalidReason?: string;
} {
  // Check if data is actually a string
  if (typeof rawData !== 'string') {
    return { 
      valid: false, 
      error: new Error('Input is not a string'),
      invalidReason: `Input type: ${typeof rawData}` 
    };
  }

  // Extract the data payload from SSE format
  let dataPayload = rawData.trim();
  
  // Handle SSE format: "data: {...}\n\n"
  if (dataPayload.startsWith('data:')) {
    dataPayload = dataPayload.substring(5).trim();
  }
  
  // Skip empty data
  if (!dataPayload) {
    return { 
      valid: false, 
      invalidReason: 'Empty data' 
    };
  }
  
  // Handle special cases
  if (dataPayload === '[DONE]' || dataPayload === 'DONE') {
    return {
      valid: true,
      parsed: { type: 'control', action: 'done' }
    };
  }

  // Check if this is a log message (to avoid noise in logs)
  if (dataPayload.includes('Initialize') || 
      dataPayload.includes('Added search') || 
      dataPayload.includes('Recorded property') ||
      dataPayload.includes('Updated filters') ||
      dataPayload.includes('Added property to comparison') ||
      dataPayload.includes('Recorded booking attempt') ||
      dataPayload.includes('Updated conversation context') ||
      dataPayload.includes('Added message')) {
    return { 
      valid: false, 
      invalidReason: 'Log message detected' 
    };
  }

  try {
    // Enhanced validation that it looks like JSON
    if (
      !(dataPayload.startsWith('{') && dataPayload.endsWith('}')) && 
      !(dataPayload.startsWith('[') && dataPayload.endsWith(']'))
    ) {
      return { 
        valid: false, 
        invalidReason: `Not JSON format: ${dataPayload.substring(0, 20)}...` 
      };
    }

    // Additional structural validation for complex cases
    // Check for balanced braces and brackets
    let braceCount = 0;
    let bracketCount = 0;
    let inString = false;
    let escaped = false;
    
    for (let i = 0; i < dataPayload.length; i++) {
      const char = dataPayload[i];
      
      if (escaped) {
        escaped = false;
        continue;
      }
      
      if (char === '\\' && inString) {
        escaped = true;
        continue;
      }
      
      if (char === '"' && !escaped) {
        inString = !inString;
        continue;
      }
      
      if (!inString) {
        if (char === '{') braceCount++;
        else if (char === '}') braceCount--;
        else if (char === '[') bracketCount++;
        else if (char === ']') bracketCount--;
      }
    }
    
    // If braces or brackets are unbalanced, it's probably incomplete JSON
    if (braceCount !== 0 || bracketCount !== 0) {
      return { 
        valid: false,
        invalidReason: 'Unbalanced JSON structure',
        error: new Error(`Unbalanced structure: braces=${braceCount}, brackets=${bracketCount}`)
      };
    }

    // Parse JSON
    const parsed = JSON.parse(dataPayload);
    
    // Validate the expected type
    if (!parsed || (typeof parsed !== 'object')) {
      return { 
        valid: false, 
        invalidReason: 'Parsed data is not an object',
        error: new Error('Parsed result is not an object') 
      };
    }
    
    return { valid: true, parsed };
  } catch (error) {
    logger.warn('JSON parse error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      data: dataPayload.length > 100 ? 
        `${dataPayload.substring(0, 100)}...` : 
        dataPayload 
    });
    
    return { 
      valid: false, 
      error: error instanceof Error ? error : new Error('JSON parse error'),
      invalidReason: 'Parse error' 
    };
  }
}

/**
 * Process a chunk from an SSE stream and handle JSON parsing
 * @param data The raw data from the stream
 * @returns A processed chunk with validation info
 */
export function processStreamChunk(data: string): {
  isValid: boolean;
  chunk?: any;
  error?: string;
} {
  const { valid, parsed, error, invalidReason } = safeParseSSEData(data);
  
  if (!valid) {
    return {
      isValid: false,
      error: error?.message || invalidReason || 'Invalid data format'
    };
  }
  
  // Additional validation - check if the parsed data has required properties
  if (!parsed || typeof parsed !== 'object') {
    return {
      isValid: false,
      error: 'Parsed data is not an object'
    };
  }
  
  // For our chat API, we expect a type and data property
  if (!('type' in parsed) || !('data' in parsed)) {
    return {
      isValid: false,
      error: 'Missing required properties (type, data)'
    };
  }
  
  return {
    isValid: true,
    chunk: parsed
  };
}