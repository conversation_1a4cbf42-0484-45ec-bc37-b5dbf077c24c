/** @type {import('@jest/types').Config.InitialOptions} */
module.exports = {
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'node',
  extensionsToTreatAsEsm: ['.ts'],
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
    '^@/(.*)$': '<rootDir>/client/src/$1',
    '^@db$': '<rootDir>/db/index.ts',
    '^@db/(.*)$': '<rootDir>/db/$1'
  },
  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        useESM: true,
        tsconfig: {
          module: 'NodeNext',
          moduleResolution: 'NodeNext'
        }
      }
    ]
  },
  setupFiles: ['<rootDir>/jest.setup.ts'],
  testTimeout: 30000, // Longer timeout for functional tests
  verbose: true,
  moduleDirectories: ['node_modules', '<rootDir>'],
  testMatch: [
    "**/__tests__/functional/**/*.test.ts",
  ],
  testEnvironmentOptions: {
    teardown: false
  }
};