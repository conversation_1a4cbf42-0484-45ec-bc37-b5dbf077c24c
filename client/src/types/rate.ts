/**
 * Interface representing a rate for a property
 */
export interface Rate {
  code: string;
  description: string;
  rate: number;
  totalAmount: number;
  originalAmount: number;
  discountAmount: number;
  discountPercent: number;
  currency: string;
  roomTypeCode: string;
  bedTypeCode: string;
  restrictedRate: boolean;
  refundable: boolean;
  maxOccupancy: number;
  availableQuantity: number;
  rateCode?: string;
  rateDescription?: string;
  cancellationPolicy?: string;
  guaranteePolicy?: string;
  depositPolicy?: string;
  includedServices?: string[];
  promotions?: Array<{
    code: string;
    description: string;
    discountType: string;
    discountValue: number;
    startDate?: string;
    endDate?: string;
  }>;
  taxes?: Array<{
    type: string;
    amount: number;
    currency: string;
    included: boolean;
  }>;
  fees?: Array<{
    type: string;
    amount: number;
    currency: string;
    included: boolean;
  }>;
} 