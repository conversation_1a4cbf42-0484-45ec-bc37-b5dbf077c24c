// Import types from server schema
import type { Rate } from './rate';

export interface PropertyImage {
  url: string;
  caption?: string;
  displayOrder?: number;
  roomTypeCode?: string;
  category?: string;
}

export interface RoomImage extends PropertyImage {
  roomTypeCode: string;
}

export interface Property {
  id: number;
  name: string;
  description: string;
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
  country: string;
  rating?: number;
  reviewCount?: number;
  basePrice: number;
  currency: string;
  images?: (string | PropertyImage)[];
  amenities?: string[];
}

export interface PropertyWithRates extends Property {
  type?: string;
  discount?: number;
  highlighted?: boolean;
  isLoadingRates?: boolean;
  rateError?: string;
  propertyDetails?: {
    images?: PropertyImage[];
    isLoading?: boolean;
    error?: boolean;
  };
  rates?: Rate[];
}

export interface Room {
  rate: number;
  totalAmount?: number;
  originalAmount?: number;
  currency: string;
  Total?: {
    '@Amount': string;
    '@Discount'?: string;
    '@RetailDiscountPercent'?: string;
    '@Currency'?: string;
    '@IncludesBookingFee': string;
    '@ComparableRetailDiscount'?: string;
  };
  code?: string;
  description?: string;
  roomTypeCode?: string;
  bedTypeCode?: string;
  maxOccupancy?: number;
  availableQuantity?: number;
  discountPercent?: number;
  totalDiscount?: number;
  retailDiscountPercent?: number;
  totalComparableRetailDiscount?: number;
  restrictedRate?: boolean;
  refundable?: boolean;
  rateCode?: string;
  rateDescription?: string;
  cancellationPolicy?: string;
  guaranteePolicy?: string;
  depositPolicy?: string;
  includedServices?: string[];
  amenities?: string[];
  viewType?: string;
  smokingPreference?: string;
  bedCount?: number;
  bedType?: string;
  roomSize?: string;
  floorLevel?: string;
  accessibility?: string;
  images?: RoomImage[];
  promotions?: Array<{
    code: string;
    description: string;
    discountType: string;
    discountValue: number;
    startDate?: string;
    endDate?: string;
  }>;
  taxes?: Array<{
    type: string;
    amount: number;
    currency: string;
    included: boolean;
  }>;
  fees?: Array<{
    type: string;
    amount: number;
    currency: string;
    included: boolean;
  }>;
}

export interface ExtendedRoom extends Omit<Room, 'images'> {
  ratePlanCode: string; 
  ratePlanDescription: string;
  images?: string[];
  virtualTour?: string;
  totalDiscount?: number;
  retailDiscountPercent?: number;
  totalComparableRetailDiscount?: number;
  packages?: Array<{
    id: string;
    name: string;
    description: string;
    type: string;
    price: number;
    savings: number;
    includes?: string[];
  }>;
  instantConfirmation?: boolean;
  minimumStay?: number;
  maximumStay?: number;
  availabilityStatus?: string;
  remainingRooms?: number;
}

export interface RatePlan {
  code: string;
  description: string;
  commissionStatus: string;
  rooms: { [key: string]: Room };
}

export interface AvailabilityResponse {
  ratePlans: { [key: string]: RatePlan };
}

export interface Reservation {
  id: number;
  propertyId: number;
  userId: number;
  checkIn: string;
  checkOut: string;
  guests: number;
  roomCode: string;
  ratePlanCode: string;
  status: 'confirmed' | 'cancelled' | 'pending';
  totalAmount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}

export type PropertyType = 'hotel' | 'resort' | 'apartment' | 'villa' | 'guesthouse';

export interface SearchResponse {
  properties: Property[];
  total: number;
  currentPage: number;
  totalPages: number;
  explanation?: string;
  searchId: string;
  timing: {
    total: number;
    source?: 'database' | 'api';
  };
} 