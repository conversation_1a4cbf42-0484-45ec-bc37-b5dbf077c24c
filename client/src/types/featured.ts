export interface FeaturedProperty {
  PropertyId: number;
  DealsFound: number;
  DealWeight: number;
  MaxDiscountPercent: number;
  AvgDiscountPercent: number;
  ReferencePrice: number;
  ReferencePriceCurrency: string;
  PropertyName: string;
  PropertyAddress: string;
  LocationId: number;
  PropertyLatitude: number;
  PropertyLongitude: number;
  PropertyImageUrl: string;
  PropertyImageUrlHighRes: string;
  PropertyRating: string;
  TripAdvisorRating: number;
  TripAdvisorReviewCount: number;
  RatingImageUrl: string;
  CheckIn: string;
  CheckOut: string;
}

export interface FeaturedPropertyResponse {
  data: FeaturedProperty[];
  error?: string;
} 