import { ReactNode, createContext, useContext } from "react";
import {
  useQuery,
  useMutation,
  UseMutationResult,
} from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { queryClient, apiRequest } from "@/lib/queryClient";

// Define types for our user and auth context
type User = {
  id: number;
  email: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  profileImageUrl?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  membershipType: string;
  isAdmin: boolean;
  isVerified: boolean;
  preferences?: {
    displayName?: string;
    emailNotifications?: boolean;
    marketingEmails?: boolean;
    darkMode?: boolean;
    currency?: string;
    language?: string;
    [key: string]: any;
  };
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string | null;
};

type LoginData = {
  email: string;
  password: string;
};

type RegisterData = LoginData & {
  firstName: string;
  lastName: string;
  promoCode?: string;
};

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  loginMutation: UseMutationResult<any, Error, LoginData>;
  logoutMutation: UseMutationResult<void, Error, void>;
  registerMutation: UseMutationResult<any, Error, RegisterData>;
};

// Create the Auth Context
export const AuthContext = createContext<AuthContextType | null>(null);

// Auth Provider Component
export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();

  // Query to fetch current user
  const {
    data: userData,
    error,
    isLoading,
  } = useQuery<User | null, Error>({
    queryKey: ["/api/user"],
    queryFn: async () => {
      try {
        const res = await apiRequest("GET", "/api/user");
        if (res.status === 401) return null;
        
        const data = await res.json();
        // Handle both old and new API response formats
        if (data.success && data.user) {
          // Session-based auth returns {success: true, user: {...}}
          return data.user as User;
        } else if (data.id) {
          // Direct user object with id - older format
          return data as User;
        }
        return null;
      } catch (error) {
        // If 401, return null to indicate not logged in
        if (error instanceof Error && error.message.includes("401")) {
          return null;
        }
        throw error;
      }
    },
    // Don't refetch on window focus for auth state
    refetchOnWindowFocus: false,
  });
  
  // Ensure user is always User | null (never undefined)
  const user = userData ?? null;

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData) => {
      const res = await apiRequest("POST", "/api/login", credentials);
      return await res.json();
    },
    onSuccess: (data) => {
      if (data.success && data.user) {
        queryClient.setQueryData(["/api/user"], data.user);
        
        // Get user's name from firstName, lastName, preferences.displayName, or email
        const userName = data.user.firstName ? 
          `${data.user.firstName} ${data.user.lastName || ''}`.trim() : 
          data.user.preferences?.displayName || 
          data.user.email.split('@')[0];
          
        toast({
          title: "Login successful",
          description: `Welcome back, ${userName}!`,
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Login failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: async (userData: RegisterData) => {
      const res = await apiRequest("POST", "/api/register", userData);
      return await res.json();
    },
    onSuccess: (data) => {
      if (data.success && data.user) {
        queryClient.setQueryData(["/api/user"], data.user);
        
        // Get user's name from firstName, lastName, preferences.displayName, or email
        const userName = data.user.firstName ? 
          `${data.user.firstName} ${data.user.lastName || ''}`.trim() : 
          data.user.preferences?.displayName || 
          data.user.email.split('@')[0];
          
        toast({
          title: "Registration successful",
          description: `Welcome to RoomLama, ${userName}!`,
        });
      } else {
        toast({
          title: "Registration issue",
          description: data.message || "Something went wrong",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Registration failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("POST", "/api/logout");
    },
    onSuccess: () => {
      queryClient.setQueryData(["/api/user"], null);
      toast({
        title: "Logout successful",
        description: "You have been logged out",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Logout failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        error,
        loginMutation,
        logoutMutation,
        registerMutation,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}