import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';

export interface SearchHistoryEntry {
  locationName: string;
  lat: number;
  lng: number;
  checkIn: string;
  checkOut: string;
  guests: number;
  rooms: number;
  timestamp: number;
}

const MAX_HISTORY_ITEMS = 5;

export function useSearchHistory() {
  const [searchHistory, setSearchHistory] = useState<SearchHistoryEntry[]>([]);
  const [_, setLocation] = useLocation();

  useEffect(() => {
    // Load search history from localStorage on mount
    const savedHistory = localStorage.getItem('searchHistory');
    if (savedHistory) {
      try {
        setSearchHistory(JSON.parse(savedHistory));
      } catch (e) {
        console.error('Failed to parse search history:', e);
        localStorage.removeItem('searchHistory');
      }
    }
  }, []);

  const addToHistory = (entry: Omit<SearchHistoryEntry, 'timestamp'>) => {
    const newEntry = { ...entry, timestamp: Date.now() };
    setSearchHistory(prev => {
      // Remove any existing entries with the same location
      const filtered = prev.filter(item => 
        !(item.lat === entry.lat && item.lng === entry.lng)
      );
      
      // Add new entry to the start
      const updated = [newEntry, ...filtered].slice(0, MAX_HISTORY_ITEMS);
      
      // Save to localStorage
      localStorage.setItem('searchHistory', JSON.stringify(updated));
      
      return updated;
    });
  };

  const clearHistory = () => {
    localStorage.removeItem('searchHistory');
    setSearchHistory([]);
  };

  const repeatSearch = (entry: SearchHistoryEntry) => {
    const params = new URLSearchParams({
      lat: String(entry.lat),
      lng: String(entry.lng),
      locationName: entry.locationName,
      checkIn: entry.checkIn,
      checkOut: entry.checkOut,
      guests: String(entry.guests),
      rooms: String(entry.rooms)
    });

    setLocation(`/results?${params.toString()}`);
  };

  return {
    searchHistory,
    addToHistory,
    clearHistory,
    repeatSearch
  };
}
