import { useState, useEffect } from 'react';
import { PropertyWithRates } from '@/types/schema.js';

export const useSavedProperties = () => {
  const [savedProperties, setSavedProperties] = useState<number[]>([]);

  useEffect(() => {
    const saved = localStorage.getItem('savedProperties');
    if (saved) {
      setSavedProperties(JSON.parse(saved));
    }
  }, []);

  const toggleSaved = (propertyId: number) => {
    setSavedProperties(prev => {
      const newSaved = prev.includes(propertyId)
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId];
      
      localStorage.setItem('savedProperties', JSON.stringify(newSaved));
      return newSaved;
    });
  };

  return {
    savedProperties,
    toggleSaved,
    isSaved: (propertyId: number) => savedProperties.includes(propertyId)
  };
}; 