import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { io, Socket } from 'socket.io-client';
import { useToast } from './use-toast';

interface TestRunSummary {
  total: number;
  passed: number;
  failed: number;
  skipped: number;
}

export interface TestRun {
  runId: string;
  status: 'running' | 'completed' | 'failed';
  duration: number;
  startTime: number;
  endTime?: number;
  testType: string;
  summary?: TestRunSummary;
  logs?: string[];
}

interface StartTestRunParams {
  testType: 'unit' | 'functional' | 'react' | 'all';
  options?: {
    testFile?: string;
  };
}

interface TestProgressUpdate {
  runId: string;
  log: string;
  status: 'running' | 'completed' | 'failed';
}

interface TestCompleteUpdate {
  runId: string;
  status: 'completed' | 'failed';
  duration: number;
  summary?: TestRunSummary;
  exitCode: number;
}

export const useTestRunner = () => {
  const { toast } = useToast();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [logs, setLogs] = useState<Record<string, string[]>>({});
  const [activeRuns, setActiveRuns] = useState<Record<string, TestRun>>({});

  // Initialize Socket.IO connection
  useEffect(() => {
    // Create the socket connection using the same protocol as the current page
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const socketUrl = `${protocol}//${window.location.host}`;
    
    const socketInstance = io(`${socketUrl}/test-runner`, {
      path: '/socket.io',
    });

    socketInstance.on('connect', () => {
      console.log('Connected to test runner socket');
    });

    socketInstance.on('disconnect', () => {
      console.log('Disconnected from test runner socket');
    });

    socketInstance.on('connect_error', (err) => {
      console.error('Socket connection error:', err);
      toast({
        title: 'Socket Connection Error',
        description: 'Could not connect to real-time test updates',
        variant: 'destructive',
      });
    });

    // Handle test progress updates
    socketInstance.on('test-progress', (update: TestProgressUpdate) => {
      // Add the log entry
      setLogs(prevLogs => {
        const runLogs = prevLogs[update.runId] || [];
        return {
          ...prevLogs,
          [update.runId]: [...runLogs, update.log],
        };
      });

      // Update the run status
      setActiveRuns(prevRuns => {
        const run = prevRuns[update.runId];
        if (run) {
          return {
            ...prevRuns,
            [update.runId]: {
              ...run,
              status: update.status,
            },
          };
        }
        return prevRuns;
      });
    });

    // Handle test completion
    socketInstance.on('test-complete', (update: TestCompleteUpdate) => {
      setActiveRuns(prevRuns => {
        const run = prevRuns[update.runId];
        if (run) {
          const updatedRun = {
            ...run,
            status: update.status,
            duration: update.duration,
            endTime: Date.now(),
            summary: update.summary,
          };

          // Show toast notification
          const title = update.status === 'completed'
            ? 'Test Run Completed'
            : 'Test Run Failed';
          const description = `${run.testType} tests ${update.status} in ${Math.round(update.duration / 1000)}s`;
          
          toast({
            title,
            description,
            variant: update.status === 'completed' ? 'default' : 'destructive',
          });

          return {
            ...prevRuns,
            [update.runId]: updatedRun,
          };
        }
        return prevRuns;
      });
    });

    setSocket(socketInstance);

    // Cleanup on unmount
    return () => {
      socketInstance.disconnect();
    };
  }, [toast]);

  // Query to get all active test runs
  const { data: testRuns, refetch: refetchTestRuns } = useQuery<TestRun[]>({
    queryKey: ['/api/tests/runs'],
    queryFn: async () => {
      const res = await fetch('/api/tests/runs');
      if (!res.ok) {
        throw new Error('Failed to fetch test runs');
      }
      const data = await res.json();
      return data.runs;
    },
  });
  
  // Update active runs state when data changes
  useEffect(() => {
    if (testRuns) {
      // Update our active runs state
      const runsMap = testRuns.reduce((acc: Record<string, TestRun>, run: TestRun) => {
        acc[run.runId] = run;
        return acc;
      }, {} as Record<string, TestRun>);
      
      setActiveRuns(prevRuns => ({
        ...prevRuns,
        ...runsMap,
      }));
    }
  }, [testRuns]);

  // Mutation to start a new test run
  const startTestRunMutation = useMutation({
    mutationFn: async (params: StartTestRunParams) => {
      const res = await fetch('/api/tests/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });
      
      if (!res.ok) {
        throw new Error('Failed to start test run');
      }
      
      return res.json();
    },
    onSuccess: (data) => {
      // Add the new run to our active runs
      setActiveRuns(prevRuns => ({
        ...prevRuns,
        [data.runId]: {
          runId: data.runId,
          status: 'running',
          duration: 0,
          startTime: Date.now(),
          testType: data.testType,
        },
      }));

      // Initialize logs for this run
      setLogs(prevLogs => ({
        ...prevLogs,
        [data.runId]: [],
      }));

      toast({
        title: 'Test Run Started',
        description: `Started ${data.testType} tests`,
      });

      // Refresh the test runs list
      refetchTestRuns();
    },
    onError: (error) => {
      toast({
        title: 'Failed to Start Tests',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    },
  });

  // Mutation to stop a test run
  const stopTestRunMutation = useMutation({
    mutationFn: async (runId: string) => {
      const res = await fetch(`/api/tests/run/${runId}`, {
        method: 'DELETE',
      });
      
      if (!res.ok) {
        throw new Error('Failed to stop test run');
      }
      
      return res.json();
    },
    onSuccess: (_, runId) => {
      toast({
        title: 'Test Run Stopped',
        description: 'Test run was stopped manually',
        variant: 'destructive',
      });

      // Update the run status
      setActiveRuns(prevRuns => {
        const run = prevRuns[runId];
        if (run) {
          return {
            ...prevRuns,
            [runId]: {
              ...run,
              status: 'failed',
              endTime: Date.now(),
            },
          };
        }
        return prevRuns;
      });

      // Refresh the test runs list
      refetchTestRuns();
    },
    onError: (error) => {
      toast({
        title: 'Failed to Stop Test Run',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    },
  });

  // Function to get logs for a specific run
  const getRunLogs = (runId: string) => {
    return logs[runId] || [];
  };

  // Function to clear logs for a specific run
  const clearRunLogs = (runId: string) => {
    setLogs(prevLogs => {
      const newLogs = { ...prevLogs };
      delete newLogs[runId];
      return newLogs;
    });
  };

  return {
    testRuns: Object.values(activeRuns),
    startTestRun: startTestRunMutation.mutate,
    stopTestRun: stopTestRunMutation.mutate,
    getRunLogs,
    clearRunLogs,
    isStarting: startTestRunMutation.isPending,
    isStopping: stopTestRunMutation.isPending,
  };
};