import { useState, useEffect, useCallback } from 'react';
import { getGoogleMaps } from '@/lib/googleMaps';

interface GeocodeResult {
  lat: number;
  lng: number;
  placeId?: string;
  formattedAddress?: string;
  placeType?: string;
}

export function useGooglePlaces() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [geocoder, setGeocoder] = useState<google.maps.Geocoder | null>(null);
  
  // Initialize the Google Maps API
  useEffect(() => {
    async function loadGoogleMaps() {
      try {
        const google = await getGoogleMaps();
        setGeocoder(new google.maps.Geocoder());
        setIsLoaded(true);
      } catch (err) {
        console.error('Failed to load Google Maps API:', err);
        setError(err instanceof Error ? err : new Error('Failed to load Google Maps API'));
      }
    }
    
    loadGoogleMaps();
  }, []);
  
  /**
   * Geocode an address string to latitude and longitude
   */
  const geocodeAddress = useCallback(async (address: string): Promise<GeocodeResult | null> => {
    if (!isLoaded || !geocoder) {
      throw new Error('Google Maps API not loaded');
    }
    
    try {
      const result = await geocoder.geocode({ address });
      
      if (result.results.length === 0) {
        return null;
      }
      
      const location = result.results[0].geometry.location;
      const placeId = result.results[0].place_id;
      const formattedAddress = result.results[0].formatted_address;
      
      // Determine place type based on types array
      let placeType = 'unknown';
      if (result.results[0].types) {
        const types = result.results[0].types;
        if (types.includes('locality')) {
          placeType = 'locality';
        } else if (types.includes('administrative_area_level_1')) {
          placeType = 'administrative_area_level_1';
        } else if (types.includes('country')) {
          placeType = 'country';
        } else if (types.includes('sublocality') || types.includes('neighborhood')) {
          placeType = 'neighborhood';
        } else if (types.includes('establishment')) {
          placeType = 'establishment';
        } else if (types.includes('point_of_interest')) {
          placeType = 'point_of_interest';
        }
      }
      
      return {
        lat: location.lat(),
        lng: location.lng(),
        placeId,
        formattedAddress,
        placeType
      };
    } catch (err) {
      console.error('Geocoding error:', err);
      throw err;
    }
  }, [isLoaded, geocoder]);
  
  return { isLoaded, error, geocodeAddress };
}