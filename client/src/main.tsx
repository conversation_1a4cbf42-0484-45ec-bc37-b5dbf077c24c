import React from "react";
import ReactD<PERSON> from "react-dom/client";
import App from "./App.tsx";
import "./index.css";

// Import E2E tester for Replit environment testing
import "./utils/e2e-chat-test";

// Import quick chat test utilities for debugging
import "./utils/quick-chat-test";

// Import enhanced AI chat test utilities
import "./utils/test-enhanced-ai-chat";

// Import manual AI chat test suite
import "./utils/manual-ai-chat-test";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);