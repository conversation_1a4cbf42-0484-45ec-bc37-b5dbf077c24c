import React from "react";
import { Switch, Route, useLocation } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import Search from "@/pages/Search";
import Results from "@/pages/Results";
import PropertyDetails from "@/pages/PropertyDetails";
import Booking from "@/pages/Booking";
import Reservation from "@/pages/Reservation";
import Reservations from "@/pages/Reservations";
import ProfilePage from "@/pages/ProfilePage";
import LocationDebug from "@/pages/LocationDebug";
import TestHub from "@/pages/TestHub";
import AuthPage from "@/pages/auth-page";
import EnhancedAIChatDemo from "@/pages/EnhancedAIChatDemo";
import AIDemo from "@/pages/AIDemo";
import AdminDashboard from "./pages/admin/Dashboard";
import AdminUsers from "./pages/admin/Users";
import AdminPromoCodes from "./pages/admin/PromoCodes";
import AdminAnalytics from "./pages/admin/Analytics";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { ThemeProvider } from "@/components/theme-provider";
import { ProtectedAdminRoute } from "@/components/ProtectedAdminRoute";
import { AuthProvider } from "@/hooks/use-auth";
import { ProtectedRoute } from "@/lib/protected-route";

function App() {
  const [location] = useLocation();

  React.useEffect(() => {
    // Handle any side effects
  }, [location]); // Properly defined dependency array

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              <Switch>
                <Route path="/" component={Search} />
                <Route path="/results" component={Results} />
                <Route path="/property/:id" component={PropertyDetails} />
                <Route path="/booking/:id" component={Booking} />
                <Route path="/reservation" component={Reservation} />
                <Route path="/properties/:id" component={PropertyDetails} />
                <Route path="/debug/location" component={LocationDebug} />
                <Route path="/auth" component={AuthPage} />
                <Route path="/enhanced-ai-chat" component={EnhancedAIChatDemo} />
                <Route path="/ai-demo" component={AIDemo} />
                <ProtectedRoute path="/profile" component={ProfilePage} />
                <ProtectedRoute path="/bookings" component={Reservations} />
                <ProtectedAdminRoute path="/admin" component={AdminDashboard} />
                <ProtectedAdminRoute path="/admin/users" component={AdminUsers} />
                <ProtectedAdminRoute path="/admin/promo-codes" component={AdminPromoCodes} />
                <ProtectedAdminRoute path="/admin/analytics" component={AdminAnalytics} />
                <ProtectedAdminRoute path="/debug/test-hub" component={TestHub} />
                <Route component={NotFound} />
              </Switch>
            </main>
            <Footer />
            <Toaster />
          </div>
        </ThemeProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

// Fallback 404 page
function NotFound() {
  return (
    <div className="min-h-[50vh] w-full flex items-center justify-center bg-background">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="pt-6">
          <div className="flex mb-4 gap-2">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <h1 className="text-2xl font-bold">404 Page Not Found</h1>
          </div>
          <p className="text-muted-foreground">
            The page you're looking for doesn't exist.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

export default App;