import { useState, useRef, useEffect, useCallback } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON>,
  CardContent,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Property, PropertyWithRates } from "@/types/schema";
import PropertyCard from "./PropertyCard";
import LocationMarker from "./LocationMarker";
import { MessageCircle, Send, MapPin, Hotel, Calendar, DollarSign, Star, X, ChevronRight, Search as SearchIcon, Lightbulb, Info, ListFilter, Map as MapIcon, Landmark, Building, Waves, Trash2, AlertTriangle, Sparkles, Users, UserCircle, Bo<PERSON> } from "lucide-react";
import { useLocation } from "wouter";
import styled from '@emotion/styled';
import { cn } from "@/lib/utils";
import { useGooglePlaces } from "@/hooks/use-google-places";
import { useToast } from "@/hooks/use-toast";

// Message interface
interface Message {
  role: "user" | "assistant";
  content: string;
  id: string;
  suggestedProperties?: PropertyWithRates[];
  location?: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
  actions?: Array<{
    type: 'location' | 'property' | 'search' | 'filter' | 'info';
    label: string;
    data: any;
    icon?: React.ReactNode;
  }>;
}

// Props interface
interface AiChatProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    rooms?: string | null;
    properties?: PropertyWithRates[];
    filters?: {
      propertyTypes: string[];
      amenities: string[];
      priceRange: [number, number];
      minRating: number;
    };
    searchHistory?: Array<any>;
  };
  variant?: 'integrated' | 'floating' | 'modal';
  onClose?: () => void;
}

// Styled components with modern, intuitive design
const ChatContainer = styled(Card)<{ variant: 'integrated' | 'floating' | 'modal' }>`
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);

  ${props => {
    switch (props.variant) {
      case 'modal':
        return `
          position: fixed;
          inset: 0;
          width: 100vw;
          height: 100vh;
          max-width: 100vw;
          margin: 0;
          border-radius: 0;
          z-index: 1000;
          background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
          animation: slideInFromBottom 0.4s cubic-bezier(0.16, 1, 0.3, 1);

          @keyframes slideInFromBottom {
            from {
              transform: translateY(100%);
              opacity: 0;
            }
            to {
              transform: translateY(0);
              opacity: 1;
            }
          }
        `;
      case 'integrated':
        return `
          height: 100%;
          width: 100%;
          max-width: 900px;
          margin: 0 auto;
        `;
      case 'floating':
        return `
          height: 650px;
          width: 420px;
          position: fixed;
          right: 24px;
          bottom: 24px;
          z-index: 1000;
        `;
    }
  }}
`;

const MessageBubble = styled.div<{ isUser: boolean; isWelcome?: boolean }>`
  max-width: ${props => props.isUser ? '80%' : '90%'};
  padding: ${props => props.isWelcome ? '24px' : '16px 20px'};
  margin: ${props => props.isUser ? '8px 0 8px auto' : '8px auto 8px 0'};
  border-radius: ${props => {
    if (props.isWelcome) return '20px';
    return props.isUser ? '20px 20px 6px 20px' : '20px 20px 20px 6px';
  }};
  background: ${props => {
    if (props.isWelcome) return 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
    return props.isUser ? 'linear-gradient(135deg, #e1f0ff 0%, #dbeafe 100%)' : '#ffffff';
  }};
  color: ${props => {
    if (props.isWelcome) return '#ffffff';
    return props.isUser ? '#1e40af' : '#1f2937';
  }};
  font-size: ${props => props.isWelcome ? '1.1rem' : '1rem'};
  line-height: 1.6;
  word-wrap: break-word;
  box-shadow: ${props => {
    if (props.isWelcome) return '0 8px 25px rgba(59, 130, 246, 0.3)';
    return props.isUser ? '0 4px 12px rgba(59, 130, 246, 0.15)' : '0 4px 12px rgba(0, 0, 0, 0.08)';
  }};
  border: ${props => {
    if (props.isWelcome) return 'none';
    return props.isUser ? '1px solid rgba(59, 130, 246, 0.2)' : '1px solid rgba(0, 0, 0, 0.08)';
  }};
  white-space: pre-wrap;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${props => {
      if (props.isWelcome) return '0 12px 30px rgba(59, 130, 246, 0.4)';
      return props.isUser ? '0 6px 16px rgba(59, 130, 246, 0.2)' : '0 6px 16px rgba(0, 0, 0, 0.12)';
    }};
  }

  ${props => props.isWelcome && `
    text-align: center;
    
    &::before {
      content: '✨';
      font-size: 1.5rem;
      display: block;
      margin-bottom: 8px;
    }
  `}
`;

const QuickActionCard = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  text-align: left;
  width: 100%;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
  }

  &:hover {
    border-color: #3b82f6;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    
    &::before {
      left: 100%;
    }
  }

  .icon {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
  }

  .content {
    flex: 1;
  }

  .title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
    font-size: 1rem;
  }

  .description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.4;
  }

  .arrow {
    color: #9ca3af;
    transition: all 0.3s ease;
  }

  &:hover .arrow {
    color: #3b82f6;
    transform: translateX(4px);
  }
`;

const SuggestionChip = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #475569;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
`;

const WelcomeSection = styled.div`
  text-align: center;
  padding: 32px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  margin: 16px;
`;

const LoadingDots = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background: #ffffff;
  border-radius: 20px 20px 20px 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.08);
  margin: 8px auto 8px 0;
  max-width: 120px;

  .dot {
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border-radius: 50%;
    animation: pulse 1.4s ease-in-out infinite both;
  }

  .dot:nth-child(1) { animation-delay: -0.32s; }
  .dot:nth-child(2) { animation-delay: -0.16s; }
  .dot:nth-child(3) { animation-delay: 0s; }

  @keyframes pulse {
    0%, 80%, 100% {
      transform: scale(0.6);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

// Debug logger
const debug = {
  log: (context: string, data?: any) => {
    console.log(`[AiChat] ${context}:`, data);
  },
  error: (context: string, error: any) => {
    console.error(`[AiChat] Error in ${context}:`, error);
  }
};

export default function AiChat({ context, variant = 'floating', onClose }: AiChatProps) {
  const { toast } = useToast();
  const [_, navigate] = useLocation();
  
  // Simple state management
  const [input, setInput] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [sessionId] = useState(() => `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize with welcome message or context
  useEffect(() => {
    const saved = localStorage.getItem('chatHistory');
    const hasTrigger = localStorage.getItem('ai_chat_trigger') === 'true';
    const hasProcessedTriggerFromStorage = localStorage.getItem('ai_chat_trigger_processed') === 'true';
    
    debug.log('Initializing chat', { 
      saved: !!saved, 
      hasTrigger, 
      hasProcessedTriggerFromStorage,
      contextProvided: !!context,
      sessionId 
    });
    
    if (saved) {
      try {
        const savedMessages = JSON.parse(saved);
        if (Array.isArray(savedMessages) && savedMessages.length > 0) {
          const messagesWithIds = savedMessages.map((msg: any) => ({
            ...msg,
            id: msg.id || `${msg.role}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
          }));
          
          setMessages(messagesWithIds);
          debug.log('Loaded saved messages', messagesWithIds.length);
          
          if (hasTrigger && !hasProcessedTriggerFromStorage) {
            const lastMessage = messagesWithIds[messagesWithIds.length - 1];
            if (lastMessage?.role === 'user' && lastMessage.content?.trim()) {
              debug.log('Processing triggered message from storage', lastMessage.content);
              localStorage.setItem('ai_chat_trigger_processed', 'true');
              localStorage.removeItem('ai_chat_trigger');
              setTimeout(() => {
                debug.log('Sending triggered message to API (from storage)');
                sendMessage.mutate(lastMessage.content);
              }, 500);
            }
          } else if (hasTrigger && hasProcessedTriggerFromStorage) {
            debug.log('Cleaning up already processed trigger (from storage)');
            localStorage.removeItem('ai_chat_trigger');
          }
          return;
        }
      } catch (error) {
        debug.error('Failed to parse saved messages', error);
        // Clear corrupted storage
        localStorage.removeItem('chatHistory');
        localStorage.removeItem('ai_chat_trigger');
        localStorage.removeItem('ai_chat_trigger_processed');
      }
    }

    // If no saved messages, or parsing failed, start fresh
    const initialMessages: Message[] = [];
    let contextMessageContent = "";

    if (context) {
      const { location, checkIn, checkOut, guests } = context;
      let parts = [];
      if (location) parts.push(`in ${location}`);
      if (checkIn && checkOut) parts.push(`from ${checkIn} to ${checkOut}`);
      else if (checkIn) parts.push(`from ${checkIn}`);
      else if (checkOut) parts.push(`until ${checkOut}`);
      if (guests) parts.push(`for ${guests} guest(s)`);
      
      if (parts.length > 0) {
        contextMessageContent = `I'm looking for a place ${parts.join(', ')}.`;
      }
    }
    
    // If there's a trigger and a specific user message in localStorage (set by Search.tsx)
    // that message takes precedence over generic context.
    const triggerUserMessage = localStorage.getItem('ai_chat_initial_user_message');

    if (hasTrigger && triggerUserMessage && !hasProcessedTriggerFromStorage) {
      debug.log('Processing triggered message from "Plan with AI" click', triggerUserMessage);
      initialMessages.push({
        role: "user",
        content: triggerUserMessage,
        id: `user_trigger_${Date.now()}`
      });
      setMessages(initialMessages);
      localStorage.setItem('ai_chat_trigger_processed', 'true');
      localStorage.removeItem('ai_chat_trigger');
      localStorage.removeItem('ai_chat_initial_user_message'); // Clean up this specific item
      
      setTimeout(() => {
        debug.log('Sending triggered message to API (from "Plan with AI" click)');
        sendMessage.mutate(triggerUserMessage);
      }, 100); // Shorter delay as this is a direct action
      return;

    } else if (contextMessageContent) {
      // This is the case where "Plan with AI" was clicked, but the trigger was already processed
      // or there was no specific initial message set. We use the generic context.
      // Or, this chat is directly embedded with context props.
      debug.log('Using provided context to form initial user message', contextMessageContent);
      initialMessages.push({
        role: "user",
        content: contextMessageContent,
        id: `user_context_${Date.now()}`
      });
      setMessages(initialMessages);
      // Automatically send this context-based message
      // This is a design choice: if context is provided, we assume the user wants to start with it.
      setTimeout(() => {
         debug.log('Sending context-based message to API');
         sendMessage.mutate(contextMessageContent);
      }, 100); 
      return; // Important to return to not add the welcome message
    }

    // Default welcome message if no saved history, no trigger, and no context message
    const welcomeMessage: Message = {
      role: "assistant",
      content: "Hi! I'm your AI travel assistant. I'll help you find the perfect place to stay by understanding your preferences and suggesting great options tailored just for you.",
      id: `welcome_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    };
    setMessages([welcomeMessage]);
    debug.log('Created default welcome message');

  }, [context, sessionId]); // Add context to dependency array

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem('chatHistory', JSON.stringify(messages));
    }
  }, [messages]);

  // Send message mutation with proper streaming handling
  const sendMessage = useMutation({
    mutationFn: async (message: string) => {
      if (!message?.trim()) {
        throw new Error("Message cannot be empty");
      }

      const trimmedMessage = message.trim();
      debug.log('Sending message', { message: trimmedMessage, sessionId });
      setIsStreaming(true);

      try {
        // Add user message immediately
        const userMessage: Message = {
          role: "user",
          content: trimmedMessage,
          id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
        };

        setMessages(prev => [...prev, userMessage]);
        setInput("");

        // API request
        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            message: trimmedMessage,
            sessionId,
            context: context || {},
            extractLocation: true
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status}`);
        }

        // Process streaming response
        const reader = response.body?.getReader();
        if (!reader) throw new Error('No response stream');

        const decoder = new TextDecoder();
        let assistantContent = '';
        let hasCreatedAssistantMessage = false;

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(5);
              if (data === '[DONE]' || data === 'DONE') continue;

              try {
                if (!data.trim().startsWith('{')) continue;
                const parsed = JSON.parse(data);

                if (parsed.type === 'text' && parsed.data?.trim()) {
                  assistantContent += parsed.data;
                  
                  // Update assistant message
                  setMessages(prev => {
                    const updated = [...prev];
                    const lastMsg = updated[updated.length - 1];
                    
                    if (lastMsg?.role === 'assistant' && !hasCreatedAssistantMessage) {
                      lastMsg.content = assistantContent;
                    } else if (!hasCreatedAssistantMessage) {
                      updated.push({
                        role: 'assistant',
                        content: assistantContent,
                        id: `assistant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
                      });
                      hasCreatedAssistantMessage = true;
                    }
                    
                    return updated;
                  });
                }
              } catch (e) {
                debug.error('Parse error', e);
              }
            }
          }
        }

        // Fallback if no response
        if (!assistantContent && !hasCreatedAssistantMessage) {
          setMessages(prev => [...prev, {
            role: 'assistant',
            content: "I'd be happy to help you find the perfect place to stay! Could you tell me a bit more about what you're looking for?",
            id: `fallback_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
          }]);
        }

        setIsStreaming(false);
        return { success: true };

      } catch (error) {
        setIsStreaming(false);
        debug.error('Send message failed', error);
        
        setMessages(prev => [...prev, {
          role: 'assistant',
          content: "I'm having trouble connecting right now. Could you please try again in a moment?",
          id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
        }]);
        
        throw error;
      }
    },
    onError: (error) => {
      debug.error('Message mutation error', error);
      toast({
        title: "Connection Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive"
      });
    }
  });

  // Auto-scroll to bottom
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isStreaming) {
      sendMessage.mutate(input);
    }
  };

  // Quick action handlers
  const quickActions = [
    {
      icon: <MapPin />,
      title: "Find by Location",
      description: "Search for stays in a specific city or area",
      action: () => setInput("I want to find a place to stay in ")
    },
    {
      icon: <Calendar />,
      title: "Plan Trip Dates",
      description: "Help me choose the best dates for my trip",
      action: () => setInput("I need help planning when to travel")
    },
    {
      icon: <DollarSign />,
      title: "Budget Planning",
      description: "Find great options within my budget",
      action: () => setInput("I have a specific budget in mind")
    },
    {
      icon: <Users />,
      title: "Group Travel",
      description: "Perfect for families or groups",
      action: () => setInput("I'm traveling with a group")
    }
  ];

  const suggestions = [
    { text: "Beach resort in Miami", icon: <Waves /> },
    { text: "City hotel in NYC", icon: <Building /> },
    { text: "Mountain cabin getaway", icon: <MapIcon /> },
    { text: "Pet-friendly options", icon: <Hotel /> },
    { text: "Luxury under $300", icon: <Star /> },
    { text: "Family-friendly resorts", icon: <Users /> }
  ];

  return (
    <ChatContainer variant={variant}>
      <CardHeader className="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
              <Bot className="h-5 w-5 text-white" />
            </div>
            <div>
              <span className="text-lg font-semibold text-gray-800">AI Travel Assistant</span>
              <div className="text-sm text-gray-500">Powered by RoomLama AI</div>
            </div>
          </CardTitle>
          <div className="flex items-center gap-2">
            {messages.length > 1 && (
              <TooltipProvider>
                <Tooltip delayDuration={300}>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => {
                        setMessages([{
                          role: "assistant",
                          content: "Hi! I'm your AI travel assistant. I'll help you find the perfect place to stay by understanding your preferences and suggesting great options tailored just for you.",
                          id: `welcome_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
                        }]);
                        localStorage.removeItem('chatHistory');
                        localStorage.removeItem('ai_chat_trigger');
                        localStorage.removeItem('ai_chat_trigger_processed');
                        localStorage.removeItem('ai_chat_initial_user_message');
                        toast({ title: "Chat Reset", description: "Starting fresh!" });
                      }}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Start New Chat</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose} className="text-gray-500 hover:text-gray-700">
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0 flex-grow overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-6 space-y-6">
            {messages.map((message) => (
              <div key={message.id} className={`flex flex-col ${message.role === 'user' ? 'items-end' : 'items-start'}`}>
                <div className={`flex items-start gap-3 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'} w-full`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.role === 'user' 
                      ? 'bg-gradient-to-br from-blue-400 to-blue-600 text-white' 
                      : 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white'
                  }`}>
                    {message.role === 'user' ? (
                      <UserCircle className="w-5 h-5" />
                    ) : (
                      <Bot className="w-5 h-5" />
                    )}
                  </div>
                  <MessageBubble 
                    isUser={message.role === 'user'} 
                    isWelcome={message.role === 'assistant' && messages.indexOf(message) === 0}
                  >
                    {message.content}
                  </MessageBubble>
                </div>
              </div>
            ))}
            
            {isStreaming && (
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white flex items-center justify-center flex-shrink-0">
                  <Bot className="w-5 h-5" />
                </div>
                <LoadingDots>
                  <div className="dot"></div>
                  <div className="dot"></div>
                  <div className="dot"></div>
                </LoadingDots>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>
      
      <CardFooter className="flex-col p-6 border-t border-gray-100 gap-6 bg-gradient-to-r from-gray-50 to-blue-50">
        {messages.length === 1 && !isStreaming && (
          <WelcomeSection>
            <h3 className="text-xl font-semibold text-gray-800 mb-4">How can I help you today?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {quickActions.map((action, index) => (
                <QuickActionCard key={index} onClick={action.action}>
                  <div className="icon">{action.icon}</div>
                  <div className="content">
                    <div className="title">{action.title}</div>
                    <div className="description">{action.description}</div>
                  </div>
                  <ChevronRight className="arrow w-5 h-5" />
                </QuickActionCard>
              ))}
            </div>
            
            <div className="text-sm text-gray-600 mb-4">Or try these popular searches:</div>
            <div className="flex flex-wrap gap-2 justify-center">
              {suggestions.map((suggestion, index) => (
                <SuggestionChip key={index} onClick={() => setInput(suggestion.text)}>
                  {suggestion.icon}
                  {suggestion.text}
                </SuggestionChip>
              ))}
            </div>
          </WelcomeSection>
        )}
        
        <form onSubmit={handleSubmit} className="flex w-full items-center gap-3">
          <div className="relative flex-grow">
            <Input
              placeholder="Tell me what you're looking for..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              disabled={isStreaming}
              className="pr-12 h-12 rounded-full border-2 border-gray-200 focus:border-blue-400 focus:shadow-md focus:shadow-blue-500/30 bg-white/80 backdrop-blur-sm transition-all duration-200 ease-in-out"
              ref={inputRef}
            />
            <Button 
              type="submit" 
              size="icon" 
              disabled={isStreaming || !input.trim()}
              className="absolute right-2 top-2 h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </CardFooter>
    </ChatContainer>
  );
}