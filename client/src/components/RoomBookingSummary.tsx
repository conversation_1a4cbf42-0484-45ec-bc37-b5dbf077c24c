import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { formatCurrency } from '@/lib/utils.jsx';

interface RoomBookingSummaryProps {
  room: ExtendedRoom;
  checkIn: string;
  checkOut: string;
  guests: number;
}

export default function RoomBookingSummary({
  room,
  checkIn,
  checkOut,
  guests
}: RoomBookingSummaryProps) {
  const nights = Math.ceil(
    (new Date(checkOut).getTime() - new Date(checkIn).getTime()) / 
    (1000 * 60 * 60 * 24)
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Booking Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h4 className="font-medium mb-2">Room Details</h4>
            <div className="space-y-1">
              <p>{room.description}</p>
              <p className="text-sm text-muted-foreground">
                {room.bedType || room.bedTypeCode} • {room.maxOccupancy} max guests
              </p>
              <div className="flex gap-2 mt-1">
                {room.refundable && (
                  <Badge variant="outline" className="bg-green-50">
                    Free Cancellation
                  </Badge>
                )}
                {room.restrictedRate && (
                  <Badge variant="secondary">Restricted Rate</Badge>
                )}
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Stay Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Check-in</span>
                <span>{new Date(checkIn).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Check-out</span>
                <span>{new Date(checkOut).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Length of stay</span>
                <span>{nights} nights</span>
              </div>
              <div className="flex justify-between">
                <span>Guests</span>
                <span>{guests} guests</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Price Breakdown</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Room Rate ({nights} nights)</span>
                <span>{formatCurrency(room.rate * nights, room.currency)}</span>
              </div>

              {room.taxes?.map(tax => (
                <div key={tax.type} className="flex justify-between">
                  <span>{tax.type}</span>
                  <span>{formatCurrency(tax.amount, tax.currency)}</span>
                </div>
              ))}

              {room.fees?.map(fee => (
                <div key={fee.type} className="flex justify-between">
                  <span>{fee.type}</span>
                  <span>{formatCurrency(fee.amount, fee.currency)}</span>
                </div>
              ))}

              {room.totalDiscount > 0 && (
                <div className="flex justify-between text-destructive">
                  <span>Savings ({room.retailDiscountPercent}% off)</span>
                  <span>-{formatCurrency(room.totalDiscount, room.currency)}</span>
                </div>
              )}

              <div className="pt-2 border-t flex justify-between font-bold">
                <span>Total</span>
                <span>{formatCurrency(room.totalAmount, room.currency)}</span>
              </div>
            </div>
          </div>

          {room.promotions && room.promotions.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Applied Promotions</h4>
              <div className="space-y-2">
                {room.promotions.map(promo => (
                  <div key={promo.code} className="text-sm">
                    <p className="font-medium">{promo.description}</p>
                    <p className="text-muted-foreground">
                      Valid until {new Date(promo.endDate).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 