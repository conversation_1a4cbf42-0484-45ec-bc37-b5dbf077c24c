import { Link } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, MapPin, Phone } from "lucide-react";
import { Logo } from "./Logo";
import { Separator } from "@/components/ui/separator";

const companyLinks = [
  { title: "About Us", href: "/about" },
  { title: "Our Mission", href: "/mission" },
  { title: "Careers", href: "/careers" },
  { title: "Press", href: "/press" },
];

const supportLinks = [
  { title: "Help Center", href: "/help" },
  { title: "Safety Information", href: "/safety" },
  { title: "Cancellation Options", href: "/cancellation" },
  { title: "Report Concern", href: "/report" },
];

const legalLinks = [
  { title: "Privacy Policy", href: "/privacy" },
  { title: "Terms of Service", href: "/terms" },
  { title: "Cookie Policy", href: "/cookies" },
];

export default function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container px-4 py-8 md:py-12">
        <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Logo size="sm" />
              <span className="font-bold">RoomLama</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Your trusted guide in finding the perfect accommodation, combining modern comfort with timeless wisdom.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>1234 Wisdom Way, San Francisco, CA 94105</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Company</h3>
            <ul className="space-y-2">
              {companyLinks.map((link) => (
                <li key={link.href}>
                  <Link href={link.href}>
                    <Button variant="link" className="h-auto p-0 text-muted-foreground hover:text-primary">
                      {link.title}
                    </Button>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Support</h3>
            <ul className="space-y-2">
              {supportLinks.map((link) => (
                <li key={link.href}>
                  <Link href={link.href}>
                    <Button variant="link" className="h-auto p-0 text-muted-foreground hover:text-primary">
                      {link.title}
                    </Button>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Legal</h3>
            <ul className="space-y-2">
              {legalLinks.map((link) => (
                <li key={link.href}>
                  <Link href={link.href}>
                    <Button variant="link" className="h-auto p-0 text-muted-foreground hover:text-primary">
                      {link.title}
                    </Button>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <Separator className="my-8" />

        <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-sm text-muted-foreground">
            © {new Date().getFullYear()} RoomLama. All rights reserved.
          </p>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              English (US)
            </Button>
            <Button variant="ghost" size="sm">
              USD ($)
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
} 