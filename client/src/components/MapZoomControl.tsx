import React from 'react';
import { But<PERSON> } from "./ui/button.jsx";
import { Plus, Minus } from "lucide-react";

interface MapZoomControlProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
}

const MapZoomControl: React.FC<MapZoomControlProps> = ({
  onZoomIn,
  onZoomOut
}) => {
  return (
    <div className="absolute right-4 bottom-8 z-10 flex flex-col gap-2">
      <Button
        variant="secondary"
        size="icon"
        className="h-8 w-8 shadow-lg"
        onClick={onZoomIn}
      >
        <Plus className="h-4 w-4" />
        <span className="sr-only">Zoom in</span>
      </Button>
      <Button
        variant="secondary"
        size="icon"
        className="h-8 w-8 shadow-lg"
        onClick={onZoomOut}
      >
        <Minus className="h-4 w-4" />
        <span className="sr-only">Zoom out</span>
      </Button>
    </div>
  );
};

export default MapZoomControl;