/**
 * Enhanced AI Chat Component
 * 
 * This component provides an improved AI travel planning experience with:
 * - Proactive Intelligence (suggests alternatives, alerts about events)
 * - Enhanced Location Understanding (neighborhood-level recommendations)
 * - Improved Technical Architecture (server-side state management)
 * - Comprehensive Testing (better conversation quality)
 */
import React, { useState, useRef, useEffect, useCallback } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  MessageCircle, 
  Send, 
  MapPin, 
  Hotel, 
  Calendar, 
  DollarSign, 
  Star, 
  X, 
  ChevronRight, 
  Search, 
  Lightbulb, 
  Info, 
  AlertTriangle,
  Clock
} from "lucide-react";
import { useLocation } from "wouter";
import styled from '@emotion/styled';
import { cn } from "@/lib/utils";
import { useGooglePlaces } from "@/hooks/use-google-places";
import { useToast } from "@/hooks/use-toast";
import { PropertyWithRates } from "@/types/schema";
import PropertyCard from "./PropertyCard";
import LocationMarker from "./LocationMarker";

// Styled components from regular AiChat carried over
const ChatContainer = styled(Card)<{ variant: 'integrated' | 'floating' | 'modal' }>`
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);

  ${props => {
    switch (props.variant) {
      case 'modal':
        return `
          position: fixed;
          inset: 0;
          width: 100vw;
          height: 100vh;
          max-width: 100vw;
          margin: 0;
          border-radius: 0;
          z-index: 1000;
          background: rgba(255, 255, 255, 0.98);
          backdrop-filter: blur(8px);
          animation: slideIn 0.3s ease-out;

          @keyframes slideIn {
            from {
              transform: translateY(100%);
              opacity: 0;
            }
            to {
              transform: translateY(0);
              opacity: 1;
            }
          }
        `;
      case 'integrated':
        return `
          height: 100%;
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
        `;
      case 'floating':
        return `
          height: 600px;
          width: 380px;
          position: fixed;
          right: 24px;
          bottom: 24px;
          z-index: 1000;
        `;
    }
  }}
`;

const MessageBubble = styled.div<{ isUser: boolean; enhanced?: boolean }>`
  max-width: 85%;
  padding: 16px;
  margin: 8px 12px;
  border-radius: ${props => props.isUser ? '16px 16px 0 16px' : '16px 16px 16px 0'};
  background: ${props => props.isUser ? '#E1F0FF' : (props.enhanced ? '#f0fff4' : '#ffffff')};
  color: ${props => props.isUser ? '#0066cc' : '#1a1a1a'};
  align-self: ${props => props.isUser ? 'flex-end' : 'flex-start'};
  font-size: 1rem;
  line-height: 1.6;
  word-wrap: break-word;
  box-shadow: ${props => props.isUser ? 
    '0 2px 4px rgba(0, 102, 204, 0.1)' : 
    (props.enhanced ? '0 2px 8px rgba(0, 120, 0, 0.1)' : '0 2px 8px rgba(0, 0, 0, 0.05)')};
  border: ${props => props.isUser ? 
    '1px solid rgba(0, 102, 204, 0.1)' : 
    (props.enhanced ? '1px solid rgba(0, 180, 0, 0.1)' : '1px solid #e5e7eb')};
  transition: all 0.2s ease;

  /* Enhanced markdown formatting */
  p {
    margin-bottom: 1.25rem;
    &:last-child {
      margin-bottom: 0;
    }
  }

  ul, ol {
    margin: 1rem 0;
    padding-left: 1.75rem;
  }

  li {
    margin: 0.75rem 0;
    position: relative;
    padding-left: 0.75rem;
    line-height: 1.5;
  }

  strong {
    font-weight: 600;
    color: ${props => props.isUser ? '#0066cc' : (props.enhanced ? '#007700' : '#0066cc')};
    background: ${props => props.isUser ? 
      'rgba(0, 102, 204, 0.08)' : 
      (props.enhanced ? 'rgba(0, 120, 0, 0.08)' : 'rgba(0, 102, 204, 0.08)')};
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
  }

  em {
    font-style: italic;
    opacity: 0.85;
  }

  a {
    color: ${props => props.isUser ? '#0066cc' : (props.enhanced ? '#007700' : '#0066cc')};
    text-decoration: underline;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
    &:hover {
      text-decoration: none;
      background: ${props => props.isUser ? 
        'rgba(0, 102, 204, 0.08)' : 
        (props.enhanced ? 'rgba(0, 120, 0, 0.08)' : 'rgba(0, 102, 204, 0.08)')};
      border-radius: 2px;
    }
  }

  &:hover {
    box-shadow: ${props => props.isUser ? 
      '0 4px 8px rgba(0, 102, 204, 0.15)' : 
      (props.enhanced ? '0 4px 12px rgba(0, 120, 0, 0.15)' : '0 4px 12px rgba(0, 0, 0, 0.08)')};
  }
`;

const InsightBubble = styled.div`
  max-width: 85%;
  padding: 12px 16px;
  margin: 8px 12px;
  border-radius: 16px;
  background: #fffdf0;
  color: #664d00;
  align-self: flex-start;
  font-size: 0.95rem;
  line-height: 1.5;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(102, 77, 0, 0.1);
  border: 1px solid rgba(102, 77, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .insight-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 223, 0, 0.2);
    border-radius: 50%;
    padding: 8px;
    color: #664d00;
    flex-shrink: 0;
  }

  .insight-content {
    flex: 1;
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(102, 77, 0, 0.15);
  }
`;

const AlertBubble = styled.div`
  max-width: 85%;
  padding: 12px 16px;
  margin: 8px 12px;
  border-radius: 16px;
  background: #fff5f5;
  color: #cc0000;
  align-self: flex-start;
  font-size: 0.95rem;
  line-height: 1.5;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(204, 0, 0, 0.1);
  border: 1px solid rgba(204, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .alert-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 0, 0, 0.1);
    border-radius: 50%;
    padding: 8px;
    color: #cc0000;
    flex-shrink: 0;
  }

  .alert-content {
    flex: 1;
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(204, 0, 0, 0.15);
  }
`;

const TipBubble = styled.div`
  max-width: 85%;
  padding: 12px 16px;
  margin: 8px 12px;
  border-radius: 16px;
  background: #f0f7ff;
  color: #0066cc;
  align-self: flex-start;
  font-size: 0.95rem;
  line-height: 1.5;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 102, 204, 0.1);
  border: 1px solid rgba(0, 102, 204, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .tip-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 102, 204, 0.1);
    border-radius: 50%;
    padding: 8px;
    color: #0066cc;
    flex-shrink: 0;
  }

  .tip-content {
    flex: 1;
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 102, 204, 0.15);
  }
`;

const ActionCard = styled.div`
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  margin: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    border-color: #0066cc;
  }

  .action-content {
    flex: 1;
  }

  .action-title {
    font-weight: 600;
    color: #0066cc;
    margin-bottom: 6px;
    font-size: 1.05rem;
  }

  .action-description {
    font-size: 0.925rem;
    color: #4b5563;
    line-height: 1.5;
  }

  .action-icon {
    width: 48px;
    height: 48px;
    background: #f0f7ff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0066cc;
    flex-shrink: 0;
    transition: all 0.2s ease;
  }

  &:hover .action-icon {
    background: #0066cc;
    color: #ffffff;
    transform: scale(1.05);
  }

  .action-arrow {
    color: #94a3b8;
    transition: transform 0.2s ease;
  }

  &:hover .action-arrow {
    transform: translateX(4px);
    color: #0066cc;
  }
`;

const LoadingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: #666;
  font-style: italic;
  
  @keyframes pulse {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
  }
  
  animation: pulse 1.5s infinite;
`;

const QuickActionBar = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
`;

const QuickActionChip = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  background: #f0f7ff;
  color: #0066cc;
  font-size: 0.875rem;
  border: 1px solid #0066cc;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: #0066cc;
    color: #ffffff;
    transform: translateY(-1px);
  }

  svg {
    width: 14px;
    height: 14px;
  }
`;

const AlternativeCard = styled.div`
  background: #f0fff4;
  border: 1px solid #d1e7dd;
  border-radius: 12px;
  padding: 16px;
  margin: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 14px;
  box-shadow: 0 2px 4px rgba(0, 120, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 120, 0, 0.1);
    border-color: #007700;
  }

  .alternative-content {
    flex: 1;
  }

  .alternative-title {
    font-weight: 600;
    color: #007700;
    margin-bottom: 6px;
    font-size: 1.05rem;
  }

  .alternative-description {
    font-size: 0.925rem;
    color: #4b5563;
    line-height: 1.5;
  }

  .alternative-icon {
    width: 48px;
    height: 48px;
    background: #e6f7ef;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #007700;
    flex-shrink: 0;
    transition: all 0.2s ease;
  }

  &:hover .alternative-icon {
    background: #007700;
    color: #ffffff;
    transform: scale(1.05);
  }
`;

// Debug logger
const debug = {
  log: (context: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[EnhancedAIChat] ${context}:`, data);
    }
  },
  error: (context: string, error: any) => {
    console.error(`[EnhancedAIChat] Error in ${context}:`, error);
    if (error?.response) {
      console.error('Response:', error.response);
    }
  }
};

// Interface definitions
interface Message {
  role: "user" | "assistant";
  content: string;
  id?: string;
  enhanced?: boolean;
  suggestedProperties?: PropertyWithRates[];
  location?: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
    enhancedDetails?: {
      neighborhoods?: string[];
      landmarks?: string[];
      popularTimes?: {
        season: string;
        highDemandDates: string[];
        events: string[];
      };
      weatherPattern?: {
        season: string;
        temperature: {
          min: number;
          max: number;
          unit: string;
        };
        precipitation: string;
      };
    };
  };
  insights?: {
    pricingInsight?: {
      message: string;
      type: string;
    };
    eventAlert?: {
      message: string;
      type: string;
      impact: 'high' | 'medium' | 'low';
    };
    tip?: string;
  };
  alternativeLocations?: Array<{
    name: string;
    reason: string;
    lat: number;
    lng: number;
  }>;
  actions?: Array<{
    type: 'location' | 'property' | 'search' | 'filter' | 'info';
    label: string;
    data: any;
    icon?: React.ReactNode;
  }>;
}

interface EnhancedAIChatProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    rooms?: string | null;
    properties?: PropertyWithRates[];
    filters?: {
      propertyTypes: string[];
      amenities: string[];
      priceRange: [number, number];
      minRating: number;
    };
    searchHistory?: Array<any>;
  };
  variant?: 'integrated' | 'floating' | 'modal';
  onClose?: () => void;
}

interface ConversationState {
  location?: {
    name: string;
    lat?: number;
    lng?: number;
    placeType?: string;
  };
  dates?: {
    checkIn: string;
    checkOut: string;
  };
  preferences?: {
    budget?: number;
    amenities?: string[];
    propertyTypes?: string[];
    guestCount?: number;
    roomCount?: number;
    travelPurpose?: string;
    priceRange?: [number, number];
    rating?: number;
    features?: string[];
  };
  lastRecommendations?: string[];
  searchHistory?: Array<{
    query: string;
    timestamp: number;
    results?: string[];
  }>;
  currentSearch?: {
    filters: {
      propertyTypes?: string[];
      amenities?: string[];
      priceRange?: [number, number];
      rating?: number;
    };
    sortBy?: string;
    page?: number;
  };
}

/**
 * Enhanced AI Chat component with improved intelligence
 */
export default function EnhancedAIChat({ context, variant = 'floating', onClose }: EnhancedAIChatProps) {
  const { toast } = useToast();
  const { geocodeAddress } = useGooglePlaces();
  const [_, navigate] = useLocation();
  
  // Enhanced conversation state initialization
  const [conversationState, setConversationState] = useState<ConversationState>(() => ({
    location: context?.location ? { name: context.location } : undefined,
    dates: context?.checkIn && context?.checkOut ? {
      checkIn: context.checkIn,
      checkOut: context.checkOut
    } : undefined,
    preferences: {
      guestCount: context?.guests ? parseInt(context.guests) : undefined,
      roomCount: context?.rooms ? parseInt(context.rooms) : undefined,
      propertyTypes: context?.filters?.propertyTypes || [],
      amenities: context?.filters?.amenities || [],
      priceRange: context?.filters?.priceRange,
      rating: context?.filters?.minRating
    },
    currentSearch: context?.filters ? {
      filters: {
        propertyTypes: context.filters.propertyTypes,
        amenities: context.filters.amenities,
        priceRange: context.filters.priceRange,
        rating: context.filters.minRating
      }
    } : undefined,
    searchHistory: []
  }));

  // Load conversation state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('enhancedConversationState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState) as ConversationState;
        setConversationState(currentState => ({
          ...currentState,
          ...parsedState
        }));
      } catch (e) {
        console.error('Failed to parse saved conversation state:', e);
      }
    }
  }, []);

  // Function to update conversation state with search context
  const updateSearchContext = useCallback((newContext: Partial<ConversationState>) => {
    setConversationState(currentState => {
      const updated = {
        ...currentState,
        ...newContext,
        searchHistory: [
          ...(currentState.searchHistory || []),
          {
            query: newContext.location?.name || '',
            timestamp: Date.now(),
            results: newContext.lastRecommendations
          }
        ].slice(-5) // Keep last 5 searches
      };

      // Store in localStorage for persistence
      localStorage.setItem('enhancedConversationState', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Load saved messages from localStorage on initial render
  const [messages, setMessages] = useState<Message[]>(() => {
    const saved = localStorage.getItem('enhancedChatHistory');
    const savedMessages = saved ? JSON.parse(saved) : [];
    
    // Process saved messages to ensure all have IDs
    const messagesWithIds = savedMessages.map((msg: any) => {
      if (!msg.id) {
        // Add ID to messages that don't have one
        return {
          ...msg,
          id: `${msg.role}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
        };
      }
      return msg;
    });
    
    // Only add welcome message if no saved messages
    if (messagesWithIds.length === 0) {
      const welcomeMessageId = `welcome_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      return [{
        role: "assistant",
        content: context 
          ? `👋 Hi! I'm your enhanced AI travel assistant. I can help you find the perfect stay${
              context.location ? ` in ${context.location}` : ''
            }${
              context.checkIn && context.checkOut 
                ? ` from ${new Date(context.checkIn).toLocaleDateString()} to ${new Date(context.checkOut).toLocaleDateString()}` 
                : ''
            }. I can recommend properties, compare options, and provide travel insights like seasonal events and pricing trends.${
              context.properties && context.properties.length > 0
                ? "\n\nI see some great options available. Would you like me to recommend the best ones based on your needs?"
                : "\n\nJust let me know what you're looking for!"
            }`
          : "👋 Hi! I'm your enhanced AI travel assistant. I can help you find the perfect place to stay, compare options, and provide travel insights like seasonal events and pricing trends. What kind of accommodation are you looking for?",
        id: welcomeMessageId
      }];
    }
    
    return messagesWithIds;
  });

  // Maintain input state
  const [input, setInput] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const initialProcessRef = useRef(false);
  const sessionIdRef = useRef<string>(`session-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Save messages to localStorage when they change
  useEffect(() => {
    localStorage.setItem('enhancedChatHistory', JSON.stringify(messages));
  }, [messages]);

  // Focus input field on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Handle sending a message
  const sendMessage = useMutation({
    mutationFn: async (message: string) => {
      debug.log('Sending message', { message, context, conversationState, sessionId: sessionIdRef.current });
      setIsStreaming(true);
      
      try {
        // Gather comprehensive context from the current state
        const enhancedContext = {
          // Basic information from props
          ...context,
          
          // Location information
          location: context?.location || (conversationState.location ? {
            name: conversationState.location.name,
            latitude: conversationState.location.lat,
            longitude: conversationState.location.lng
          } : undefined),
          
          // Date range information
          dateRange: context?.checkIn && context?.checkOut 
            ? { checkIn: context.checkIn, checkOut: context.checkOut }
            : conversationState.dates,
          
          // Guest and room information
          guests: context?.guests || conversationState.preferences?.guestCount,
          rooms: context?.rooms,
          
          // User preferences
          preferences: {
            amenities: conversationState.preferences?.amenities || [],
            propertyTypes: conversationState.preferences?.propertyTypes || [],
            priceRange: conversationState.preferences?.priceRange,
            guestCount: conversationState.preferences?.guestCount,
            travelPurpose: conversationState.preferences?.travelPurpose
          },
          
          searchHistory: conversationState.searchHistory || [],
          extractLocation: true // Request explicit location extraction
        };
        
        debug.log('Enhanced context', enhancedContext);

        // Create the request payload
        const payload = {
          message,
          sessionId: sessionIdRef.current,
          context: enhancedContext,
          extractLocation: true
        };

        // Generate a unique message ID for tracking
        const messageId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

        // Add user message to the chat immediately
        setMessages(prev => [...prev, { 
          role: "user", 
          content: message,
          id: messageId
        }]);

        // Reset input
        setInput("");

        // Use the enhanced API endpoint
        const response = await fetch("/api/chat/enhanced", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }

        // Check if the response is an event stream
        const contentType = response.headers.get("Content-Type");
        if (contentType && contentType.includes("text/event-stream")) {
          // Process the event stream
          const reader = response.body?.getReader();
          if (!reader) throw new Error("Response body is not readable");

          const decoder = new TextDecoder();
          let buffer = "";
          let locationReceived = false;
          let currentAssistantMessage: Message = {
            role: "assistant",
            content: "",
            id: `assistant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
          };

          while (true) {
            const { value, done } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            
            // Process complete events in the buffer
            const events = buffer.split("\n\n");
            buffer = events.pop() || "";

            for (const event of events) {
              if (!event.trim() || !event.startsWith("data: ")) continue;
              
              const data = event.replace(/^data: /, "").trim();
              if (data === "[DONE]") {
                setIsStreaming(false);
                break;
              }

              try {
                const parsed = JSON.parse(data);
                
                // Process different response types
                switch (parsed.type) {
                  case 'text':
                    if (parsed.data.isLoading) {
                      // Show loading message temporarily
                      setMessages(prev => [
                        ...prev.filter(m => m.id !== currentAssistantMessage.id), 
                        { 
                          ...currentAssistantMessage, 
                          content: parsed.data.content,
                          enhanced: !!parsed.data.enhancedWithInsights
                        }
                      ]);
                    } else {
                      // Update with actual content
                      currentAssistantMessage = {
                        ...currentAssistantMessage,
                        content: parsed.data.content,
                        enhanced: !!parsed.data.enhancedWithInsights,
                        insights: parsed.data.insights
                      };
                      
                      setMessages(prev => [
                        ...prev.filter(m => m.id !== currentAssistantMessage.id), 
                        currentAssistantMessage
                      ]);
                    }
                    break;
                    
                  case 'location':
                    if (!locationReceived) {
                      locationReceived = true;
                      
                      // Update currentAssistantMessage with location
                      currentAssistantMessage = {
                        ...currentAssistantMessage,
                        location: parsed.data.location
                      };
                      
                      // Update conversation state with location
                      updateSearchContext({
                        location: {
                          name: parsed.data.location.name,
                          lat: parsed.data.location.lat,
                          lng: parsed.data.location.lng,
                          placeType: parsed.data.location.placeType
                        }
                      });
                      
                      // If we have enhanced details, include them
                      if (parsed.data.enhancedDetails) {
                        currentAssistantMessage.location.enhancedDetails = parsed.data.enhancedDetails;
                      }
                    }
                    break;
                    
                  case 'properties':
                    // Convert API properties to schema properties
                    const convertedProperties = parsed.data.properties.map((prop: any) => ({
                      ...prop,
                      latitude: typeof prop.latitude === 'string' ? parseFloat(prop.latitude) : prop.latitude,
                      longitude: typeof prop.longitude === 'string' ? parseFloat(prop.longitude) : prop.longitude,
                      basePrice: typeof prop.basePrice === 'string' ? parseFloat(prop.basePrice) : prop.basePrice,
                      currency: prop.currency || 'USD',
                      rates: prop.rates || [],
                      amenities: prop.amenities || [],
                      images: prop.images || [],
                      reviews: prop.reviews || [],
                      lastUpdated: new Date(prop.lastUpdated || Date.now())
                    }));
                    
                    // Update currentAssistantMessage with properties
                    currentAssistantMessage = {
                      ...currentAssistantMessage,
                      suggestedProperties: convertedProperties
                    };
                    
                    // If we have alternative locations, include them
                    if (parsed.data.alternativeLocations) {
                      currentAssistantMessage.alternativeLocations = parsed.data.alternativeLocations;
                    }
                    
                    setMessages(prev => [
                      ...prev.filter(m => m.id !== currentAssistantMessage.id), 
                      currentAssistantMessage
                    ]);
                    break;
                    
                  case 'action':
                    // Add actions to the message
                    const actions = Array.isArray(currentAssistantMessage.actions) 
                      ? [...currentAssistantMessage.actions]
                      : [];
                      
                    actions.push({
                      type: parsed.data.type,
                      label: parsed.data.label,
                      data: parsed.data.data,
                      icon: getActionIcon(parsed.data.type)
                    });
                    
                    currentAssistantMessage = {
                      ...currentAssistantMessage,
                      actions
                    };
                    
                    setMessages(prev => [
                      ...prev.filter(m => m.id !== currentAssistantMessage.id), 
                      currentAssistantMessage
                    ]);
                    break;
                    
                  case 'error':
                    // Display error message
                    toast({
                      title: "Error",
                      description: parsed.data.message,
                      variant: "destructive"
                    });
                    
                    setMessages(prev => [
                      ...prev, 
                      { 
                        role: "assistant", 
                        content: parsed.data.message || "Sorry, I'm having trouble processing your request. Please try again.",
                        id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
                      }
                    ]);
                    break;
                }
              } catch (err) {
                debug.error('Error parsing SSE data', err);
                console.error('Raw data:', data);
              }
            }
          }
        } else {
          // Handle non-streaming response
          const data = await response.json();
          setMessages(prev => [
            ...prev, 
            { 
              role: "assistant", 
              content: data.message || "I'm sorry, there was an issue with my response.",
              id: `assistant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
            }
          ]);
        }
      } catch (error) {
        debug.error('Error in sendMessage', error);
        
        toast({
          title: "Error",
          description: "Sorry, I couldn't process your message. Please try again.",
          variant: "destructive"
        });
        
        setMessages(prev => [
          ...prev, 
          { 
            role: "assistant", 
            content: "Sorry, I'm having trouble connecting to my services. Please try again in a moment.",
            id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
          }
        ]);
      } finally {
        setIsStreaming(false);
      }
    }
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isStreaming) return;
    
    sendMessage.mutate(input);
  };
  
  // Handle clicks on locations
  const handleLocationClick = (location: Message['location']) => {
    if (!location) return;
    
    // Geocode for better coordinates if needed
    if (location.lat === 0 && location.lng === 0) {
      geocodeAddress(location.name)
        .then(geoResult => {
          navigate(`/search?location=${encodeURIComponent(location.name)}&lat=${geoResult.lat}&lng=${geoResult.lng}`);
        })
        .catch(error => {
          debug.error('Geocoding error', error);
          navigate(`/search?location=${encodeURIComponent(location.name)}`);
        });
    } else {
      navigate(`/search?location=${encodeURIComponent(location.name)}&lat=${location.lat}&lng=${location.lng}`);
    }
  };
  
  // Handle clicks on properties
  const handlePropertyClick = (property: PropertyWithRates) => {
    navigate(`/property/${property.id}`);
  };
  
  // Handle clicks on alternative locations
  const handleAlternativeClick = (alternative: Message['alternativeLocations'][0]) => {
    navigate(`/search?location=${encodeURIComponent(alternative.name)}&lat=${alternative.lat}&lng=${alternative.lng}`);
  };
  
  // Process any initial message
  useEffect(() => {
    // Check if we have a trigger flag
    const shouldTrigger = localStorage.getItem('ai_chat_trigger') === 'true';
    
    // Get chat history
    const savedHistory = localStorage.getItem('enhancedChatHistory');
    const savedMessages = savedHistory ? JSON.parse(savedHistory) : [];
    
    // Only process if we have a trigger and messages, and haven't processed yet
    if (shouldTrigger && savedMessages.length > 0 && !initialProcessRef.current) {
      initialProcessRef.current = true;
      
      // We need to make sure sendMessage is already initialized
      if (sendMessage && sendMessage.mutate) {
        sendMessage.mutate(savedMessages[0].content);
      }
      
      // Remove the trigger flag
      localStorage.removeItem('ai_chat_trigger');
    }
  }, [sendMessage]); // Include sendMessage in dependencies

  // Get an icon for an action type
  function getActionIcon(type: string) {
    switch (type) {
      case 'location':
        return <MapPin />;
      case 'property':
        return <Hotel />;
      case 'search':
        return <Search />;
      case 'filter':
        return <ListFilter />;
      case 'info':
        return <Info />;
      default:
        return <MessageCircle />;
    }
  }

  return (
    <ChatContainer variant={variant}>
      <CardHeader className="px-4 py-3 border-b flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="bg-blue-100 p-2 rounded-full">
            <MessageCircle className="h-5 w-5 text-blue-500" />
          </div>
          <CardTitle className="text-lg">AI Travel Assistant</CardTitle>
          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Enhanced</span>
        </div>
        {onClose && (
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full h-8 w-8"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </CardHeader>
      
      <ScrollArea className="flex-1 p-4">
        <div className="flex flex-col">
          {messages.map((message, index) => (
            <div
              key={message.id || index}
              className={cn("flex", message.role === "user" ? "justify-end" : "justify-start")}
            >
              {message.role === "assistant" ? (
                <div className="flex flex-col space-y-2 max-w-[85%]">
                  {/* Main message content */}
                  <MessageBubble 
                    isUser={false} 
                    enhanced={message.enhanced}
                    dangerouslySetInnerHTML={{ __html: message.content.replace(/\n/g, '<br />') }}
                  />
                  
                  {/* Show insights if available */}
                  {message.insights?.pricingInsight && (
                    <InsightBubble>
                      <div className="insight-icon">
                        <DollarSign className="h-4 w-4" />
                      </div>
                      <div className="insight-content">
                        {message.insights.pricingInsight.message}
                      </div>
                    </InsightBubble>
                  )}
                  
                  {message.insights?.eventAlert && (
                    <AlertBubble>
                      <div className="alert-icon">
                        <AlertTriangle className="h-4 w-4" />
                      </div>
                      <div className="alert-content">
                        {message.insights.eventAlert.message}
                      </div>
                    </AlertBubble>
                  )}
                  
                  {message.insights?.tip && (
                    <TipBubble>
                      <div className="tip-icon">
                        <Lightbulb className="h-4 w-4" />
                      </div>
                      <div className="tip-content">
                        {message.insights.tip}
                      </div>
                    </TipBubble>
                  )}
                  
                  {/* Show location if available */}
                  {message.location && (
                    <div className="ml-4 mt-2">
                      <LocationMarker
                        location={message.location}
                        onClick={() => handleLocationClick(message.location)}
                      />
                      
                      {/* Show enhanced location details if available */}
                      {message.location.enhancedDetails && (
                        <div className="mt-2 text-sm text-muted-foreground bg-slate-50 p-3 rounded-md">
                          {message.location.enhancedDetails.neighborhoods && message.location.enhancedDetails.neighborhoods.length > 0 && (
                            <div className="mb-2">
                              <span className="font-medium">Notable areas:</span> {message.location.enhancedDetails.neighborhoods.join(', ')}
                            </div>
                          )}
                          
                          {message.location.enhancedDetails.landmarks && message.location.enhancedDetails.landmarks.length > 0 && (
                            <div className="mb-2">
                              <span className="font-medium">Landmarks:</span> {message.location.enhancedDetails.landmarks.join(', ')}
                            </div>
                          )}
                          
                          {message.location.enhancedDetails.popularTimes && message.location.enhancedDetails.popularTimes.events.length > 0 && (
                            <div>
                              <span className="font-medium">Current events:</span> {message.location.enhancedDetails.popularTimes.events.join(', ')}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Show properties if available */}
                  {message.suggestedProperties && message.suggestedProperties.length > 0 && (
                    <div className="mt-2 space-y-4">
                      {message.suggestedProperties.slice(0, 3).map((property) => (
                        <PropertyCard
                          key={property.id}
                          property={property}
                          onClick={() => handlePropertyClick(property)}
                        />
                      ))}
                      
                      {message.suggestedProperties.length > 3 && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => handleLocationClick(message.location)}
                        >
                          View all {message.suggestedProperties.length} properties
                        </Button>
                      )}
                    </div>
                  )}
                  
                  {/* Show alternative locations if available */}
                  {message.alternativeLocations && message.alternativeLocations.length > 0 && (
                    <div className="mt-2 space-y-2">
                      <div className="text-sm font-medium text-muted-foreground ml-2">Alternative destinations you might like:</div>
                      
                      {message.alternativeLocations.map((alternative, idx) => (
                        <AlternativeCard
                          key={idx}
                          onClick={() => handleAlternativeClick(alternative)}
                        >
                          <div className="alternative-icon">
                            <MapPin className="h-5 w-5" />
                          </div>
                          <div className="alternative-content">
                            <div className="alternative-title">{alternative.name}</div>
                            <div className="alternative-description">{alternative.reason}</div>
                          </div>
                          <ChevronRight className="h-5 w-5 text-muted-foreground" />
                        </AlternativeCard>
                      ))}
                    </div>
                  )}
                  
                  {/* Show actions if available */}
                  {message.actions && message.actions.length > 0 && (
                    <QuickActionBar>
                      {message.actions.map((action, idx) => (
                        <QuickActionChip
                          key={idx}
                          onClick={() => {
                            if (action.type === 'location' && action.data) {
                              handleLocationClick(action.data);
                            } else if (action.type === 'property' && action.data) {
                              handlePropertyClick(action.data);
                            } else if (action.type === 'search' && action.data?.query) {
                              setInput(action.data.query);
                              inputRef.current?.focus();
                            }
                          }}
                        >
                          {action.icon}
                          {action.label}
                        </QuickActionChip>
                      ))}
                    </QuickActionBar>
                  )}
                </div>
              ) : (
                <MessageBubble isUser={true}>
                  {message.content}
                </MessageBubble>
              )}
            </div>
          ))}
          
          {isStreaming && (
            <LoadingIndicator>
              <div className="w-5 h-5 rounded-full border-2 border-t-transparent border-blue-500 animate-spin" />
              <span>Thinking...</span>
            </LoadingIndicator>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      
      <CardFooter className="p-4 border-t">
        <form onSubmit={handleSubmit} className="w-full flex gap-2">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type a message..."
            className="flex-1"
            disabled={isStreaming}
          />
          <Button type="submit" disabled={!input.trim() || isStreaming}>
            <Send className="h-4 w-4 mr-2" />
            Send
          </Button>
        </form>
      </CardFooter>
    </ChatContainer>
  );
}