import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { formatCurrency } from '@/lib/utils.jsx';
import { Tag, Calendar, Percent } from 'lucide-react';

interface RoomPromotionsProps { 
  room: ExtendedRoom;
}

export default function RoomPromotions({ room }: RoomPromotionsProps) {
  if (!room.promotions?.length) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Special Offers & Promotions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {room.promotions.map((promo) => (
            <div key={promo.code} className="pb-6 last:pb-0 border-b last:border-0">
              <div className="flex items-start gap-3">
                {promo.discountType === 'Percentage' ? (
                  <Percent className="h-5 w-5 text-destructive mt-1" />
                ) : (
                  <Tag className="h-5 w-5 text-destructive mt-1" />
                )}
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{promo.description}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground">
                          Valid until {new Date(promo.endDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Badge variant="destructive">
                      {promo.discountType === 'Percentage'
                        ? `${promo.discountValue}% off`
                        : formatCurrency(promo.discountValue, room.currency)}
                    </Badge>
                  </div>

                  {promo.terms && (
                    <div className="mt-3">
                      <p className="text-sm font-medium">Terms & Conditions</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {promo.terms}
                      </p>
                    </div>
                  )}

                  {promo.minimumStay && (
                    <Badge variant="outline" className="mt-3">
                      Minimum stay: {promo.minimumStay} nights
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}

          {room.totalDiscount > 0 && (
            <div className="pt-4 border-t">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Total Savings</p>
                  <p className="text-sm text-muted-foreground">
                    Save {room.retailDiscountPercent}% on your stay
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-destructive">
                    {formatCurrency(room.totalDiscount, room.currency)}
                  </p>
                  <p className="text-sm text-muted-foreground line-through">
                    Original price: {formatCurrency(room.totalAmount + room.totalDiscount, room.currency)}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 