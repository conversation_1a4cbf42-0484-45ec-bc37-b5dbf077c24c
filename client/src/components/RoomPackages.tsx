import { ExtendedRoom } from '@/types/schema.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { RadioGroup, RadioGroupItem } from './ui/radio-group.jsx';
import { Label } from './ui/label.jsx';
import { formatCurrency } from '@/lib/utils.jsx';
import { 
  Package, 
  Utensils, 
  Car, 
  Wifi, 
  Bath, 
  Wine 
} from 'lucide-react';

interface RoomPackagesProps {
  room: ExtendedRoom;
  selectedPackage: string | null;
  onPackageSelect: (packageId: string) => void;
}

const PACKAGE_ICONS: Record<string, any> = {
  'Breakfast': Utensils,
  'Parking': Car,
  'Internet': Wifi,
  'Spa': Bath,
  'Dining': Wine
};

export default function RoomPackages({
  room,
  selectedPackage,
  onPackageSelect
}: RoomPackagesProps) {
  if (!room.packages?.length) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Available Packages</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <RadioGroup
            value={selectedPackage || ''}
            onValueChange={onPackageSelect}
          >
            {room.packages.map((pkg) => {
              const IconComponent = PACKAGE_ICONS[pkg.type] || Package;
              return (
                <div
                  key={pkg.id}
                  className="flex items-start space-x-4 p-4 rounded-lg border"
                >
                  <RadioGroupItem value={pkg.id} id={pkg.id} className="mt-1" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <Label
                          htmlFor={pkg.id}
                          className="font-medium flex items-center gap-2"
                        >
                          <IconComponent className="h-4 w-4" />
                          {pkg.name}
                        </Label>
                        <p className="text-sm text-muted-foreground mt-1">
                          {pkg.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">
                          +{formatCurrency(pkg.price, room.currency)}
                        </Badge>
                        {pkg.savings > 0 && (
                          <p className="text-sm text-destructive mt-1">
                            Save {formatCurrency(pkg.savings, room.currency)}
                          </p>
                        )}
                      </div>
                    </div>

                    {pkg.includes && pkg.includes.length > 0 && (
                      <div className="mt-2 text-sm">
                        <p className="font-medium">Includes:</p>
                        <ul className="list-disc pl-5 mt-1">
                          {pkg.includes.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </RadioGroup>
        </div>
      </CardContent>
    </Card>
  );
} 