import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card.jsx';
import { Textarea } from './ui/textarea.jsx';
import { Checkbox } from './ui/checkbox.jsx';
import { Label } from './ui/label.jsx';
import { MessageSquare, Clock, BedDouble, Utensils } from 'lucide-react';

interface RoomSpecialRequestsProps {
  room: ExtendedRoom;
  onRequestChange: (requests: string[]) => void;
  specialRequests: string[];
  additionalNotes: string;
  onNotesChange: (notes: string) => void;
}

const COMMON_REQUESTS = [
  {
    id: 'early-checkin',
    label: 'Early Check-in',
    icon: Clock,
    description: 'Subject to availability'
  },
  {
    id: 'late-checkout',
    label: 'Late Check-out',
    icon: Clock,
    description: 'Subject to availability'
  },
  {
    id: 'high-floor',
    label: 'High Floor',
    icon: BedDouble,
    description: 'If available'
  },
  {
    id: 'quiet-room',
    label: 'Quiet Room',
    icon: BedDouble,
    description: 'Away from elevator/ice machine'
  },
  {
    id: 'connecting-rooms',
    label: 'Connecting Rooms',
    icon: BedDouble,
    description: 'Subject to availability'
  },
  {
    id: 'dietary-restrictions',
    label: 'Dietary Restrictions',
    icon: Utensils,
    description: 'Please specify in notes'
  }
];

export default function RoomSpecialRequests({
  room,
  onRequestChange,
  specialRequests,
  additionalNotes,
  onNotesChange
}: RoomSpecialRequestsProps) {
  const handleRequestToggle = (requestId: string) => {
    const updatedRequests = specialRequests.includes(requestId)
      ? specialRequests.filter(id => id !== requestId)
      : [...specialRequests, requestId];
    onRequestChange(updatedRequests);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Special Requests</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Common Requests</h4>
            <div className="grid gap-4">
              {COMMON_REQUESTS.map(({ id, label, icon: Icon, description }) => (
                <div key={id} className="flex items-start space-x-2">
                  <Checkbox
                    id={id}
                    checked={specialRequests.includes(id)}
                    onCheckedChange={() => handleRequestToggle(id)}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <div className="flex items-center gap-2">
                      <Label
                        htmlFor={id}
                        className="font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {label}
                      </Label>
                      <Icon className="h-4 w-4 text-muted-foreground" />
                    </div>
                    {description && (
                      <p className="text-sm text-muted-foreground">
                        {description}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              placeholder="Please specify any dietary restrictions, accessibility needs, or other special requests..."
              value={additionalNotes}
              onChange={(e) => onNotesChange(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          {room.specialRequestNotes && (
            <div className="flex items-start gap-2 pt-4 border-t">
              <MessageSquare className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div className="flex-1">
                <p className="text-sm font-medium">Important Information</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {room.specialRequestNotes}
                </p>
              </div>
            </div>
          )}

          <div className="text-sm text-muted-foreground space-y-2 pt-4 border-t">
            <p>• All special requests are subject to availability</p>
            <p>• We will do our best to accommodate your requests but cannot guarantee them</p>
            <p>• Some requests may incur additional charges</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 