import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, MapPin, AlertTriangle, CheckCircle2 } from "lucide-react";
import { testLocationQuery, validateLocationResponse } from "@/utils/location-test-utility";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import LocationMarker from "@/components/LocationMarker";

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
}

interface TestResult {
  success: boolean;
  message: string;
  location?: LocationData;
  response?: string[];
  error?: string;
  duration?: number;
}

/**
 * A debug tool for testing location detection in queries
 */
export default function LocationDebugTool() {
  const [query, setQuery] = useState<string>("Find hotels in Miami Beach");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [result, setResult] = useState<TestResult | null>(null);
  const [testHistory, setTestHistory] = useState<Array<{ query: string; result: TestResult }>>([]);

  const runTest = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    setResult(null);

    try {
      const startTime = performance.now();
      const testResult = await testLocationQuery(query);
      const endTime = performance.now();
      
      const newResult: TestResult = {
        success: testResult.success,
        message: testResult.success 
          ? "Successfully detected location" 
          : "Failed to detect location",
        location: testResult.location,
        response: testResult.textResponse || [],
        error: testResult.error,
        duration: Math.round(endTime - startTime)
      };
      
      setResult(newResult);
      setTestHistory(prev => [{ query, result: newResult }, ...prev.slice(0, 4)]);
    } catch (error) {
      setResult({
        success: false,
        message: "Test failed due to error",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      runTest();
    }
  };

  // Helper to validate URLs for dynamic linking
  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Create Google Maps link for the detected location
  const getGoogleMapsLink = (location?: LocationData): string => {
    if (!location) return '#';
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lng}`;
    return validateUrl(googleMapsUrl) ? googleMapsUrl : '#';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Location Detection Test</CardTitle>
          <CardDescription>
            Enter a location query to test if the AI can extract the location data correctly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Enter a location query, e.g., 'Find hotels in New York'"
              className="flex-1"
            />
            <Button 
              onClick={runTest} 
              disabled={isLoading || !query.trim()}
            >
              {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Test
            </Button>
          </div>
        </CardContent>
      </Card>

      {result && (
        <Card className={result.success ? "border-green-500/50" : "border-red-500/50"}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                {result.success ? (
                  <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                ) : (
                  <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
                )}
                {result.message}
              </CardTitle>
              {result.duration && (
                <Badge variant="outline">{result.duration}ms</Badge>
              )}
            </div>
            <CardDescription>
              Query: <span className="font-medium">{query}</span>
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {result.location && (
              <div className="rounded-md bg-muted p-4">
                <h3 className="text-sm font-medium mb-2 flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  Detected Location
                </h3>
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="space-y-2 flex-1">
                    <div className="grid grid-cols-2 gap-1 text-sm">
                      <span className="text-muted-foreground">Name:</span>
                      <span className="font-medium">{result.location.name}</span>
                      
                      <span className="text-muted-foreground">Latitude:</span>
                      <span className="font-mono text-xs">{result.location.lat}</span>
                      
                      <span className="text-muted-foreground">Longitude:</span>
                      <span className="font-mono text-xs">{result.location.lng}</span>
                      
                      {result.location.placeType && (
                        <>
                          <span className="text-muted-foreground">Type:</span>
                          <span>{result.location.placeType}</span>
                        </>
                      )}
                    </div>
                    
                    <div className="pt-2">
                      <a 
                        href={getGoogleMapsLink(result.location)} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:underline"
                      >
                        View on Google Maps
                      </a>
                    </div>
                  </div>
                  
                  <div className="min-w-[200px] h-[150px] bg-gray-100 rounded-md overflow-hidden relative">
                    <LocationMarker 
                      location={result.location}
                      className="w-full h-full"
                    />
                  </div>
                </div>
              </div>
            )}
            
            {result.error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <h3 className="text-sm font-medium text-red-800 mb-1">Error Details</h3>
                <p className="text-sm text-red-700">{result.error}</p>
              </div>
            )}
            
            {result.response && result.response.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-2">Response Text</h3>
                <Textarea 
                  readOnly 
                  value={result.response.join('\n')} 
                  className="min-h-[100px] font-mono text-xs"
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}
      
      {testHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testHistory.map((item, index) => (
                <div key={index} className="text-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 font-medium truncate pr-2">{item.query}</div>
                    {item.result.success ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Success
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                        Failed
                      </Badge>
                    )}
                  </div>
                  {item.result.location && (
                    <div className="text-xs text-muted-foreground flex items-center mt-1">
                      <MapPin className="h-3 w-3 mr-1" />
                      {item.result.location.name} ({item.result.location.lat.toFixed(4)}, {item.result.location.lng.toFixed(4)})
                    </div>
                  )}
                  {index < testHistory.length - 1 && <Separator className="my-2" />}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}