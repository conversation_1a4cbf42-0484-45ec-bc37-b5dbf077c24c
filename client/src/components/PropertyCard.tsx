import { Card, CardContent, CardFooter } from "@/components/ui/card.js";
import { Button } from "@/components/ui/button.js";
import { useLocation } from "wouter";
import { Property, PropertyWithRates, PropertyImage } from "@/types/schema.js";
import { Rate } from "@/types/rate.js";
import { cn, calculateNights } from "@/lib/utils.js";
import { getFullSizeImageUrl, getImageSource } from "@/utils/imageUtils.js";
import { getAmenityIcon } from "@/utils/amenityUtils.js";
import { Tag } from "lucide-react";
import { Badge } from "@/components/ui/badge.js";
import ImageGalleryModal from './ImageGalleryModal.jsx';
import { useState, useEffect } from 'react';
import { RateDisplay } from "./RateDisplay.js";
import { PropertyCardContainer, ImageSection, DiscountBadge } from "@/components/ui/styled/property-card";
import { useIsMobile } from "@/hooks/use-mobile";

export interface PropertyCardProps {
  property: any; // Make this more flexible to accept different property types
  onClick?: () => void;
  highlighted?: boolean;
  rates?: Rate[];
  isLoading?: boolean;
  error?: string;
  propertyDetails?: {
    images?: PropertyImage[];
    isLoading?: boolean;
    error?: boolean;
  };
  checkIn?: Date;
  checkOut?: Date;
  guests?: string;
  rooms?: string;
  onCompareToggle?: () => void;
  isComparing?: boolean;
}

export default function PropertyCard({ 
  property, 
  onClick, 
  highlighted, 
  rates,
  isLoading,
  error,
  propertyDetails,
  checkIn,
  checkOut,
  guests,
  rooms,
  onCompareToggle,
  isComparing
}: PropertyCardProps) {
  const [_, setLocation] = useLocation();
  const [galleryOpen, setGalleryOpen] = useState(false);
  const [propertyImages, setPropertyImages] = useState<string[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (galleryOpen) {
      setIsLoadingImages(true);
      fetch(`/api/properties/${property.id}`)
        .then(res => res.json())
        .then(data => {
          const images = data.images?.map((img: string | PropertyImage) => 
            getFullSizeImageUrl(typeof img === 'string' ? img : img.url)
          ) || [];
          setPropertyImages(images);
        })
        .catch(error => {
          // Silently handle error
          setPropertyImages([]);
        })
        .finally(() => {
          setIsLoadingImages(false);
        });
    }
  }, [galleryOpen, property.id]);

  const handleClick = () => {
    console.log('PropertyCard handleClick called', {
      propertyId: property.id,
      hasOnClick: !!onClick,
      checkIn,
      checkOut,
      guests,
      rooms
    });

    const searchParams = new URLSearchParams();

    if (checkIn) searchParams.set('checkIn', checkIn.toISOString().split('T')[0]);
    if (checkOut) searchParams.set('checkOut', checkOut.toISOString().split('T')[0]);
    searchParams.set('guests', guests || '2');
    searchParams.set('rooms', rooms || '1');

    if (onClick) {
      console.log('Calling provided onClick handler');
      onClick();
    } else {
      const targetUrl = `/property/${property.id}?${searchParams.toString()}`;
      console.log('Navigating to:', targetUrl);
      setLocation(targetUrl);
    }
  };

  const amenities = property.amenities || [];
  const nights = calculateNights(checkIn, checkOut);
  const discount = rates?.[0]?.discountPercent ?? 0;

  return (
    <PropertyCardContainer className={cn("w-full", isMobile && "flex flex-col")}>
      <ImageSection className={cn(
        "relative w-full",
        isMobile ? "h-48" : "h-64",
        "overflow-hidden rounded-t-lg"
      )}>
        {discount > 0 && (
          <DiscountBadge className="absolute top-2 left-2 z-10 bg-primary text-primary-foreground px-2 py-1 rounded-md text-sm font-medium shadow-sm">
            <Tag className="h-3 w-3 mr-1" />
            Save {discount}%
          </DiscountBadge>
        )}
        <div 
          onClick={() => setGalleryOpen(true)} 
          className="h-full cursor-pointer"
        >
          <img
            src={getFullSizeImageUrl(getImageSource(property.images?.[0]))}
            alt={property.name}
            className="w-full h-full object-cover transition-transform hover:scale-105"
            loading="lazy"
          />
        </div>
      </ImageSection>

      <Card 
        className={cn(
          "flex-1 overflow-hidden transition-all duration-200 hover:shadow-lg cursor-pointer",
          highlighted && "ring-2 ring-primary",
          isMobile && "rounded-t-none border-t-0"
        )}
        onClick={handleClick}
      >
        <CardContent className={cn("p-4", isMobile && "p-3")}>
          <div className="flex justify-between items-start gap-4">
            <div className="min-w-0 flex-1">
              <h3 className={cn(
                "font-semibold leading-tight",
                isMobile ? "text-base" : "text-lg"
              )}>
                {property.name}
              </h3>
              <p className="text-sm text-muted-foreground mt-1 truncate">
                {property.city}{property.state ? `, ${property.state}` : ''}
              </p>
              {property.rating && (
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-sm font-medium">
                    {Number(property.rating).toFixed(1)}★
                  </span>
                  {property.reviewCount && (
                    <span className="text-xs text-muted-foreground">
                      ({property.reviewCount.toLocaleString()})
                    </span>
                  )}
                </div>
              )}
            </div>
            <div className="text-right flex flex-col items-end justify-start shrink-0">
              <RateDisplay 
                rates={rates}
                isLoading={isLoading}
                error={error}
                nights={nights}
                className={cn(
                  "flex flex-col items-end",
                  isMobile ? "min-w-[120px]" : "min-w-[150px]"
                )}
              />
            </div>
          </div>

          {!isMobile && (
            <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
              {property.description}
            </p>
          )}

          {amenities.length > 0 && (
            <div className={cn(
              "flex flex-wrap gap-1.5",
              isMobile ? "mt-2" : "mt-3"
            )}>
              {amenities.slice(0, isMobile ? 3 : 5).map((amenity: string) => {
                const Icon = getAmenityIcon(amenity);
                return (
                  <Badge 
                    key={amenity} 
                    variant="secondary" 
                    className="flex items-center gap-1 py-0.5 text-xs"
                  >
                    {Icon && <Icon className="h-3 w-3" />}
                    {amenity}
                  </Badge>
                );
              })}
              {amenities.length > (isMobile ? 3 : 5) && (
                <Badge variant="outline" className="py-0.5 text-xs">
                  +{amenities.length - (isMobile ? 3 : 5)} more
                </Badge>
              )}
            </div>
          )}

          {rates?.[0] && !isMobile && (
            <div className="mt-3 text-xs text-muted-foreground flex flex-wrap gap-2">
              {rates[0].refundable && (
                <div className="flex items-center gap-1.5">
                  <span className="text-green-600">✓</span>
                  <span>Free cancellation</span>
                </div>
              )}
              {rates[0].description?.toLowerCase().includes('pay later') && (
                <div className="flex items-center gap-1.5">
                  <span className="text-green-600">✓</span>
                  <span>Reserve now, pay later</span>
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className={cn(
          "px-4 pb-4",
          isMobile && "p-3 pt-0"
        )}>
          <Button
            className="w-full text-sm"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              handleClick();
            }}
          >
            View Details
          </Button>
        </CardFooter>
      </Card>

      <ImageGalleryModal
        images={propertyImages.length > 0 ? propertyImages : [
          getFullSizeImageUrl(getImageSource(property.images?.[0]))
        ]}
        open={galleryOpen}
        onClose={() => setGalleryOpen(false)}
        isLoading={isLoadingImages}
      />
    </PropertyCardContainer>
  );
}