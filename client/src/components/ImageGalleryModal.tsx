import React, { useState } from 'react';
import { Dialog, IconButton } from '@mui/material';
import { Close, ArrowBack, ArrowForward } from '@mui/icons-material';
import styled from '@emotion/styled';
import { Loader2 } from 'lucide-react';

interface ImageGalleryModalProps {
  images: string[];
  open: boolean;
  onClose: () => void;
  initialImageIndex?: number;
  isLoading?: boolean;
}

const ModalContainer = styled.div`
  background: rgba(0, 0, 0, 0.9);
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ImageContainer = styled.div`
  position: relative;
  max-width: 90%;
  max-height: 90vh;
  img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
  }
`;

const NavigationButton = styled(IconButton)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const CloseButton = styled(IconButton)`
  position: absolute;
  top: 16px;
  right: 16px;
  color: white;
  z-index: 1;
`;

const ImageCounter = styled.div`
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 16px;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: white;
`;

const ImageGalleryModal: React.FC<ImageGalleryModalProps> = ({
  images,
  open,
  onClose,
  initialImageIndex = 0,
  isLoading = false,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(initialImageIndex);

  const handlePrevious = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={false}
      fullScreen
    >
      <ModalContainer>
        <CloseButton onClick={onClose}>
          <Close />
        </CloseButton>
        
        {isLoading ? (
          <LoadingContainer>
            <Loader2 className="h-8 w-8 animate-spin" />
            <span>Loading images...</span>
          </LoadingContainer>
        ) : (
          <>
            <NavigationButton
              sx={{ left: 16 }}
              onClick={handlePrevious}
            >
              <ArrowBack />
            </NavigationButton>

            <ImageContainer>
              <img
                src={images[currentImageIndex]}
                alt={`Property image ${currentImageIndex + 1}`}
              />
            </ImageContainer>

            <NavigationButton
              sx={{ right: 16 }}
              onClick={handleNext}
            >
              <ArrowForward />
            </NavigationButton>

            <ImageCounter>
              {currentImageIndex + 1} / {images.length}
            </ImageCounter>
          </>
        )}
      </ModalContainer>
    </Dialog>
  );
};

export default ImageGalleryModal; 