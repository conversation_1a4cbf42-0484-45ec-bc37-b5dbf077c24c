import { useQuery } from "@tanstack/react-query";
import { FeaturedProperty } from '@/types/featured';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";
import { AlertCircle, Star, Tag, RefreshCcw } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const fetchFeaturedProperties = async (): Promise<FeaturedProperty[]> => {
  const response = await fetch('https://api.travsrv.com/api/content?type=findfeaturedhoteldeals&maxresultcount=4');
  if (!response.ok) {
    throw new Error('Failed to fetch featured properties');
  }
  return response.json();
};

export default function FeaturedProperties() {
  const [_, setLocation] = useLocation();
  
  const { 
    data: featuredProperties = [], 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['featuredProperties'],
    queryFn: fetchFeaturedProperties,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep data in cache for 10 minutes
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Featured Deals</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="h-48 bg-gray-200 rounded-t-lg" />
              <CardContent className="p-4">
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-2" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
                <div className="mt-4 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Featured Deals</h2>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load featured properties. Please try again.</span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => refetch()}
              className="ml-4"
            >
              <RefreshCcw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!featuredProperties.length) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Featured Deals</h2>
          <Button variant="outline" onClick={() => setLocation('/search')}>
            View All Deals
          </Button>
        </div>
        <Card className="p-6 text-center">
          <p className="text-muted-foreground">No featured properties available at the moment.</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => refetch()}
          >
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Featured Deals</h2>
        <Button variant="outline" onClick={() => setLocation('/search')}>
          View All Deals
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {featuredProperties.map((property) => (
          <Card 
            key={property.PropertyId}
            className="overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer group"
            onClick={() => setLocation(`/property/${property.PropertyId}`)}
          >
            <div className="relative">
              <img
                src={`https:${property.PropertyImageUrlHighRes}`}
                alt={property.PropertyName}
                className="w-full h-48 object-cover transition-transform duration-200 group-hover:scale-105"
              />
              {property.MaxDiscountPercent > 0 && (
                <Badge 
                  className="absolute top-2 left-2 bg-red-500 text-white flex items-center gap-1"
                >
                  <Tag className="h-3 w-3" />
                  Save {property.MaxDiscountPercent}%
                </Badge>
              )}
            </div>
            
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-1 line-clamp-2">
                {property.PropertyName}
              </h3>
              
              <p className="text-sm text-muted-foreground mb-2">
                {property.PropertyAddress}
              </p>
              
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="ml-1 text-sm font-medium">
                    {property.TripAdvisorRating.toFixed(1)}
                  </span>
                </div>
                <span className="text-sm text-muted-foreground">
                  ({property.TripAdvisorReviewCount.toLocaleString()} reviews)
                </span>
              </div>
              
              <div className="flex justify-between items-end">
                <div>
                  <div className="text-sm text-muted-foreground">From</div>
                  <div className="text-lg font-semibold">
                    ${property.ReferencePrice.toFixed(2)}
                  </div>
                </div>
                <Badge variant="secondary">
                  {property.PropertyRating}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
} 