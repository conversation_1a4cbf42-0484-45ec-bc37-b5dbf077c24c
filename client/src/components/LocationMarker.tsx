import React, { useEffect, useRef } from "react";
import { MapPin } from "lucide-react";
import { getGoogleMaps } from "@/lib/googleMaps";

interface LocationMarkerProps {
  location: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
  onSearchNearby?: () => void;
  className?: string;
}

export default function LocationMarker({ 
  location,
  onSearchNearby,
  className = "",
}: LocationMarkerProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<google.maps.Marker | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    const initMap = async () => {
      if (!mapRef.current) return;
      
      try {
        const google = await getGoogleMaps();
        
        if (!isMounted || !mapRef.current) return;
        
        // Create map instance
        const mapOptions: google.maps.MapOptions = {
          center: { lat: location.lat, lng: location.lng },
          zoom: 14,
          mapTypeId: google.maps.MapTypeId.ROADMAP,
          disableDefaultUI: true,
          zoomControl: false,
          scrollwheel: false,
          gestureHandling: "none",
          styles: [
            {
              featureType: "poi",
              elementType: "labels",
              stylers: [{ visibility: "off" }]
            }
          ]
        };
        
        const map = new google.maps.Map(mapRef.current, mapOptions);
        mapInstanceRef.current = map;
        
        // Add marker
        const marker = new google.maps.Marker({
          position: { lat: location.lat, lng: location.lng },
          map,
          title: location.name,
          animation: google.maps.Animation.DROP
        });
        markerRef.current = marker;
      } catch (error) {
        console.error("Failed to initialize map:", error);
      }
    };
    
    initMap();
    
    return () => {
      isMounted = false;
      // Clean up marker and map if needed
      if (markerRef.current) {
        markerRef.current.setMap(null);
      }
    };
  }, [location]);

  // This ensures we update the marker position if location changes
  useEffect(() => {
    if (markerRef.current && location) {
      markerRef.current.setPosition({ lat: location.lat, lng: location.lng });
      
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setCenter({ lat: location.lat, lng: location.lng });
      }
    }
  }, [location.lat, location.lng]);

  // Fallback if Google Maps fails to load
  const renderFallback = () => (
    <div className="w-full h-full flex items-center justify-center bg-gray-100">
      <div className="flex flex-col items-center text-gray-500">
        <MapPin className="h-8 w-8 mb-2" />
        <div className="text-sm">
          {location.name}
        </div>
        <div className="text-xs mt-1">
          {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
        </div>
      </div>
    </div>
  );

  return (
    <div className={`relative ${className}`}>
      <div 
        ref={mapRef} 
        className="w-full h-full"
        data-testid="location-map"
      />
      
      {/* Overlay that displays if map doesn't load properly */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 transition-opacity" 
        id="map-fallback">
        {renderFallback()}
      </div>
      
      {/* JavaScript to detect map loading failures and show fallback */}
      <script dangerouslySetInnerHTML={{
        __html: `
          setTimeout(() => {
            const mapElement = document.getElementById('${mapRef.current?.id}');
            const fallback = document.getElementById('map-fallback');
            if (mapElement && mapElement.childElementCount === 0 && fallback) {
              fallback.style.opacity = '1';
            }
          }, 2000);
        `
      }} />
    </div>
  );
}