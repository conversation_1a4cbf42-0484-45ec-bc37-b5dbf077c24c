import { ExtendedRoom } from '@/types/schema.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import {
  Wifi,
  Tv,
  Coffee,
  Bath,
  Wind,
  Phone,
  Safe,
  Refrigerator,
  Utensils,
  Key,
} from 'lucide-react';

interface RoomAmenityListProps {
  room: ExtendedRoom;
}

const AMENITY_ICONS: Record<string, any> = {
  'WiFi': Wifi,
  'TV': Tv,
  'Coffee Maker': Coffee,
  'Bathtub': Bath,
  'Air Conditioning': Wind,
  'Telephone': Phone,
  'Safe': Safe,
  'Mini Fridge': Refrigerator,
  'Kitchen': Utensils,
  'Room Service': Key,
};

// Group amenities by category
const AMENITY_CATEGORIES = {
  'Room Features': [
    'WiFi', 'TV', 'Air Conditioning', 'Heating', 'Safe', 'Work Desk',
    'Telephone', 'Iron', 'Blackout Curtains'
  ],
  'Bathroom': [
    'Bathtub', 'Shower', 'Hair Dryer', 'Toiletries', 'Bathrobes', 'Slippers'
  ],
  'Food & Drink': [
    'Coffee Maker', 'Mini Fridge', 'Kitchen', 'Room Service', 'Mini Bar',
    'Electric Kettle'
  ],
  'Other': [] as string[]
};

export default function RoomAmenityList({ room }: RoomAmenityListProps) {
  if (!room.amenities?.length) return null;

  // Categorize amenities
  const categorizedAmenities = room.amenities.reduce((acc, amenity) => {
    let placed = false;
    for (const [category, items] of Object.entries(AMENITY_CATEGORIES)) {
      if (items.includes(amenity)) {
        if (!acc[category]) acc[category] = [];
        acc[category].push(amenity);
        placed = true;
        break;
      }
    }
    if (!placed) {
      if (!acc['Other']) acc['Other'] = [];
      acc['Other'].push(amenity);
    }
    return acc;
  }, {} as Record<string, string[]>);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Room Amenities</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(categorizedAmenities).map(([category, amenities]) => (
            <div key={category}>
              <h4 className="font-medium mb-3">{category}</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {amenities.map((amenity) => {
                  const IconComponent = AMENITY_ICONS[amenity];
                  return (
                    <div key={amenity} className="flex items-center gap-2">
                      {IconComponent ? (
                        <IconComponent className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <div className="w-4" />
                      )}
                      <span className="text-sm">{amenity}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {room.includedServices && room.includedServices.length > 0 && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-3">Included Services</h4>
            <div className="flex flex-wrap gap-2">
              {room.includedServices.map((service) => (
                <Badge key={service} variant="secondary">
                  {service}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {room.additionalAmenities && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-3">Additional Amenities</h4>
            <p className="text-sm text-muted-foreground">
              {room.additionalAmenities}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 