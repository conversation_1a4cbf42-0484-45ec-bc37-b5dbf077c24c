import { ExtendedRoom } from '@/types/schema.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { formatCurrency } from '@/lib/utils.jsx';

interface RoomTaxesAndFeesProps {
  room: ExtendedRoom;
}

export default function RoomTaxesAndFees({ room }: RoomTaxesAndFeesProps) {
  const hasTaxesOrFees = (room.taxes?.length || 0) + (room.fees?.length || 0) > 0;

  if (!hasTaxesOrFees) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Taxes & Fees</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {room.taxes && room.taxes.length > 0 && (
            <div>
              <h4 className="font-medium mb-3">Taxes</h4>
              <div className="space-y-2">
                {room.taxes.map((tax) => (
                  <div key={tax.type} className="flex justify-between items-center">
                    <div>
                      <span className="text-sm">{tax.type}</span>
                      {tax.included && (
                        <Badge variant="outline" className="ml-2">
                          Included
                        </Badge>
                      )}
                    </div>
                    <span>{formatCurrency(tax.amount, tax.currency)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {room.fees && room.fees.length > 0 && (
            <div>
              <h4 className="font-medium mb-3">Fees</h4>
              <div className="space-y-2">
                {room.fees.map((fee) => (
                  <div key={fee.type} className="flex justify-between items-center">
                    <div>
                      <span className="text-sm">{fee.type}</span>
                      {fee.included && (
                        <Badge variant="outline" className="ml-2">
                          Included
                        </Badge>
                      )}
                    </div>
                    <span>{formatCurrency(fee.amount, fee.currency)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="pt-4 border-t">
            <div className="flex justify-between items-center font-medium">
              <span>Base Rate</span>
              <span>{formatCurrency(room.rate, room.currency)}</span>
            </div>
            <div className="flex justify-between items-center font-bold mt-2">
              <span>Total with Taxes & Fees</span>
              <span>{formatCurrency(room.totalAmount, room.currency)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 