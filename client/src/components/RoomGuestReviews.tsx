import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { Star, ThumbsUp, MessageSquare, Calendar } from 'lucide-react';
import { Progress } from './ui/progress.jsx';

interface RoomGuestReviewsProps {
  room: ExtendedRoom;
}

interface Review {
  id: string;
  rating: number;
  comment: string;
  guestName: string;
  date: string;
  stayDuration?: string;
  tripType?: string;
  helpful?: number;
  response?: {
    text: string;
    date: string;
    responderName: string;
  };
}

export default function RoomGuestReviews({ room }: RoomGuestReviewsProps) {
  if (!room.reviews?.length) return null;

  const averageRating = room.reviews.reduce((acc, review) => acc + review.rating, 0) / room.reviews.length;
  
  const ratingDistribution = room.reviews.reduce((acc, review) => {
    acc[Math.floor(review.rating)] = (acc[Math.floor(review.rating)] || 0) + 1;
    return acc;
  }, {} as Record<number, number>);

  const totalReviews = room.reviews.length;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Guest Reviews</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 fill-primary text-primary" />
                <span className="text-2xl font-bold">
                  {averageRating.toFixed(1)}
                </span>
                <Badge variant="secondary">
                  {totalReviews} reviews
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Based on recent room-specific reviews
              </p>
            </div>
          </div>

          <div className="space-y-2">
            {[5, 4, 3, 2, 1].map((rating) => (
              <div key={rating} className="flex items-center gap-2">
                <span className="text-sm w-6">{rating}</span>
                <Progress 
                  value={(ratingDistribution[rating] || 0) / totalReviews * 100} 
                  className="h-2"
                />
                <span className="text-sm text-muted-foreground w-12">
                  {Math.round((ratingDistribution[rating] || 0) / totalReviews * 100)}%
                </span>
              </div>
            ))}
          </div>

          <div className="space-y-6 pt-6 border-t">
            {room.reviews.map((review: Review) => (
              <div key={review.id} className="space-y-4">
                <div>
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{review.guestName}</span>
                        <Badge variant="secondary">
                          {review.tripType}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(review.date).toLocaleDateString()}</span>
                        {review.stayDuration && (
                          <span>• {review.stayDuration}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-primary text-primary" />
                      <span className="font-medium">{review.rating}</span>
                    </div>
                  </div>
                  <p className="mt-2 text-sm">{review.comment}</p>
                  {review.helpful && (
                    <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                      <ThumbsUp className="h-4 w-4" />
                      <span>{review.helpful} found this helpful</span>
                    </div>
                  )}
                </div>

                {review.response && (
                  <div className="pl-4 border-l-2">
                    <div className="flex items-center gap-2 text-sm">
                      <MessageSquare className="h-4 w-4" />
                      <span className="font-medium">Response from {review.response.responderName}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {new Date(review.response.date).toLocaleDateString()}
                    </div>
                    <p className="mt-2 text-sm">{review.response.text}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {room.reviewHighlights && (
            <div className="pt-6 border-t">
              <h4 className="font-medium mb-3">Review Highlights</h4>
              <div className="flex flex-wrap gap-2">
                {room.reviewHighlights.map((highlight, index) => (
                  <Badge key={index} variant="secondary">
                    {highlight}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 