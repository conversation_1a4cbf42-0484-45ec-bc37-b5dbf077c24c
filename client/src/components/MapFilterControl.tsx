import React, { useState } from 'react';
import { Card } from "./ui/card.jsx";
import { Label } from "./ui/label.jsx";
import { Slider } from "./ui/slider.jsx";
import { Button } from "./ui/button.jsx";
import { X, Filter, ChevronLeft, ChevronRight } from "lucide-react";
import { PropertyType } from "../types/schema.js";
import { cn } from "../lib/utils.js";

interface MapFilterControlProps {
  priceRange: [number, number];
  onPriceRangeChange: (range: [number, number]) => void;
  selectedTypes: PropertyType[];
  onTypeToggle: (type: PropertyType) => void;
  onReset: () => void;
  className?: string;
}

const PROPERTY_TYPES: { type: PropertyType; label: string; icon: string }[] = [
  { type: 'hotel', label: 'Hotel', icon: '🏨' },
  { type: 'resort', label: 'Resort', icon: '🌴' },
  { type: 'apartment', label: 'Apartment', icon: '🏢' },
  { type: 'villa', label: 'Villa', icon: '🏡' },
  { type: 'guesthouse', label: 'Guesthouse', icon: '🏠' }
];

const MapFilterControl: React.FC<MapFilterControlProps> = ({
  priceRange,
  onPriceRangeChange,
  selectedTypes,
  onTypeToggle,
  onReset
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div>
      <Button
        variant="secondary"
        size="sm"
        className="bg-white shadow-lg mb-2 hover:bg-gray-100"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <Filter className="h-4 w-4 mr-2" />
        Filters
        {isExpanded ? (
          <ChevronLeft className="h-4 w-4 ml-2" />
        ) : (
          <ChevronRight className="h-4 w-4 ml-2" />
        )}
      </Button>

      <Card className={cn(
        "p-4 w-72 bg-white shadow-lg transition-all duration-200 ease-in-out",
        isExpanded ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-2 pointer-events-none"
      )}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium">Filters</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onReset}
            className="h-8 px-2"
          >
            <X className="h-4 w-4 mr-1" />
            Reset
          </Button>
        </div>

        <div className="space-y-6">
          <div>
            <Label>Price Range</Label>
            <div className="mt-2">
              <Slider
                value={priceRange}
                min={0}
                max={1000}
                step={50}
                onValueChange={onPriceRangeChange}
              />
              <div className="flex justify-between mt-1 text-sm text-muted-foreground">
                <span>${priceRange[0]}</span>
                <span>${priceRange[1]}</span>
              </div>
            </div>
          </div>

          <div>
            <Label>Property Type</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {PROPERTY_TYPES.map(({ type, label, icon }) => (
                <Button
                  key={type}
                  variant={selectedTypes.includes(type) ? "default" : "outline"}
                  size="sm"
                  className="justify-start"
                  onClick={() => onTypeToggle(type)}
                >
                  <span className="mr-2">{icon}</span>
                  {label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default MapFilterControl; 