import { useCallback, useEffect, useState } from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react'
import { Button } from './ui/button.jsx'
import { cn } from '@/lib/utils.js'
import { PropertyImage } from '@/types/schema.js'
import { getFullSizeImageUrl, sortByDisplayOrder } from '@/utils/imageUtils.js'

interface ImageCarouselProps {
  images?: PropertyImage[];
  isLoading?: boolean;
  error?: boolean;
  fallbackImage?: string;
  className?: string;
}

export default function ImageCarousel({ 
  images = [], 
  isLoading,
  error,
  fallbackImage = '/placeholder-property.jpg',
  className 
}: ImageCarouselProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({ 
    loop: true,
    dragFree: false,
    align: 'center',
    containScroll: 'keepSnaps'
  })
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false)
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set())

  const handleImageLoad = (imagePath: string) => {
    setLoadedImages(prev => new Set(prev).add(imagePath))
  }

  const scrollPrev = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation();
    emblaApi && emblaApi.scrollPrev();
  }, [emblaApi])

  const scrollNext = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation();
    emblaApi && emblaApi.scrollNext();
  }, [emblaApi])

  const handleImageClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX - rect.left;
    if (x < rect.width / 3) {
      scrollPrev();
    } else if (x > (rect.width * 2) / 3) {
      scrollNext();
    }
  }, [scrollPrev, scrollNext])

  const onSelect = useCallback(() => {
    if (!emblaApi) return
    setPrevBtnEnabled(emblaApi.canScrollPrev())
    setNextBtnEnabled(emblaApi.canScrollNext())
    setSelectedIndex(emblaApi.selectedScrollSnap())
  }, [emblaApi])

  useEffect(() => {
    if (!emblaApi) return
    onSelect()
    emblaApi.on('select', onSelect)
    emblaApi.on('reInit', onSelect)
  }, [emblaApi, onSelect])

  // Sort images by displayOrder
  const sortedImages = sortByDisplayOrder(images);

  if (isLoading) {
    return (
      <div className={cn("relative w-full h-48 bg-muted flex items-center justify-center", className)}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (error || (!isLoading && (!images || images.length === 0))) {
    return (
      <div className={cn("relative w-full h-48", className)}>
        <img
          src={fallbackImage}
          alt="Property"
          className="w-full h-full object-cover"
        />
      </div>
    )
  }

  return (
    <div 
      className={cn("relative w-full h-48 group touch-pan-y select-none", className)}
      onClick={handleImageClick}
    >
      <div className="overflow-hidden h-full" ref={emblaRef}>
        <div className="flex h-full">
          {sortedImages.map((image, index) => (
            <div
              key={`${image.url}-${index}`}
              className="relative w-full h-full flex-shrink-0"
            >
              {!loadedImages.has(image.url) && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              )}
              <img
                src={getFullSizeImageUrl(image.url)}
                alt={image.caption || `Property image ${index + 1}`}
                className={cn(
                  "w-full h-full object-cover transition-opacity duration-300",
                  loadedImages.has(image.url) ? "opacity-100" : "opacity-0"
                )}
                onLoad={() => handleImageLoad(image.url)}
                onError={() => {
                  console.error('Image failed to load:', image.url);
                  // Still mark as loaded to remove loading spinner
                  handleImageLoad(image.url);
                }}
              />
              {image.caption && loadedImages.has(image.url) && (
                <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-2 text-sm">
                  {image.caption}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {sortedImages.length > 1 && (
        <>
          <div className="absolute inset-0 flex items-center justify-between p-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 rounded-full bg-black/50 hover:bg-black/70"
              onClick={(e) => {
                e.stopPropagation();
                scrollPrev();
              }}
              disabled={!prevBtnEnabled}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 rounded-full bg-black/50 hover:bg-black/70"
              onClick={(e) => {
                e.stopPropagation();
                scrollNext();
              }}
              disabled={!nextBtnEnabled}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1 z-10">
            {sortedImages.map((_, index) => (
              <button
                key={index}
                className={cn(
                  "w-1.5 h-1.5 rounded-full transition-all",
                  index === selectedIndex
                    ? "bg-white w-3"
                    : "bg-white/50 hover:bg-white/75"
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  emblaApi?.scrollTo(index);
                }}
              />
            ))}
          </div>
        </>
      )}
    </div>
  )
} 