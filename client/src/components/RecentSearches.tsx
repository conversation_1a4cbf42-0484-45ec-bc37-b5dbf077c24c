import { SearchHistoryEntry } from "@/hooks/use-search-history";
import { Button } from "@/components/ui/button";
import { Clock, MapPin, X } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface RecentSearchesProps {
  searches: SearchHistoryEntry[];
  onSelect: (search: SearchHistoryEntry) => void;
  onClear?: () => void;
  variant?: "compact" | "full";
}

export default function RecentSearches({ 
  searches, 
  onSelect, 
  onClear,
  variant = "full" 
}: RecentSearchesProps) {
  if (searches.length === 0) return null;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-muted-foreground">Recent Searches</h3>
        {onClear && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClear}
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
            <span className="ml-2">Clear</span>
          </Button>
        )}
      </div>
      <div className="space-y-2">
        {searches.map((search, index) => (
          <Button
            key={`${search.locationName}-${index}`}
            variant="outline"
            className="w-full justify-start h-auto py-3 px-4"
            onClick={() => onSelect(search)}
          >
            <div className="flex items-start gap-3">
              <MapPin className="h-4 w-4 mt-1 shrink-0" />
              <div className="flex-1 text-left">
                <div className="font-medium">{search.locationName}</div>
                {variant === "full" && (
                  <div className="text-sm text-muted-foreground mt-1">
                    {new Date(search.checkIn).toLocaleDateString()} - {new Date(search.checkOut).toLocaleDateString()} 
                    • {search.guests} guests, {search.rooms} {search.rooms === 1 ? 'room' : 'rooms'}
                  </div>
                )}
                <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatDistanceToNow(search.timestamp, { addSuffix: true })}
                </div>
              </div>
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
}
