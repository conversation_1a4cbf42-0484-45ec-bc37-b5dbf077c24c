import { ExtendedRoom } from '@/types/schema.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { formatCurrency } from '@/lib/utils.jsx';
import { Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';

interface RoomRateDetailsProps {
  room: ExtendedRoom;
  nights: number;
}

export default function RoomRateDetails({ room, nights }: RoomRateDetailsProps) {
  const baseTotal = room.rate * nights;
  const hasPromotions = room.promotions && room.promotions.length > 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Rate Details</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">{room.ratePlanDescription}</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  {room.refundable ? 'Refundable Rate' : 'Non-Refundable Rate'}
                </p>
              </div>
              {room.restrictedRate && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-5 w-5 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>This is a restricted rate with special conditions</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex justify-between">
                <span>Base Rate per Night</span>
                <span>{formatCurrency(room.rate, room.currency)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Total for {nights} Nights</span>
                <span>{formatCurrency(baseTotal, room.currency)}</span>
              </div>
            </div>
          </div>

          {hasPromotions && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Applied Promotions</h4>
              <div className="space-y-3">
                {room.promotions.map(promo => (
                  <div key={promo.code}>
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">{promo.description}</p>
                        <p className="text-xs text-muted-foreground">
                          Valid until {new Date(promo.endDate).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant="secondary">
                        {promo.discountType === 'Percentage' 
                          ? `${promo.discountValue}% off`
                          : formatCurrency(promo.discountValue, room.currency)}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="pt-4 border-t space-y-2">
            {room.taxes?.map(tax => (
              <div key={tax.type} className="flex justify-between text-sm">
                <span>{tax.type}</span>
                <div className="flex items-center gap-2">
                  <span>{formatCurrency(tax.amount, tax.currency)}</span>
                  {tax.included && (
                    <Badge variant="outline" className="text-xs">
                      Included
                    </Badge>
                  )}
                </div>
              </div>
            ))}

            {room.fees?.map(fee => (
              <div key={fee.type} className="flex justify-between text-sm">
                <span>{fee.type}</span>
                <div className="flex items-center gap-2">
                  <span>{formatCurrency(fee.amount, fee.currency)}</span>
                  {fee.included && (
                    <Badge variant="outline" className="text-xs">
                      Included
                    </Badge>
                  )}
                </div>
              </div>
            ))}

            {room.totalDiscount > 0 && (
              <div className="flex justify-between text-destructive">
                <span>Total Savings</span>
                <span>-{formatCurrency(room.totalDiscount, room.currency)}</span>
              </div>
            )}
          </div>

          <div className="pt-4 border-t">
            <div className="flex justify-between items-center font-bold text-lg">
              <span>Total Amount</span>
              <span>{formatCurrency(room.totalAmount, room.currency)}</span>
            </div>
            {room.totalDiscount > 0 && (
              <div className="flex justify-between items-center text-sm text-muted-foreground mt-1">
                <span>Original Price</span>
                <span className="line-through">
                  {formatCurrency(room.totalAmount + room.totalDiscount, room.currency)}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 