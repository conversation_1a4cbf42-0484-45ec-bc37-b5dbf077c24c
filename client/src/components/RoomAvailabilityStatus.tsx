import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { Alert, AlertDescription, AlertTitle } from './ui/alert.jsx';
import { AlertTriangle, CheckCircle2, Clock } from 'lucide-react';

interface RoomAvailabilityStatusProps {
  room: ExtendedRoom;
  checkIn: string;
  checkOut: string;
}

export default function RoomAvailabilityStatus({
  room,
  checkIn,
  checkOut
}: RoomAvailabilityStatusProps) {
  const checkInDate = new Date(checkIn);
  const checkOutDate = new Date(checkOut);
  const nights = Math.ceil(
    (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Availability Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-green-500" />
            <div>
              <p className="font-medium">Room Available</p>
              <p className="text-sm text-muted-foreground">
                For {nights} night{nights > 1 ? 's' : ''} from{' '}
                {checkInDate.toLocaleDateString()} to{' '}
                {checkOutDate.toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Check-in</p>
              <p className="text-sm text-muted-foreground">From 3:00 PM</p>
            </div>
            <div>
              <p className="text-sm font-medium">Check-out</p>
              <p className="text-sm text-muted-foreground">Until 12:00 PM</p>
            </div>
          </div>

          {room.restrictedRate && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Restricted Rate</AlertTitle>
              <AlertDescription>
                This rate has special conditions or restrictions. Please review the
                cancellation policy and rate rules carefully.
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <div className="flex gap-2">
              <Badge variant={room.refundable ? "outline" : "destructive"}>
                {room.refundable ? "Refundable" : "Non-refundable"}
              </Badge>
              {room.instantConfirmation && (
                <Badge variant="outline" className="bg-green-50">
                  Instant Confirmation
                </Badge>
              )}
            </div>

            {room.cancellationPolicy && (
              <p className="text-sm text-muted-foreground">
                {room.cancellationPolicy}
              </p>
            )}
          </div>

          {room.minimumStay || room.maximumStay ? (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Stay Requirements</h4>
              <div className="space-y-2 text-sm">
                {room.minimumStay && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>Minimum stay: {room.minimumStay} nights</span>
                  </div>
                )}
                {room.maximumStay && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>Maximum stay: {room.maximumStay} nights</span>
                  </div>
                )}
              </div>
            </div>
          ) : null}

          {room.availabilityStatus && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Availability Status</h4>
              <Badge variant="secondary">
                {room.availabilityStatus}
              </Badge>
              {room.remainingRooms && (
                <p className="text-sm text-muted-foreground mt-2">
                  Only {room.remainingRooms} room{room.remainingRooms > 1 ? 's' : ''} left at this rate
                </p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 