import { useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { Property } from "@db/schema";
import PropertyCard from "./PropertyCard";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { PropertyWithRates } from "@/types/schema";

interface RecommendationCarouselProps {
  userId?: number;
  className?: string;
}

export default function RecommendationCarousel({ className }: RecommendationCarouselProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    loop: true,
    skipSnaps: false,
    dragFree: true,
  });

  const { data: recommendations, isLoading } = useQuery<{
    properties: Property[];
    explanation: string;
  }>({
    queryKey: ["/api/recommendations"],
  });

  // Map properties to PropertyWithRates type
  const mappedProperties = recommendations?.properties.map(property => {
    // Convert property to compatible format
    return {
      id: property.id,
      name: property.name,
      description: property.description,
      address: property.address,
      city: property.city,
      state: property.state,
      country: property.country,
      latitude: typeof property.latitude === 'string' ? parseFloat(property.latitude) : property.latitude,
      longitude: typeof property.longitude === 'string' ? parseFloat(property.longitude) : property.longitude,
      rating: property.rating ? (typeof property.rating === 'string' ? parseFloat(property.rating) : property.rating) : undefined,
      reviewCount: property.reviewCount,
      basePrice: typeof property.basePrice === 'string' ? parseFloat(property.basePrice) : property.basePrice,
      currency: "USD", // Add default currency
      images: property.images,
      amenities: property.amenities,
      type: property.type || "hotel",
      rates: []
    };
  }) || [];

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  if (isLoading || !recommendations?.properties.length) {
    return null;
  }

  return (
    <div className={className}>
      <div className="relative">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            <AnimatePresence>
              {mappedProperties.map((property) => (
                <motion.div
                  key={property.id}
                  className="flex-[0_0_100%] min-w-0 pl-4 sm:flex-[0_0_50%] lg:flex-[0_0_33.333%]"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.5 }}
                >
                  <PropertyCard 
                    property={property} 
                    checkIn={new Date(Date.now() + 86400000)} // tomorrow
                    checkOut={new Date(Date.now() + 86400000 * 3)} // 3 days from tomorrow
                    guests="2"
                    rooms="1"
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>

        <div className="absolute inset-y-0 left-0 flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm"
            onClick={scrollPrev}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        <div className="absolute inset-y-0 right-0 flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm"
            onClick={scrollNext}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {recommendations.explanation && (
        <p className="mt-4 text-sm text-muted-foreground italic">
          {recommendations.explanation}
        </p>
      )}
    </div>
  );
}
