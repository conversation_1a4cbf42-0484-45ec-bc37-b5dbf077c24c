import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { 
  Sparkles, 
  Clock, 
  Recycle,
  Leaf,
  ShieldCheck,
  Spray
} from 'lucide-react';

interface RoomHousekeepingProps {
  room: ExtendedRoom;
}

export default function RoomHousekeeping({ room }: RoomHousekeepingProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Housekeeping Services</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="h-5 w-5 text-muted-foreground" />
              <h4 className="font-medium">Cleaning Schedule</h4>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>Daily housekeeping service included</span>
              </div>
              {room.cleaningFrequency && (
                <p className="text-muted-foreground">
                  {room.cleaningFrequency}
                </p>
              )}
            </div>
          </div>

          {room.cleaningProtocols && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2 mb-3">
                <ShieldCheck className="h-5 w-5 text-muted-foreground" />
                <h4 className="font-medium">Cleaning Protocols</h4>
              </div>
              <div className="space-y-2">
                {room.cleaningProtocols.map((protocol, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Spray className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{protocol}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {room.sustainabilityInitiatives && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2 mb-3">
                <Leaf className="h-5 w-5 text-muted-foreground" />
                <h4 className="font-medium">Sustainability Initiatives</h4>
              </div>
              <div className="space-y-2">
                {room.sustainabilityInitiatives.map((initiative, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Recycle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{initiative}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="pt-4 border-t">
            <h4 className="font-medium mb-3">Additional Services</h4>
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary">Extra Towels Available</Badge>
              <Badge variant="secondary">Turndown Service</Badge>
              <Badge variant="secondary">Laundry Service</Badge>
              {room.additionalServices?.map((service, index) => (
                <Badge key={index} variant="secondary">
                  {service}
                </Badge>
              ))}
            </div>
          </div>

          {room.housekeepingNotes && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Special Notes</h4>
              <p className="text-sm text-muted-foreground">
                {room.housekeepingNotes}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 