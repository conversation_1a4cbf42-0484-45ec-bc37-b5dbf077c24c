import { ExtendedRoom } from '@/types/schema.js';
import { useState } from 'react';
import { Card, CardContent } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { 
  ChevronLeft, 
  ChevronRight, 
  Maximize2, 
  Image as ImageIcon 
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from './ui/dialog';
import { VisuallyHidden } from '@/components/ui/visually-hidden';

interface RoomImagesProps {
  room: ExtendedRoom;
}

export default function RoomImages({ room }: RoomImagesProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreen, setShowFullscreen] = useState(false);

  if (!room.images?.length) return null;

  const handlePrevious = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? (room.images?.length || 1) - 1 : prev - 1
    );
  };

  const handleNext = () => {
    setCurrentImageIndex((prev) => 
      prev === (room.images?.length || 1) - 1 ? 0 : prev + 1
    );
  };

  const images = room.images || [];

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="relative">
          <div className="relative aspect-video">
            <img
              src={images[currentImageIndex]}
              alt={`Room view ${currentImageIndex + 1}`}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 right-4 flex gap-2">
              <Badge variant="secondary" className="backdrop-blur-sm">
                <ImageIcon className="h-4 w-4 mr-1" />
                {currentImageIndex + 1} / {images.length}
              </Badge>
              <Dialog open={showFullscreen} onOpenChange={setShowFullscreen}>
                <DialogTrigger asChild>
                  <Badge 
                    variant="secondary" 
                    className="cursor-pointer backdrop-blur-sm"
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Badge>
                </DialogTrigger>
                <DialogContent className="max-w-screen-xl h-[90vh]">
                  <VisuallyHidden>
                    <DialogTitle>Room Image Gallery</DialogTitle>
                  </VisuallyHidden>
                  <div className="relative w-full h-full">
                    <img
                      src={images[currentImageIndex]}
                      alt={`Room view ${currentImageIndex + 1}`}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <button
            onClick={handlePrevious}
            className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background/90 transition"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            onClick={handleNext}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background/90 transition"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </div>

        <div className="p-4 grid grid-cols-4 gap-2">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={`relative aspect-video rounded-md overflow-hidden ${
                index === currentImageIndex ? 'ring-2 ring-primary' : ''
              }`}
            >
              <img
                src={image}
                alt={`Room view ${index + 1}`}
                className="w-full h-full object-cover"
              />
            </button>
          ))}
        </div>

        {room.virtualTour && (
          <div className="p-4 pt-0">
            <a
              href={room.virtualTour}
              target="_blank"
              rel="noopener noreferrer"
              className="block w-full text-center py-2 bg-secondary hover:bg-secondary/80 rounded-md transition"
            >
              View Virtual Tour
            </a>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 