import React, { useEffect, useRef, useState, CSSProperties } from 'react';
import { Map as MapG<PERSON>, <PERSON><PERSON>, Popup, NavigationControl } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Button } from './ui/button.jsx';
import { PropertyWithRates, PropertyType } from '@/types/schema.js';
import PropertyMarkerPopup from './PropertyMarkerPopup.jsx';
import MapLoadingState from './MapLoadingState.jsx';
import { useTheme } from '@/components/theme-provider.jsx';
import Supercluster from 'supercluster';
import type { BBox, Feature, Point } from 'geojson';
import MapFilterControl from './MapFilterControl.jsx';
import { cn } from '../lib/utils.js';

// Add this CSS at the top of the file, after imports
const mapControlStyles: CSSProperties = {
  zIndex: 20,
  position: 'absolute',
  bottom: '20px',
  right: '20px',
  background: 'white',
  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  borderRadius: '4px',
};

export interface MapProps {
  properties: PropertyWithRates[];
  center?: { lat: number; lng: number };
  zoom?: number;
  onMapMoved?: (center: { lat: number; lng: number }, radius: number) => void;
  onSearchThisArea?: (center: { lat: number; lng: number }, radius: number) => void;
  propertyRates?: Record<number, number>;
  onPropertySelect?: (propertyId: number) => void;
  onFiltersChange?: (filters: {
    priceRange: [number, number];
    propertyTypes: PropertyType[];
  }) => void;
  className?: string;
  isFullScreen?: boolean;
  onToggleFullScreen?: () => void;
}

const PROPERTY_TYPE_ICONS: Record<PropertyType, string> = {
  hotel: '🏨',
  resort: '🌴',
  apartment: '🏢',
  villa: '🏡',
  guesthouse: '🏠'
};

interface PointFeature {
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
  properties: {
    id: number;
    property: PropertyWithRates;
  };
}

const Map: React.FC<MapProps> = ({
  properties,
  center,
  zoom,
  onMapMoved,
  onSearchThisArea,
  propertyRates,
  onPropertySelect,
  onFiltersChange,
  className,
  isFullScreen = false,
  onToggleFullScreen,
}) => {
  const mapRef = useRef<any>(null);
  const [viewport, setViewport] = useState({
    latitude: center?.lat || 0,
    longitude: center?.lng || 0,
    zoom: zoom || 12
  });
  const [selectedProperty, setSelectedProperty] = useState<PropertyWithRates | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { theme } = useTheme();
  const [clusters, setClusters] = useState<any[]>([]);
  const superclusterRef = useRef<Supercluster>();
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [selectedTypes, setSelectedTypes] = useState<PropertyType[]>([]);
  const [mapMoved, setMapMoved] = useState(false);

  useEffect(() => {
    if (center && !viewport.latitude && !viewport.longitude) {
      // Only set initial center if viewport is not already set
      setViewport(prev => ({
        ...prev,
        latitude: center.lat,
        longitude: center.lng
      }));
    } else if (!viewport.latitude && !viewport.longitude && properties.length > 0) {
      // Center on first property only if no center provided and viewport not set
      setViewport(prev => ({
        ...prev,
        latitude: Number(properties[0].latitude),
        longitude: Number(properties[0].longitude)
      }));
    }
  }, [center, properties, viewport.latitude, viewport.longitude]);

  const handleMapMove = () => {
    if (!mapRef.current) return;
    
    const map = mapRef.current.getMap();
    const bounds = map.getBounds();
    const center = bounds.getCenter();
    
    // Calculate approximate radius in meters
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const radius = getDistanceFromLatLonInM(
      center.lat,
      center.lng,
      ne.lat,
      ne.lng
    );

    onMapMoved?.({
      lat: center.lat,
      lng: center.lng
    }, radius);
    setMapMoved(true);
  };

  // Calculate distance between two points in meters
  const getDistanceFromLatLonInM = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  };

  const updateClusters = () => {
    if (!mapRef.current || !properties.length) return;
    
    const map = mapRef.current.getMap();
    const bounds = map.getBounds();
    
    // Initialize supercluster if not already done
    if (!superclusterRef.current) {
      superclusterRef.current = new Supercluster({
        radius: 40,
        maxZoom: 16,
      });
      
      // Load property points
      const points: PointFeature[] = properties.map(property => ({
        type: 'Feature',
        properties: { 
          id: property.id,
          property 
        },
        geometry: {
          type: 'Point',
          coordinates: [Number(property.longitude), Number(property.latitude)]
        }
      }));
      
      superclusterRef.current.load(points);
    }
    
    const clusters = superclusterRef.current.getClusters(
      [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()],
      Math.floor(map.getZoom())
    );
    
    setClusters(clusters);
  };

  useEffect(() => {
    if (properties.length > 0) {
      updateClusters();
    }
  }, [properties, viewport.zoom]);

  const handlePriceRangeChange = (range: [number, number]) => {
    setPriceRange(range);
    onFiltersChange?.({
      priceRange: range,
      propertyTypes: selectedTypes
    });
  };

  const handleTypeToggle = (type: PropertyType) => {
    setSelectedTypes(prev => {
      const newTypes = prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type];
      
      onFiltersChange?.({
        priceRange,
        propertyTypes: newTypes
      });
      
      return newTypes;
    });
  };

  const handleResetFilters = () => {
    setPriceRange([0, 1000]);
    setSelectedTypes([]);
    onFiltersChange?.({
      priceRange: [0, 1000],
      propertyTypes: []
    });
  };

  const handleZoomIn = () => {
    setViewport(prev => ({
      ...prev,
      zoom: Math.min((prev.zoom || 0) + 1, 20) // Max zoom level is 20
    }));
  };

  const handleZoomOut = () => {
    setViewport(prev => ({
      ...prev,
      zoom: Math.max((prev.zoom || 0) - 1, 1) // Min zoom level is 1
    }));
  };

  const renderPropertyMarker = (property: PropertyWithRates) => {
    const rates = property.rates || [];
    const hasRates = rates.length > 0;
    const price = hasRates ? Math.round(rates[0].totalAmount) : null;
    const hasDiscount = hasRates && rates[0].discountPercent > 0;
    
    return (
      <div className="relative group">
        <button
          className={cn(
            "bg-white text-foreground px-3 py-1.5 rounded-full text-sm font-medium shadow-lg transform transition-all hover:scale-105 flex items-center gap-1.5",
            hasDiscount ? "ring-2 ring-red-500" : "hover:bg-primary hover:text-primary-foreground",
            !hasRates && "bg-muted hover:bg-muted/80"
          )}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setSelectedProperty(property);
            if (!hasRates) {
              onPropertySelect?.(property.id);
            }
          }}
        >
          <span className="text-base">{PROPERTY_TYPE_ICONS[property.type as PropertyType] || '🏢'}</span>
          {price ? (
            <>
              ${price}
              {hasDiscount && (
                <span className="text-xs text-red-500 font-bold">
                  -{rates[0].discountPercent}%
                </span>
              )}
            </>
          ) : (
            <span className="text-xs font-medium">View rates</span>
          )}
        </button>
        {property.name && (
          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="bg-white/90 backdrop-blur-sm text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap">
              {property.name}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderCluster = (cluster: any) => {
    const [longitude, latitude] = cluster.geometry.coordinates;
    const { cluster: isCluster, point_count: pointCount } = cluster.properties;

    if (!isCluster) {
      return (
        <Marker
          key={cluster.properties.id}
          longitude={longitude}
          latitude={latitude}
        >
          {renderPropertyMarker(cluster.properties.property)}
        </Marker>
      );
    }

    // Calculate cluster size class based on point count
    const size = pointCount < 10 ? 'small' : pointCount < 50 ? 'medium' : 'large';
    
    return (
      <Marker
        key={cluster.id}
        longitude={longitude}
        latitude={latitude}
      >
        <button
          className={cn(
            "flex items-center justify-center rounded-full bg-primary text-primary-foreground font-bold shadow-lg transform transition-transform hover:scale-105",
            size === 'small' ? 'w-10 h-10 text-sm' : 
            size === 'medium' ? 'w-12 h-12 text-base' : 
            'w-14 h-14 text-lg'
          )}
          onClick={() => {
            const expansionZoom = Math.min(
              superclusterRef.current?.getClusterExpansionZoom(cluster.id) ?? 0,
              20
            );
            
            setViewport({
              ...viewport,
              latitude,
              longitude,
              zoom: expansionZoom
            });
          }}
        >
          {pointCount}
        </button>
      </Marker>
    );
  };

  return (
    <div className="relative w-full h-full">
      <MapGL
        ref={mapRef}
        mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
        {...viewport}
        onMove={evt => {
          setViewport(evt.viewState);
          handleMapMove();
        }}
        onLoad={() => {
          setIsLoading(false);
          updateClusters();
        }}
        onZoom={() => updateClusters()}
        mapStyle={theme === 'dark' ? 'mapbox://styles/mapbox/dark-v11' : 'mapbox://styles/cjlynch5/cm6o3hlv300pm01qqbgschdiy'}
        style={{ width: '100%', height: '100%' }}
      >
        {/* Search This Area Button */}
        <div className="absolute top-20 left-1/2 -translate-x-1/2 z-20">
          {mapMoved && (
            <Button
              variant="default"
              size="sm"
              onClick={() => {
                if (!mapRef.current) return;
                const map = mapRef.current.getMap();
                const center = map.getCenter();
                const bounds = map.getBounds();
                const ne = bounds.getNorthEast();
                const radius = getDistanceFromLatLonInM(
                  center.lat,
                  center.lng,
                  ne.lat,
                  ne.lng
                );
                onSearchThisArea?.({
                  lat: center.lat,
                  lng: center.lng
                }, radius);
                setMapMoved(false); // Reset after search
              }}
              className="shadow-lg bg-white hover:bg-gray-100 text-foreground px-6 py-2 font-medium rounded-full border border-border/50 backdrop-blur-sm"
            >
              Search this area
            </Button>
          )}
        </div>

        {/* Property Markers and Clusters */}
        {clusters.map(renderCluster)}

        {/* Selected Property Popup */}
        {selectedProperty && (
          <Popup
            latitude={Number(selectedProperty.latitude)}
            longitude={Number(selectedProperty.longitude)}
            closeButton={true}
            closeOnClick={false}
            onClose={() => setSelectedProperty(null)}
            anchor="bottom"
            offset={20}
          >
            <PropertyMarkerPopup 
              property={selectedProperty}
              onViewDetails={() => onPropertySelect?.(selectedProperty.id)}
            />
          </Popup>
        )}

        {/* Map Controls */}
        <div style={{
          position: 'absolute',
          bottom: '20px',
          right: '20px',
          zIndex: 20,
          background: 'white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          borderRadius: '4px',
        }}>
          <NavigationControl visualizePitch={true} showZoom={true} showCompass={true} />
        </div>

        {/* Filter Controls */}
        <div className="absolute top-4 left-4 z-20">
          <MapFilterControl
            priceRange={priceRange}
            onPriceRangeChange={handlePriceRangeChange}
            selectedTypes={selectedTypes}
            onTypeToggle={handleTypeToggle}
            onReset={handleResetFilters}
            className="bg-white shadow-lg rounded-lg"
          />
        </div>

        {/* Full Screen Toggle */}
        <Button
          variant="secondary"
          size="sm"
          className="absolute top-4 right-4 z-10 shadow-lg"
          onClick={onToggleFullScreen}
        >
          {isFullScreen ? 'Exit Full Screen' : 'Full Screen'}
        </Button>
      </MapGL>
      
      {isLoading && <MapLoadingState />}
    </div>
  );
};

export default Map;