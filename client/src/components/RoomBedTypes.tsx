import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Bed } from 'lucide-react';

interface RoomBedTypesProps {
  room: ExtendedRoom;
}

export default function RoomBedTypes({ room }: RoomBedTypesProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Bed Configuration</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-start gap-4">
            <Bed className="h-6 w-6 text-muted-foreground mt-1" />
            <div>
              <h4 className="font-medium">{room.bedType || room.bedTypeCode}</h4>
              <p className="text-sm text-muted-foreground mt-1">
                Suitable for up to {room.maxOccupancy} guests
              </p>
            </div>
          </div>

          {room.roomSize && (
            <div>
              <h4 className="font-medium mb-2">Room Size</h4>
              <p className="text-sm">{room.roomSize}</p>
            </div>
          )}

          {room.floorLevel && (
            <div>
              <h4 className="font-medium mb-2">Floor Level</h4>
              <p className="text-sm">{room.floorLevel}</p>
            </div>
          )}

          {room.bedArrangements && (
            <div>
              <h4 className="font-medium mb-2">Bed Arrangements</h4>
              <p className="text-sm">{room.bedArrangements}</p>
            </div>
          )}

          {room.maxOccupancy && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Occupancy Details</h4>
              <div className="space-y-2 text-sm">
                <p>Maximum occupancy: {room.maxOccupancy} guests</p>
                {room.maxAdults && <p>Maximum adults: {room.maxAdults}</p>}
                {room.maxChildren && <p>Maximum children: {room.maxChildren}</p>}
                {room.maxInfants && <p>Maximum infants: {room.maxInfants}</p>}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 