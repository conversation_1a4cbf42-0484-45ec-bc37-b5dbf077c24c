import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON>Title } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { PropertyWithRates } from "@/types/schema";
import { X } from "lucide-react";

interface CompareDrawerProps {
  properties: PropertyWithRates[];
  onRemove: (propertyId: number) => void;
  onClose: () => void;
  open: boolean;
}

const CompareDrawer: React.FC<CompareDrawerProps> = ({
  properties,
  onRemove,
  onClose,
  open
}) => {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-[80vh]">
        <SheetHeader>
          <SheetTitle>Compare Properties ({properties.length})</SheetTitle>
        </SheetHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4 overflow-auto">
          {properties.map(property => (
            <div key={property.id} className="relative border rounded-lg p-4">
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-2 right-2"
                onClick={() => onRemove(property.id)}
              >
                <X className="h-4 w-4" />
              </Button>
              
              <img
                src={property.images?.[0]}
                alt={property.name}
                className="w-full h-48 object-cover rounded-md mb-4"
              />
              
              <h3 className="font-medium mb-2">{property.name}</h3>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Rating</span>
                  <span>{property.rating}★</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Price</span>
                  <span>
                    {property.rates?.[0] && new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: property.rates[0].currency,
                    }).format(property.rates[0].rate)}
                    /night
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span>Location</span>
                  <span>{property.location}</span>
                </div>
                
                <div>
                  <span className="font-medium">Amenities</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {property.amenities?.slice(0, 5).map(amenity => (
                      <span key={amenity} className="text-xs bg-muted px-2 py-1 rounded">
                        {amenity}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default CompareDrawer; 