import { ExtendedRoom } from '@/types/schema.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

interface RoomCancellationPolicyProps {
  room: ExtendedRoom;
}

export default function RoomCancellationPolicy({ room }: RoomCancellationPolicyProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Cancellation Policy</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            {room.refundable ? (
              <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />
            ) : (
              <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
            )}
            <div>
              <h4 className="font-medium">
                {room.refundable ? 'Free Cancellation Available' : 'Non-Refundable Rate'}
              </h4>
              {room.cancellationPolicy && (
                <p className="text-sm text-muted-foreground mt-1">
                  {room.cancellationPolicy}
                </p>
              )}
            </div>
          </div>

          {room.depositPolicy && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Deposit Policy</h4>
              <p className="text-sm text-muted-foreground">
                {room.depositPolicy}
              </p>
            </div>
          )}

          {room.guaranteePolicy && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Guarantee Policy</h4>
              <p className="text-sm text-muted-foreground">
                {room.guaranteePolicy}
              </p>
            </div>
          )}

          <div className="pt-4 border-t">
            <h4 className="font-medium mb-2">Important Information</h4>
            <ul className="text-sm text-muted-foreground space-y-2">
              <li>• Check-in time starts at 3 PM</li>
              <li>• Check-out time is 12 PM</li>
              {room.maxOccupancy && (
                <li>• Maximum occupancy: {room.maxOccupancy} guests per room</li>
              )}
              {room.restrictedRate && (
                <li className="text-destructive">
                  • This is a restricted rate with special conditions
                </li>
              )}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 