
import { cn } from "@/lib/utils";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function Logo({ className, size = "md" }: LogoProps) {
  const sizes = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-10 w-10"
  };

  return (
    <svg
      viewBox="0 0 100 100"
      className={cn(sizes[size], "text-primary", className)}
      fill="none"
      stroke="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Compass Circle */}
      <circle cx="50" cy="50" r="45" strokeWidth="6" />
      {/* Compass Cardinal Points */}
      <line x1="50" y1="10" x2="50" y2="25" strokeWidth="6" />
      <line x1="50" y1="75" x2="50" y2="90" strokeWidth="6" />
      <line x1="10" y1="50" x2="25" y2="50" strokeWidth="6" />
      <line x1="75" y1="50" x2="90" y2="50" strokeWidth="6" />
      {/* Llama Silhouette */}
      <path
        d="M45 65 Q50 60 55 65 L50 45 L45 65"
        fill="currentColor"
        strokeWidth="2"
      />
      {/* Compass Needle */}
      <path
        d="M50 30 L45 55 L50 50 L55 55 L50 30"
        fill="currentColor"
        strokeWidth="2"
      />
    </svg>
  );
}
