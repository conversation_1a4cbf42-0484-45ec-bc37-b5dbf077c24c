import { Card } from "./card";
import { ShimmerDiv } from "./styled/shimmer";

/**
 * Shimmer loading card component with placeholder content
 */
export function ShimmerCard() {
  return (
    <Card className="w-full">
      <div className="flex p-4 gap-4">
        <ShimmerDiv className="w-[280px] h-[200px] rounded-lg" />
        <div className="flex-1 space-y-4">
          <ShimmerDiv className="h-8 w-3/4" />
          <div className="space-y-2">
            <ShimmerDiv className="h-4 w-1/2" />
            <ShimmerDiv className="h-4 w-1/3" />
          </div>
          <div className="flex gap-2">
            {[1, 2, 3].map((i) => (
              <ShimmerDiv key={i} className="h-6 w-16 rounded-full" />
            ))}
          </div>
          <div className="flex justify-between items-end">
            <div className="space-y-2">
              <ShimmerDiv className="h-6 w-24" />
              <ShimmerDiv className="h-4 w-32" />
            </div>
            <ShimmerDiv className="h-10 w-28" />
          </div>
        </div>
      </div>
    </Card>
  );
} 