import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface LoadingCardProps {
  className?: string;
  isMobile?: boolean;
}

export function PropertyLoadingCard({ className, isMobile = false }: LoadingCardProps) {
  return (
    <div className={cn("w-full flex", isMobile ? "flex-col" : "md:flex-row", className)}>
      <div className={cn(
        "relative w-full md:w-1/3 lg:w-1/4",
        isMobile ? "h-48" : "h-64",
        "overflow-hidden rounded-t-lg md:rounded-l-lg md:rounded-tr-none"
      )}>
        <Skeleton className="h-full w-full" />
      </div>

      <Card className={cn(
        "flex-1 overflow-hidden", 
        isMobile && "rounded-t-none border-t-0"
      )}>
        <CardContent className={cn("p-4 animate-pulse", isMobile && "p-3")}>
          <div className="flex justify-between items-start gap-4">
            <div className="min-w-0 flex-1">
              <Skeleton className="h-5 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-2" />
              <div className="flex items-center gap-1 mt-1">
                <Skeleton className="h-4 w-10 rounded-md" />
              </div>
            </div>
            <div className="text-right flex flex-col items-end justify-start shrink-0">
              <div className="space-y-2">
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-4 w-20" />
              </div>
            </div>
          </div>

          {!isMobile && (
            <div className="mt-3">
              <Skeleton className="h-4 w-full mb-1" />
              <Skeleton className="h-4 w-5/6" />
            </div>
          )}

          <div className={cn(
            "flex flex-wrap gap-1.5",
            isMobile ? "mt-2" : "mt-3"
          )}>
            {Array.from({ length: isMobile ? 3 : 5 }).map((_, i) => (
              <Skeleton key={i} className="h-5 w-16 rounded-full" />
            ))}
          </div>

          {!isMobile && (
            <div className="mt-3 flex gap-3">
              <Skeleton className="h-4 w-24 rounded-full" />
              <Skeleton className="h-4 w-32 rounded-full" />
            </div>
          )}
        </CardContent>
        <CardFooter className={cn(
          "px-4 pb-4",
          isMobile && "p-3 pt-0"
        )}>
          <Skeleton className="h-9 w-full rounded-md" />
        </CardFooter>
      </Card>
    </div>
  );
}