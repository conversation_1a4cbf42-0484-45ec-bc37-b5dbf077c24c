import React from "react";
import { cn } from "@/lib/utils";

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  size?: "default" | "sm" | "lg" | "full";
}

/**
 * A responsive container with predefined max-widths
 */
export function Container({
  children,
  className,
  size = "default",
  ...props
}: ContainerProps) {
  const sizeClasses = {
    sm: "max-w-3xl",
    default: "max-w-5xl",
    lg: "max-w-7xl",
    full: "max-w-none"
  };
  
  return (
    <div 
      className={cn(
        "mx-auto w-full px-4 sm:px-6 lg:px-8", 
        sizeClasses[size],
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
}