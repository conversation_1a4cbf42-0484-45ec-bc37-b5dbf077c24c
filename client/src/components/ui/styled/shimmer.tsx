import styled from '@emotion/styled';
import { Card } from "../card";

/**
 * CSS keyframes for shimmer loading animation
 */
export const shimmerKeyframes = `
  @keyframes shimmer {
    0% {
      background-position: -1000px 0;
    }
    100% {
      background-position: 1000px 0;
    }
  }
`;

/**
 * Base shimmer styles that can be applied to any component
 */
export const shimmerStyles = `
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 1200px 100%;
  animation: shimmer 1.5s infinite linear;
  ${shimmerKeyframes}
`;

/**
 * Styled card component with shimmer effect
 */
export const ShimmerCard = styled(Card)`
  ${shimmerStyles}
`;

/**
 * Styled div component with shimmer effect
 */
export const ShimmerDiv = styled.div`
  ${shimmerStyles}
`; 