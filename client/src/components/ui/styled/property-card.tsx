import styled from '@emotion/styled';

/**
 * Container component for the property card
 */
export const PropertyCardContainer = styled.div`
  width: 100%;
  max-width: 900px;
  min-width: 900px;
  margin: 8px auto;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  background: white;
  display: flex;
  overflow: hidden;
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;

  > div:last-child {
    flex: 1;
    min-width: 0;
    width: calc(100% - 280px);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }
`;

/**
 * Image section component with hover effects
 */
export const ImageSection = styled.div`
  width: 280px;
  min-width: 280px;
  max-width: 280px;
  height: 280px;
  min-height: 280px;
  max-height: 280px;
  position: relative;
  cursor: pointer;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
`;

/**
 * Discount badge component with consistent styling
 */
export const DiscountBadge = styled.div`
  position: absolute;
  top: 12px;
  left: 12px;
  background: #e41e31;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 1;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
`; 