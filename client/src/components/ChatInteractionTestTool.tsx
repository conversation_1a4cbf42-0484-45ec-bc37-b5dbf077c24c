import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, MapPin, AlertTriangle, CheckCircle2, MessageSquare, Building, Link } from "lucide-react";
import { testChatInteraction, runChatIntegrationTests } from "@/utils/chat-interaction-test";
import LocationMarker from "@/components/LocationMarker";

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
}

interface PropertyReference {
  id: number;
  name: string;
}

interface LinkAction {
  type: 'link';
  target: string;
  label: string;
}

interface ChatInteractionResult {
  success: boolean;
  location?: LocationData;
  properties?: PropertyReference[];
  conversations?: {
    query: string;
    response: string[];
  }[];
  actions?: {
    links?: LinkAction[];
    bookingAttempt?: boolean;
    searchInitiated?: boolean;
  };
  error?: string;
  duration: number;
}

/**
 * Interactive tool for testing the chat interaction flow
 */
export default function ChatInteractionTestTool() {
  const [initialQuery, setInitialQuery] = useState<string>("I'm looking for hotels in Miami Beach");
  const [followupQueries, setFollowupQueries] = useState<string[]>(["Tell me more about the first hotel"]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [testResult, setTestResult] = useState<ChatInteractionResult | null>(null);
  const [activeTab, setActiveTab] = useState<string>("singleTest");
  const [testHistory, setTestHistory] = useState<{query: string, result: ChatInteractionResult}[]>([]);
  const [allTestResults, setAllTestResults] = useState<{
    results: ChatInteractionResult[];
    overallSuccess: boolean;
  } | null>(null);
  
  // Add a followup query input
  const addFollowupQuery = () => {
    setFollowupQueries([...followupQueries, ""]);
  };
  
  // Update a followup query
  const updateFollowupQuery = (index: number, value: string) => {
    const updated = [...followupQueries];
    updated[index] = value;
    setFollowupQueries(updated);
  };
  
  // Remove a followup query
  const removeFollowupQuery = (index: number) => {
    setFollowupQueries(followupQueries.filter((_, i) => i !== index));
  };
  
  // Run a single test with the current queries
  const runSingleTest = async () => {
    if (!initialQuery.trim()) return;
    
    setIsLoading(true);
    setTestResult(null);
    
    try {
      const nonEmptyFollowups = followupQueries.filter(q => q.trim());
      const result = await testChatInteraction(initialQuery, nonEmptyFollowups);
      setTestResult(result);
      
      // Add to history
      setTestHistory(prev => [{
        query: initialQuery, 
        result
      }, ...prev.slice(0, 4)]);
    } catch (error) {
      console.error("Test failed:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Run all predefined tests
  const runAllTests = async () => {
    setIsLoading(true);
    setAllTestResults(null);
    
    try {
      const results = await runChatIntegrationTests();
      setAllTestResults(results);
    } catch (error) {
      console.error("Tests failed:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-6">
      <Tabs defaultValue="singleTest" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="singleTest">Single Test</TabsTrigger>
          <TabsTrigger value="batchTests">Batch Tests</TabsTrigger>
        </TabsList>
        
        <TabsContent value="singleTest" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Chat Interaction Test</CardTitle>
              <CardDescription>
                Test the complete chat interaction flow from initial query to contextual links
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium" htmlFor="initialQuery">Initial Query</label>
                <Input
                  id="initialQuery"
                  value={initialQuery}
                  onChange={(e) => setInitialQuery(e.target.value)}
                  placeholder="Enter a location query, e.g., 'Find hotels in New York'"
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Follow-up Queries</label>
                  <Button variant="outline" size="sm" onClick={addFollowupQuery}>
                    Add Query
                  </Button>
                </div>
                
                {followupQueries.map((query, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      value={query}
                      onChange={(e) => updateFollowupQuery(index, e.target.value)}
                      placeholder={`Follow-up query ${index + 1}`}
                      className="flex-1"
                    />
                    <Button 
                      variant="ghost"
                      size="icon"
                      onClick={() => removeFollowupQuery(index)}
                      className="text-destructive"
                    >
                      ✕
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={runSingleTest} 
                disabled={isLoading || !initialQuery.trim()}
                className="w-full"
              >
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Run Test
              </Button>
            </CardFooter>
          </Card>
          
          {/* Test Result */}
          {testResult && (
            <Card className={testResult.success ? "border-green-500/50" : "border-red-500/50"}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    {testResult.success ? (
                      <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
                    )}
                    {testResult.success ? "Test Passed" : "Test Failed"}
                  </CardTitle>
                  <Badge variant="outline">{testResult.duration}ms</Badge>
                </div>
                <CardDescription>
                  Initial query: <span className="font-medium">{initialQuery}</span>
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Location Section */}
                {testResult.location && (
                  <div className="rounded-md bg-muted p-4">
                    <h3 className="text-sm font-medium mb-2 flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      Detected Location
                    </h3>
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="space-y-2 flex-1">
                        <div className="grid grid-cols-2 gap-1 text-sm">
                          <span className="text-muted-foreground">Name:</span>
                          <span className="font-medium">{testResult.location.name}</span>
                          
                          <span className="text-muted-foreground">Latitude:</span>
                          <span className="font-mono text-xs">{testResult.location.lat}</span>
                          
                          <span className="text-muted-foreground">Longitude:</span>
                          <span className="font-mono text-xs">{testResult.location.lng}</span>
                          
                          {testResult.location.placeType && (
                            <>
                              <span className="text-muted-foreground">Type:</span>
                              <span>{testResult.location.placeType}</span>
                            </>
                          )}
                        </div>
                      </div>
                      
                      <div className="min-w-[200px] h-[150px] bg-gray-100 rounded-md overflow-hidden relative">
                        <LocationMarker 
                          location={testResult.location}
                          className="w-full h-full"
                        />
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Properties Section */}
                {testResult.properties && testResult.properties.length > 0 && (
                  <div className="rounded-md bg-muted p-4">
                    <h3 className="text-sm font-medium mb-2 flex items-center">
                      <Building className="h-4 w-4 mr-1" />
                      Recommended Properties
                    </h3>
                    <div className="grid gap-2">
                      {testResult.properties.map((property, index) => (
                        <div key={index} className="flex items-center justify-between bg-background p-2 rounded-md">
                          <div>
                            <div className="font-medium">{property.name}</div>
                            <div className="text-xs text-muted-foreground">ID: {property.id}</div>
                          </div>
                          <a 
                            href={`/property/${property.id}`} 
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-blue-600 hover:underline"
                          >
                            View Details
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Actions Section */}
                {testResult.actions && (
                  <div className="rounded-md bg-muted p-4">
                    <h3 className="text-sm font-medium mb-2 flex items-center">
                      <Link className="h-4 w-4 mr-1" />
                      Contextual Actions
                    </h3>
                    <div className="space-y-2">
                      {testResult.actions.links && testResult.actions.links.length > 0 && (
                        <div className="grid gap-2">
                          <div className="text-sm text-muted-foreground">Generated Links:</div>
                          {testResult.actions.links.map((link, index) => (
                            <div key={index} className="bg-background p-2 rounded-md flex items-center justify-between">
                              <div className="text-sm">{link.label}</div>
                              <a 
                                href={link.target} 
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:underline"
                              >
                                {link.target}
                              </a>
                            </div>
                          ))}
                        </div>
                      )}
                      
                      {testResult.actions.searchInitiated && (
                        <div className="bg-green-50 text-green-700 p-2 rounded-md flex items-center">
                          <CheckCircle2 className="h-4 w-4 mr-1" />
                          Search was initiated
                        </div>
                      )}
                      
                      {testResult.actions.bookingAttempt && (
                        <div className="bg-blue-50 text-blue-700 p-2 rounded-md flex items-center">
                          <CheckCircle2 className="h-4 w-4 mr-1" />
                          Booking attempt detected
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                {/* Conversations Section */}
                {testResult.conversations && testResult.conversations.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium mb-2 flex items-center">
                      <MessageSquare className="h-4 w-4 mr-1" />
                      Conversation Flow
                    </h3>
                    {testResult.conversations.map((conversation, index) => (
                      <div key={index} className="mb-4">
                        <div className="font-medium text-sm flex items-center mb-1">
                          <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                            <span className="text-xs text-primary">U</span>
                          </div>
                          {conversation.query}
                        </div>
                        <div className="pl-8">
                          <div className="font-medium text-sm flex items-center mb-1">
                            <div className="h-6 w-6 rounded-full bg-secondary/10 flex items-center justify-center mr-2">
                              <span className="text-xs text-secondary">A</span>
                            </div>
                            Response
                          </div>
                          <div className="pl-8 text-sm">
                            {conversation.response.map((response, rIndex) => (
                              <div key={rIndex} className="mb-2">{response}</div>
                            ))}
                          </div>
                        </div>
                        {index < (testResult.conversations?.length || 0) - 1 && <Separator className="my-2" />}
                      </div>
                    ))}
                  </div>
                )}
                
                {/* Error Section */}
                {testResult.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <h3 className="text-sm font-medium text-red-800 mb-1">Error Details</h3>
                    <p className="text-sm text-red-700">{testResult.error}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
          
          {/* Test History */}
          {testHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Test History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {testHistory.map((item, index) => (
                    <div key={index} className="text-sm">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 font-medium truncate pr-2">{item.query}</div>
                        {item.result.success ? (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            Success
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                            Failed
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mt-1">
                        {item.result.location && (
                          <Badge variant="outline" className="flex items-center text-xs">
                            <MapPin className="h-3 w-3 mr-1" />
                            {item.result.location.name}
                          </Badge>
                        )}
                        
                        {item.result.properties && (
                          <Badge variant="outline" className="flex items-center text-xs">
                            <Building className="h-3 w-3 mr-1" />
                            {item.result.properties.length} properties
                          </Badge>
                        )}
                        
                        {item.result.actions?.links && (
                          <Badge variant="outline" className="flex items-center text-xs">
                            <Link className="h-3 w-3 mr-1" />
                            {item.result.actions.links.length} links
                          </Badge>
                        )}
                      </div>
                      
                      {index < testHistory.length - 1 && <Separator className="my-2" />}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="batchTests" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Batch Test Runner</CardTitle>
              <CardDescription>
                Run predefined test scenarios to validate the complete chat interaction flow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm mb-4">
                This will run a series of predefined test scenarios covering different user journeys:
              </p>
              <ul className="list-disc pl-5 text-sm space-y-2 mb-4">
                <li>Location detection with hotel recommendations</li>
                <li>Property details follow-up queries</li>
                <li>Amenities and facilities questions</li>
                <li>Area recommendations and contextual search</li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                onClick={runAllTests}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Run All Tests
              </Button>
            </CardFooter>
          </Card>
          
          {/* Batch Test Results */}
          {allTestResults && (
            <Card className={allTestResults.overallSuccess ? "border-green-500/50" : "border-red-500/50"}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    {allTestResults.overallSuccess ? (
                      <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
                    )}
                    Test Results
                  </CardTitle>
                  <Badge variant="outline" className={allTestResults.overallSuccess ? "bg-green-50 text-green-700 border-green-200" : "bg-red-50 text-red-700 border-red-200"}>
                    {allTestResults.results.filter(r => r.success).length}/{allTestResults.results.length} Passed
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {allTestResults.results.map((result, index) => (
                    <Card key={index} className="overflow-hidden">
                      <CardHeader className={`py-3 ${result.success ? "bg-green-50" : "bg-red-50"}`}>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center">
                            {result.success ? (
                              <CheckCircle2 className="mr-2 h-4 w-4 text-green-600" />
                            ) : (
                              <AlertTriangle className="mr-2 h-4 w-4 text-red-600" />
                            )}
                            Scenario {index + 1}
                          </CardTitle>
                          <Badge variant="outline">{result.duration}ms</Badge>
                        </div>
                        <CardDescription>
                          {result.conversations && result.conversations[0]?.query}
                        </CardDescription>
                      </CardHeader>
                      
                      <CardContent className="py-3">
                        <div className="text-sm space-y-2">
                          {/* Test details */}
                          <div className="grid grid-cols-2 gap-2">
                            <div className="font-medium">Location:</div>
                            <div>{result.location ? result.location.name : "Not detected"}</div>
                            
                            <div className="font-medium">Properties:</div>
                            <div>{result.properties ? `${result.properties.length} found` : "None"}</div>
                            
                            <div className="font-medium">Conversation:</div>
                            <div>
                              {result.conversations
                                ? `${result.conversations.length} exchanges`
                                : "None"}
                            </div>
                            
                            <div className="font-medium">Actions:</div>
                            <div>
                              {result.actions?.links
                                ? `${result.actions.links.length} links`
                                : "None"}
                              {result.actions?.searchInitiated && ", search initiated"}
                              {result.actions?.bookingAttempt && ", booking attempted"}
                            </div>
                          </div>
                          
                          {/* Error if any */}
                          {result.error && (
                            <div className="text-sm mt-2 text-red-600">
                              Error: {result.error}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}