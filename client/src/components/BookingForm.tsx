import { useForm } from "react-hook-form";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useCreateReservation } from "@/lib/api";
import { Property } from "@db/schema";
import type { Reservation } from "@db/schema";
import { useToast } from "@/hooks/use-toast";
import { loadStripe } from "@stripe/stripe-js";
import { Elements, CardElement, useStripe, useElements } from "@stripe/react-stripe-js";

interface BookingFormProps {
  property: Property;
}

interface BookingFormValues {
  email: string;
  firstName: string;
  lastName: string;
  checkIn: string;
  checkOut: string;
  guests: number;
}

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

function BookingFormContent({ property }: BookingFormProps) {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const createReservation = useCreateReservation();
  const stripe = useStripe();
  const elements = useElements();

  const form = useForm<BookingFormValues>({
    defaultValues: {
      guests: 2,
    },
  });

  async function onSubmit(values: BookingFormValues) {
    try {
      if (!stripe || !elements) {
        throw new Error("Stripe has not been initialized");
      }

      // Create the reservation with properly typed data
      const reservationData: Partial<Reservation> = {
        propertyId: property.id,
        checkIn: new Date(values.checkIn),
        checkOut: new Date(values.checkOut),
        totalPrice: property.basePrice.toString(),
        status: "pending",
      };

      const reservation = await createReservation.mutateAsync(reservationData);

      const cardElement = elements.getElement(CardElement);
      if (!cardElement) {
        throw new Error("Card element not found");
      }

      const { error, paymentIntent } = await stripe.confirmCardPayment(
        reservation.paymentIntent,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: `${values.firstName} ${values.lastName}`,
              email: values.email,
            },
          },
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      toast({
        title: "Success",
        description: "Your booking has been confirmed",
      });

      setLocation("/reservations");
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Booking failed",
        variant: "destructive",
      });
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            rules={{ required: "First name is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastName"
            rules={{ required: "Last name is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="email"
          rules={{ 
            required: "Email is required",
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: "Invalid email address",
            },
          }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="checkIn"
            rules={{ required: "Check-in date is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Check-in Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="checkOut"
            rules={{ required: "Check-out date is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Check-out Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="guests"
          rules={{ 
            required: "Number of guests is required",
            min: { value: 1, message: "Minimum 1 guest required" },
            max: { value: 10, message: "Maximum 10 guests allowed" },
          }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Guests</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min={1}
                  max={10}
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <h3 className="font-semibold">Payment Details</h3>
          <div className="p-4 border rounded-md">
            <CardElement options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
                invalid: {
                  color: '#9e2146',
                },
              },
            }} />
          </div>
        </div>

        <Button 
          type="submit" 
          className="w-full"
          disabled={!stripe}
        >
          Complete Booking (${property.basePrice}/night)
        </Button>
      </form>
    </Form>
  );
}

// Wrap the form with Stripe Elements
export default function BookingForm(props: BookingFormProps) {
  return (
    <Elements stripe={stripePromise}>
      <BookingFormContent {...props} />
    </Elements>
  );
}