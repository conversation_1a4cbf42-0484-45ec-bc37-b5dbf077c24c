import React from "react";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from "@/components/ui/navigation-menu";
import { Menu, User, Settings, LogOut } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { Logo } from "./Logo";
import { useAuth } from "@/hooks/use-auth";

const mainNav = [
  {
    title: "Find Rooms",
    href: "/",
  },
  {
    title: "Enhanced AI Chat",
    href: "/enhanced-ai-chat",
  },
  {
    title: "My Bookings",
    href: "/bookings",
  },
  {
    title: "My Profile",
    href: "/profile",
  }
];

export default function Header() {
  const { user, logoutMutation } = useAuth();
  
  const isAuthenticated = !!user;
  const isAdmin = user?.isAdmin || false;

  // Admin navigation items
  const adminNav = [
    {
      title: "Admin Dashboard",
      href: "/admin",
    }
  ];
  
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-8">
          <Link href="/" className="flex items-center space-x-2">
            <Logo size="sm" />
            <span className="hidden font-bold sm:inline-block">
              RoomLama
            </span>
          </Link>
          <NavigationMenu>
            <NavigationMenuList>
              {/* Show only "Find Rooms" when not authenticated */}
              {!isAuthenticated ? (
                <NavigationMenuItem>
                  <NavigationMenuLink
                    href="/"
                    className={cn(
                      "group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}
                  >
                    Find Rooms
                  </NavigationMenuLink>
                </NavigationMenuItem>
              ) : (
                /* Show all navigation items when authenticated */
                mainNav.map((item) => (
                  <NavigationMenuItem key={item.href}>
                    <NavigationMenuLink
                      href={item.href}
                      className={cn(
                        "group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                      )}
                    >
                      {item.title}
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                ))
              )}
              
              {/* Admin menu items - only visible if user is admin */}
              {isAuthenticated && isAdmin && adminNav.map((item) => (
                <NavigationMenuItem key={item.href}>
                  <NavigationMenuLink
                    href={item.href}
                    className={cn(
                      "group inline-flex h-9 w-max items-center justify-center rounded-md bg-primary text-primary-foreground px-4 py-2 text-sm font-medium transition-colors hover:bg-primary/90 focus:bg-primary/90 focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                    )}
                  >
                    {item.title}
                  </NavigationMenuLink>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
        </div>
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
            >
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="pr-0">
            <Link href="/" className="flex items-center space-x-2">
              <Logo size="sm" />
              <span className="font-bold">RoomLama</span>
            </Link>
            <div className="my-4 flex flex-col space-y-2">
              {/* Show only "Find Rooms" for non-authenticated users */}
              {!isAuthenticated ? (
                <Link href="/">
                  <Button variant="ghost" className="w-full justify-start">
                    Find Rooms
                  </Button>
                </Link>
              ) : (
                /* Show all navigation for authenticated users */
                mainNav.map((item) => (
                  <Link key={item.href} href={item.href}>
                    <Button variant="ghost" className="w-full justify-start">
                      {item.title}
                    </Button>
                  </Link>
                ))
              )}
              
              {/* Admin menu items for mobile - only visible if user is admin */}
              {isAuthenticated && isAdmin && adminNav.map((item) => (
                <Link key={item.href} href={item.href}>
                  <Button variant="default" className="w-full justify-start">
                    {item.title}
                  </Button>
                </Link>
              ))}
              
              {/* Authentication links for mobile */}
              <div className="pt-2 mt-2 border-t">
                {isAuthenticated ? (
                  <>
                    <div className="p-3">
                      <p className="text-sm font-medium">Signed in as:</p>
                      <p className="text-sm text-muted-foreground">
                        {user?.preferences?.displayName || 
                         user?.email?.split('@')[0] || 'User'}
                      </p>
                    </div>
                    <Link href="/profile">
                      <Button 
                        variant="ghost" 
                        className="w-full justify-start"
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        Profile Settings
                      </Button>
                    </Link>
                    <Button 
                      variant="ghost" 
                      className="w-full justify-start"
                      onClick={() => logoutMutation.mutate()}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      Sign Out
                    </Button>
                  </>
                ) : (
                  <>
                    <Link href="/auth">
                      <Button 
                        variant="ghost" 
                        className="w-full justify-start"
                      >
                        Sign In
                      </Button>
                    </Link>
                    <Link href="/auth">
                      <Button 
                        variant="default" 
                        className="w-full justify-start mt-2"
                      >
                        Get Started
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </SheetContent>
        </Sheet>
        <div className="flex items-center space-x-4">
          <nav className="flex items-center space-x-2">
            {isAuthenticated ? (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="hidden md:flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      <span>
                        {user?.preferences?.displayName || 
                         user?.email?.split('@')[0] || 'User'}
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>My Account</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <Link href="/profile">
                      <DropdownMenuItem className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Profile Settings</span>
                      </DropdownMenuItem>
                    </Link>
                    <Link href="/bookings">
                      <DropdownMenuItem className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        <span>My Bookings</span>
                      </DropdownMenuItem>
                    </Link>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      className="cursor-pointer"
                      onClick={() => logoutMutation.mutate()}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign Out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <Link href="/auth">
                  <Button 
                    variant="ghost" 
                    className="hidden md:flex"
                  >
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth">
                  <Button>
                    Get Started
                  </Button>
                </Link>
              </>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
}