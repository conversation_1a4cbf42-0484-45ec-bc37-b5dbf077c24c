import { AlertCircle, Tag } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";
import { Rate } from "@/types/rate";
import { cn } from "@/lib/utils";

interface RateDisplayProps {
  rates?: Rate[];
  isLoading?: boolean;
  error?: string;
  nights?: number;
  className?: string;
}

/**
 * Component for displaying rate information with loading and error states
 */
export function RateDisplay({ rates, isLoading, error, nights = 1, className }: RateDisplayProps) {
  if (isLoading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-4 w-20" />
        <div className="flex gap-2 mt-1">
          <Skeleton className="h-3 w-3 rounded-full" />
          <Skeleton className="h-3 w-16" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-sm text-destructive flex items-center gap-1">
        <AlertCircle className="h-4 w-4" />
        {error}
      </div>
    );
  }

  if (!rates || rates.length === 0) {
    return (
      <div className="text-sm text-muted-foreground font-medium">
        No Availability
      </div>
    );
  }

  const rate = rates[0];
  const formattedTotal = formatCurrency(rate.totalAmount, rate.currency);
  const formattedOriginal = formatCurrency(rate.originalAmount, rate.currency);
  const formattedPerNight = formatCurrency(Math.round(rate.totalAmount / nights), rate.currency);
  const formattedOriginalPerNight = formatCurrency(Math.round(rate.originalAmount / nights), rate.currency);
  const hasDiscount = rate.discountPercent > 0;

  return (
    <div className={cn("space-y-1", className)}>
      {hasDiscount ? (
        <>
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-sm line-through text-muted-foreground">{formattedOriginalPerNight}</span>
              <span className="text-lg font-bold text-primary">{formattedPerNight}</span>
              <span className="text-sm text-muted-foreground">/ night</span>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm line-through text-muted-foreground">{formattedOriginal}</span>
              <span className="font-medium text-primary">{formattedTotal} total</span>
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold">{formattedPerNight}</span>
              <span className="text-sm text-muted-foreground">/ night</span>
            </div>
            <div className="mt-1 font-medium">{formattedTotal} total</div>
          </div>
        </>
      )}
      {rate.description && (
        <p className="text-sm text-muted-foreground">{rate.description}</p>
      )}
    </div>
  );
} 