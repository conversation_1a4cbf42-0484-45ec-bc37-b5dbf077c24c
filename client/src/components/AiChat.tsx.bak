import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Property, PropertyWithRates } from "@/types/schema";
import PropertyCard from "./PropertyCard";
import LocationMarker from "./LocationMarker";
import { MessageCircle, Send, MapPin, Hotel, Calendar, DollarSign, Star, X, ChevronRight, Search as SearchIcon, Lightbulb, Info, ListFilter, Map as MapIcon, Landmark, Building, Waves } from "lucide-react";
import { useLocation } from "wouter";
import styled from '@emotion/styled';
import { cn } from "@/lib/utils";
import { useGooglePlaces } from "@/hooks/use-google-places";
import { useToast } from "@/hooks/use-toast";

// Add custom CSS for the clickable elements
const globalStyles = `
  .property-link, .location-link {
    color: #0066cc;
    text-decoration: none;
    border-bottom: 1px dotted #0066cc;
    padding-bottom: 1px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: rgba(0, 102, 204, 0.05);
    padding: 0 4px;
    border-radius: 4px;
    font-weight: 500;
    display: inline-block;
  }
  
  .property-link {
    border-left: 2px solid #0066cc;
  }
  
  .location-link {
    border-left: 2px solid #00cc66;
  }
  
  .property-link:hover, .location-link:hover {
    background-color: rgba(0, 102, 204, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

// Add the styles to the head of the document
if (typeof document !== 'undefined') {
  const styleEl = document.createElement('style');
  styleEl.innerHTML = globalStyles;
  document.head.appendChild(styleEl);
}

// Styled components for enhanced chat UI
const ChatContainer = styled(Card)<{ variant: 'integrated' | 'floating' | 'modal' }>`
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);

  ${props => {
    switch (props.variant) {
      case 'modal':
        return `
          position: fixed;
          inset: 0;
          width: 100vw;
          height: 100vh;
          max-width: 100vw;
          margin: 0;
          border-radius: 0;
          z-index: 1000;
          background: rgba(255, 255, 255, 0.98);
          backdrop-filter: blur(8px);
          animation: slideIn 0.3s ease-out;

          @keyframes slideIn {
            from {
              transform: translateY(100%);
              opacity: 0;
            }
            to {
              transform: translateY(0);
              opacity: 1;
            }
          }
        `;
      case 'integrated':
        return `
          height: 100%;
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
        `;
      case 'floating':
        return `
          height: 600px;
          width: 380px;
          position: fixed;
          right: 24px;
          bottom: 24px;
          z-index: 1000;
        `;
    }
  }}
`;

const MessageBubble = styled.div<{ isUser: boolean }>`
  max-width: 85%;
  padding: 16px;
  margin: 8px 12px;
  border-radius: ${props => props.isUser ? '16px 16px 0 16px' : '16px 16px 16px 0'};
  background: ${props => props.isUser ? '#E1F0FF' : '#ffffff'};
  color: ${props => props.isUser ? '#0066cc' : '#1a1a1a'};
  align-self: ${props => props.isUser ? 'flex-end' : 'flex-start'};
  font-size: 1rem;
  line-height: 1.6;
  word-wrap: break-word;
  box-shadow: ${props => props.isUser ? 
    '0 2px 4px rgba(0, 102, 204, 0.1)' : 
    '0 2px 8px rgba(0, 0, 0, 0.05)'};
  border: ${props => props.isUser ? '1px solid rgba(0, 102, 204, 0.1)' : '1px solid #e5e7eb'};
  transition: all 0.2s ease;

  /* Enhanced markdown formatting */
  p {
    margin-bottom: 1.25rem;
    &:last-child {
      margin-bottom: 0;
    }
  }

  ul, ol {
    margin: 1rem 0;
    padding-left: 1.75rem;
  }

  li {
    margin: 0.75rem 0;
    position: relative;
    padding-left: 0.75rem;
    line-height: 1.5;
  }

  strong {
    font-weight: 600;
    color: ${props => props.isUser ? '#0066cc' : '#0066cc'};
    background: ${props => props.isUser ? 'rgba(0, 102, 204, 0.08)' : 'rgba(0, 102, 204, 0.08)'};
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
  }

  em {
    font-style: italic;
    opacity: 0.85;
  }

  a {
    color: ${props => props.isUser ? '#0066cc' : '#0066cc'};
    text-decoration: underline;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
    &:hover {
      text-decoration: none;
      background: ${props => props.isUser ? 'rgba(0, 102, 204, 0.08)' : 'rgba(0, 102, 204, 0.08)'};
      border-radius: 2px;
    }
  }

  &:hover {
    box-shadow: ${props => props.isUser ? 
      '0 4px 8px rgba(0, 102, 204, 0.15)' : 
      '0 4px 12px rgba(0, 0, 0, 0.08)'};
  }
`;

const ActionCard = styled.div`
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  margin: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    border-color: #0066cc;
  }

  .action-content {
    flex: 1;
  }

  .action-title {
    font-weight: 600;
    color: #0066cc;
    margin-bottom: 6px;
    font-size: 1.05rem;
  }

  .action-description {
    font-size: 0.925rem;
    color: #4b5563;
    line-height: 1.5;
  }

  .action-icon {
    width: 48px;
    height: 48px;
    background: #f0f7ff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0066cc;
    flex-shrink: 0;
    transition: all 0.2s ease;
  }

  &:hover .action-icon {
    background: #0066cc;
    color: #ffffff;
    transform: scale(1.05);
  }

  .action-arrow {
    color: #94a3b8;
    transition: transform 0.2s ease;
  }

  &:hover .action-arrow {
    transform: translateX(4px);
    color: #0066cc;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 10px;
  margin: 16px;
  flex-wrap: wrap;
`;

const ActionButton = styled(Button)`
  flex: 1;
  min-width: 140px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 10px;
  transition: all 0.2s ease;
  font-size: 0.95rem;
  height: auto;
  line-height: 1.4;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  svg {
    width: 18px;
    height: 18px;
  }

  &[data-variant='primary'] {
    background: #0066cc;
    color: white;
    border: none;
    
    &:hover {
      background: #0052a3;
    }
  }

  &[data-variant='secondary'] {
    background: #f0f7ff;
    color: #0066cc;
    border: 1px solid #0066cc;
    
    &:hover {
      background: #e1f0ff;
    }
  }
`;

const HighlightText = styled.span`
  background: #f0f7ff;
  color: #007AFF;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
`;

const LoadingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: #666;
  font-style: italic;
  
  @keyframes pulse {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
  }
  
  animation: pulse 1.5s infinite;
`;

const QuickActionButton = styled(Button)`
  background: #f8f9fa;
  color: #1a1a1a;
  border: 1px solid #e0e0e0;
  padding: 8px 12px;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex: 1;
  justify-content: center;
  min-width: 0;

  &:hover {
    background: #0066cc;
    color: #ffffff;
    border-color: #0066cc;
  }

  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
`;

// Add these styled component definitions after the MessageBubble component
const QuickActionBar = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
`;

const QuickActionChip = styled.button`
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  background: #f0f7ff;
  color: #0066cc;
  font-size: 0.875rem;
  border: 1px solid #0066cc;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: #0066cc;
    color: #ffffff;
    transform: translateY(-1px);
  }

  svg {
    width: 14px;
    height: 14px;
  }
`;

// Debug logger
const debug = {
  log: (context: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[AiChat] ${context}:`, data);
    }
  },
  error: (context: string, error: any) => {
    console.error(`[AiChat] Error in ${context}:`, error);
    if (error?.response) {
      console.error('Response:', error.response);
    }
  }
};

interface Message {
  role: "user" | "assistant";
  content: string;
  suggestedProperties?: PropertyWithRates[];
  location?: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
  actions?: Array<{
    type: 'location' | 'property' | 'search' | 'filter' | 'info';
    label: string;
    data: any;
    icon?: React.ReactNode;
  }>;
}

// Helper function to convert API property to schema property
const convertToSchemaProperty = (apiProperty: any): PropertyWithRates => {
  debug.log('Converting property', apiProperty);
  
  try {
    return {
      ...apiProperty,
      latitude: typeof apiProperty.latitude === 'string' ? parseFloat(apiProperty.latitude) : apiProperty.latitude,
      longitude: typeof apiProperty.longitude === 'string' ? parseFloat(apiProperty.longitude) : apiProperty.longitude,
      basePrice: typeof apiProperty.basePrice === 'string' ? parseFloat(apiProperty.basePrice) : apiProperty.basePrice,
      currency: apiProperty.currency || 'USD',
      // Ensure all required fields are present
      rates: apiProperty.rates || [],
      amenities: apiProperty.amenities || [],
      images: apiProperty.images || [],
      reviews: apiProperty.reviews || [],
      lastUpdated: new Date(apiProperty.lastUpdated || Date.now())
    };
  } catch (error) {
    debug.error('Property conversion failed', { error, property: apiProperty });
    throw new Error('Failed to convert property data');
  }
};

interface AiChatProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    rooms?: string | null;
    properties?: PropertyWithRates[];
    filters?: {
      propertyTypes: string[];
      amenities: string[];
      priceRange: [number, number];
      minRating: number;
    };
  };
  variant?: 'integrated' | 'floating' | 'modal';
  onClose?: () => void;
}

interface ConversationState {
  location?: {
    name: string;
    lat?: number;
    lng?: number;
    placeType?: string;
  };
  dates?: {
    checkIn: string;
    checkOut: string;
  };
  preferences?: {
    budget?: number;
    amenities?: string[];
    propertyTypes?: string[];
    guestCount?: number;
    roomCount?: number;
    travelPurpose?: string;
    priceRange?: [number, number];
    rating?: number;
    features?: string[];
  };
  lastRecommendations?: string[];
  searchHistory?: Array<{
    query: string;
    timestamp: number;
    results?: string[];
  }>;
  currentSearch?: {
    filters: {
      propertyTypes?: string[];
      amenities?: string[];
      priceRange?: [number, number];
      rating?: number;
    };
    sortBy?: string;
    page?: number;
  };
}

/**
 * AI Chat component for property search assistance
 */
export default function AiChat({ context, variant = 'floating', onClose }: AiChatProps) {
  const { toast } = useToast();
  // Enhanced conversation state initialization
  const [conversationState, setConversationState] = useState<ConversationState>(() => ({
    location: context?.location ? { name: context.location } : undefined,
    dates: context?.checkIn && context?.checkOut ? {
      checkIn: context.checkIn,
      checkOut: context.checkOut
    } : undefined,
    preferences: {
      guestCount: context?.guests ? parseInt(context.guests) : undefined,
      roomCount: context?.rooms ? parseInt(context.rooms) : undefined,
      propertyTypes: context?.filters?.propertyTypes || [],
      amenities: context?.filters?.amenities || [],
      priceRange: context?.filters?.priceRange,
      rating: context?.filters?.minRating
    },
    currentSearch: context?.filters ? {
      filters: {
        propertyTypes: context.filters.propertyTypes,
        amenities: context.filters.amenities,
        priceRange: context.filters.priceRange,
        rating: context.filters.minRating
      }
    } : undefined,
    searchHistory: []
  }));

  // Load conversation state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('conversationState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState) as ConversationState;
        setConversationState(currentState => ({
          ...currentState,
          ...parsedState
        }));
      } catch (e) {
        console.error('Failed to parse saved conversation state:', e);
      }
    }
  }, []);

  // Function to update conversation state with search context
  const updateSearchContext = useCallback((newContext: Partial<ConversationState>) => {
    setConversationState(currentState => {
      const updated = {
        ...currentState,
        ...newContext,
        searchHistory: [
          ...(currentState.searchHistory || []),
          {
            query: newContext.location?.name || '',
            timestamp: Date.now(),
            results: newContext.lastRecommendations
          }
        ].slice(-5) // Keep last 5 searches
      };

      // Store in localStorage for persistence
      localStorage.setItem('conversationState', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Load saved messages from localStorage on initial render
  const [messages, setMessages] = useState<Message[]>(() => {
    const saved = localStorage.getItem('chatHistory');
    const savedMessages = saved ? JSON.parse(saved) : [];
    
    // Only add welcome message if no saved messages
    if (savedMessages.length === 0) {
      return [{
        role: "assistant",
        content: context 
          ? `👋 Hi! I'm here to help you find the perfect stay${
              context.location ? ` in ${context.location}` : ''
            }${
              context.checkIn && context.checkOut 
                ? ` from ${new Date(context.checkIn).toLocaleDateString()} to ${new Date(context.checkOut).toLocaleDateString()}` 
                : ''
            }. I can recommend properties, compare options, and help you find exactly what you're looking for.${
              context.properties && context.properties.length > 0
                ? "\n\nI see some great options available. Would you like me to recommend the best ones based on your needs?"
                : "\n\nJust let me know what you're looking for!"
            }`
          : "👋 Hi! I'm your RoomLamAI assistant. I can help you find the perfect place to stay, compare options, and make sure you get the best value. What kind of stay are you looking for?"
      }];
    }
    return savedMessages;
  });

  // Handle initial user message if present
  useEffect(() => {
    const saved = localStorage.getItem('chatHistory');
    if (saved) {
      const savedMessages = JSON.parse(saved);
      // If there's exactly one message and it's from the user, trigger AI response
      if (savedMessages.length === 1 && savedMessages[0].role === 'user') {
        sendMessage.mutate(savedMessages[0].content);
      }
    }
  }, []); // Run once on mount

  // Save messages to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('chatHistory', JSON.stringify(messages.slice(-20))); // Keep last 20 messages
  }, [messages]);

  // Add a clear chat function
  const clearChat = () => {
    setMessages([{
      role: "assistant",
      content: "Chat history cleared. How can I help you today?"
    }]);
  };

  const [input, setInput] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [_, setLocation] = useLocation();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { geocodeAddress, isLoaded: isGoogleMapsLoaded } = useGooglePlaces();

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Get or generate a session ID from localStorage that persists across browser sessions
  const [sessionId, setSessionId] = useState<string>(() => {
    const savedSessionId = localStorage.getItem('booking_session_id');
    if (savedSessionId) return savedSessionId;
    
    const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    localStorage.setItem('booking_session_id', newSessionId);
    return newSessionId;
  });

  const sendMessage = useMutation({
    mutationFn: async (message: string) => {
      if (!message?.trim()) {
        throw new Error("Message cannot be empty");
      }

      debug.log('Sending message', { message, context, conversationState, sessionId });
      setIsStreaming(true);
      
      try {
        // Gather comprehensive context from the current state
        const enhancedContext = {
          // Basic information from props
          ...context,
          
          // Location information
          location: context?.location || (conversationState.location ? {
            name: conversationState.location.name,
            latitude: conversationState.location.lat,
            longitude: conversationState.location.lng
          } : undefined),
          
          // Date range information
          dateRange: context?.checkIn && context?.checkOut 
            ? { checkIn: context.checkIn, checkOut: context.checkOut }
            : conversationState.dates,
          
          // Guest and room information
          guests: context?.guests || conversationState.preferences?.guestCount,
          rooms: context?.rooms,
          
          // User preferences
          preferences: {
            amenities: conversationState.preferences?.amenities || [],
            propertyTypes: conversationState.preferences?.propertyTypes || [],
            priceRange: conversationState.preferences?.priceRange,
            guestCount: conversationState.preferences?.guestCount,
            travelPurpose: conversationState.preferences?.travelPurpose
          },
          
          // Available properties for AI to recommend
          properties: context?.properties || []
        };
      
        const response = await fetch("/api/chat", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ 
            message,
            context: enhancedContext,
            sessionId,
            extractLocation: true // Enable location extraction in AI responses
          }),
        });

        if (!response.ok) {
          const errorData = await response.text();
          throw new Error(`API request failed: ${response.status} ${errorData}`);
        }

        if (!response.body) {
          throw new Error("Response body is empty");
        }

        // Reset retry count on successful request
        setRetryCount(0);

        // Read the stream
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let currentMessage = "";
        let currentProperties: PropertyWithRates[] | undefined;

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            // Process the stream data
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(5);
                // Skip if it's a DONE message
                if (data === '[DONE]' || data === 'DONE') continue;

                try {
                  // Enhanced filtering for non-JSON data and log messages
                  // First, check for the most common log message patterns
                  if (data.includes('Initialize') || 
                      data.includes('Added search') || 
                      data.includes('Recorded property') ||
                      data.includes('Updated filters') ||
                      data.includes('Added property to comparison') ||
                      data.includes('Recorded booking attempt') ||
                      data.includes('Updated conversation context') ||
                      data.includes('Added message')) {
                    debug.log('Skipping log message', data);
                    continue;
                  }

                  // Then check if it's properly formatted JSON (should start with { or [)
                  if (!/^[\[\{]/.test(data.trim())) {
                    debug.log('Skipping non-JSON format data', data);
                    continue;
                  }
                  
                  // Additional validation to catch malformed JSON that might pass the basic check
                  try {
                    JSON.parse(data);
                  } catch (error) {
                    debug.log('Invalid JSON data detected', data);
                    continue;
                  }

                  const parsed = JSON.parse(data);
                  debug.log('Parsed response', parsed);

                  if (parsed.type === 'text') {
                    // Only append non-empty text
                    if (parsed.data?.trim()) {
                      currentMessage += parsed.data;
                      // Update the message in real-time
                      setMessages(prev => {
                        const updated = [...prev];
                        const lastMessage = updated[updated.length - 1];
                        if (lastMessage && lastMessage.role === 'assistant') {
                          lastMessage.content = currentMessage;
                          return [...updated];
                        } else {
                          return [...updated, { role: 'assistant', content: currentMessage }];
                        }
                      });
                    }
                  } else if (parsed.type === 'properties' && Array.isArray(parsed.data)) {
                    try {
                      currentProperties = parsed.data.map((p: any) => convertToSchemaProperty(p));
                      
                      // Only update if we have valid properties
                      if (currentProperties && currentProperties.length > 0) {
                        setMessages(prev => {
                          const updated = [...prev];
                          const lastMessage = updated[updated.length - 1];
                          if (lastMessage && lastMessage.role === 'assistant') {
                            lastMessage.suggestedProperties = currentProperties;
                            return [...updated];
                          }
                          return updated;
                        });

                        // If there are multiple properties suggested, navigate to search results
                        if (currentProperties.length > 1) {
                          const ids = currentProperties.map(p => p.id).join(',');
                          setLocation(`/results?recommended=${ids}`);
                        }
                      }
                    } catch (error) {
                      debug.error('Property processing failed', error);
                    }
                  } else if (parsed.type === 'action') {
                    setMessages(prev => {
                      const updated = [...prev];
                      const lastMessage = updated[updated.length - 1];
                      if (lastMessage && lastMessage.role === 'assistant') {
                        lastMessage.actions = [...(lastMessage.actions || []), parsed.data];
                        return [...updated];
                      }
                      return updated;
                    });
                  } else if (parsed.type === 'location' && parsed.data) {
                    // Enhanced validation for location data
                    const locationData = parsed.data;
                    if (locationData && 
                        typeof locationData === 'object' &&
                        'name' in locationData && 
                        'lat' in locationData && 
                        'lng' in locationData) {
                        
                      // Update the conversation context with the location
                      updateSearchContext({
                        location: {
                          name: locationData.name,
                          lat: locationData.lat,
                          lng: locationData.lng,
                          placeType: locationData.placeType || 'unknown'
                        }
                      });
                      
                      // Add a location marker to the current assistant message
                      setMessages(prev => {
                        const updated = [...prev];
                        const lastMessage = updated[updated.length - 1];
                        if (lastMessage && lastMessage.role === 'assistant') {
                          lastMessage.location = {
                            name: locationData.name,
                            lat: locationData.lat,
                            lng: locationData.lng,
                            placeType: locationData.placeType
                          };
                          return [...updated];
                        }
                        return updated;
                      });
                      
                      debug.log('Location detected', locationData);
                    } else {
                      debug.error('Invalid location data', parsed);
                    }
                  } else if (parsed.type === 'property') {
                    updateSearchContext({
                      lastRecommendations: [...(conversationState.lastRecommendations || []), parsed.data.id]
                    });
                  } else if (parsed.type === 'preferences') {
                    updateSearchContext({
                      preferences: {
                        ...conversationState.preferences,
                        ...parsed.data
                      }
                    });
                  } else if (parsed.type === 'filters') {
                    updateSearchContext({
                      currentSearch: {
                        filters: {
                          ...(conversationState.currentSearch?.filters || {}),
                          ...parsed.data
                        }
                      }
                    });
                  }
                } catch (e) {
                  debug.error('Stream chunk parsing failed', e);
                  // Log the data that caused the error and continue processing
                  // This allows the stream to continue even if one message fails to parse
                  debug.log('Problem data that failed to parse:', data);
                  
                  // If it's likely a status message, we can safely ignore it
                  if (typeof data === 'string' && 
                     (data.includes('Recorded') || 
                      data.includes('Added') || 
                      data.includes('Updated') || 
                      data.includes('Initialize'))) {
                    debug.log('Ignoring status message in stream');
                  } else {
                    // For other parsing errors, log a more detailed error
                    console.warn('JSON parse error in chat stream. Data:', data.substring(0, 100) + (data.length > 100 ? '...' : ''));
                  }
                }
              }
            }
          }
        } catch (error) {
          debug.error('Stream processing failed', error);
          throw error;
        }

        return { content: currentMessage, suggestedProperties: currentProperties };
      } catch (error) {
        debug.error('Message processing failed', error);
        throw error;
      } finally {
        setIsStreaming(false);
      }
    },
    onError: (error) => {
      debug.error('Chat mutation error', error);
      
      // Increment retry count
      setRetryCount(prev => prev + 1);
      
      // Different error messages based on retry count
      const errorMessage = retryCount >= 2
        ? "I'm having persistent trouble connecting. Please try again later or contact support if this continues."
        : retryCount === 1
        ? "Still having trouble. Let me try one more time..."
        : "Sorry, something went wrong. I'll try again...";
      
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: errorMessage
      }]);

      // Auto-retry for first two failures
      if (retryCount < 2) {
        setTimeout(() => {
          sendMessage.mutate(input);
        }, 1000);
      }
      
      setIsStreaming(false);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isStreaming) return;

    // Reset retry count on new message
    setRetryCount(0);
    setMessages(prev => [...prev, { role: "user", content: input }]);
    sendMessage.mutate(input);
    setInput("");
  };

  const renderMessage = (message: Message, index: number) => {
    const isUser = message.role === "user";

    // Process the content to remove metadata tags and create interactive elements
    let processedContent = message.content;
    const actions: Array<{
      type: 'location' | 'property' | 'search';
      label: string;
      data: any;
    }> = [];

    // Extract and remove action tags
    processedContent = processedContent.replace(
      /\[ACTION:(\w+)\|(.*?)\|(.*?)\]/g,
      (match, type, label, dataStr) => {
        try {
          const data = JSON.parse(dataStr);
          actions.push({ type: type.toLowerCase() as any, label, data });
        } catch (e) {
          console.error('Failed to parse action data:', e);
        }
        return ''; // Remove the tag from displayed text
      }
    );

    // Extract and remove property recommendations
    processedContent = processedContent.replace(
      /\[PROPERTIES_START\](.*?)\[PROPERTIES_END\]/g,
      ''
    );

    // Format markdown content with enhanced property, location, and formatting
    let formattedContent = processedContent
      .trim()
      .split('\n')
      .map(line => line.trim())
      .filter(line => line)
      .join('\n\n');
    
    // Add interactive property links - Match "Hotel ID XXX" or "Property ID XXX" patterns
    formattedContent = formattedContent.replace(
      /Hotel ID (\d+):? ([^.,:;!?]+)|Hotel ID (\d+)|Property ID (\d+)/g, 
      (match, id1, name, id2, id3) => {
        const propertyId = id1 || id2 || id3;
        const propertyName = name || `Property ${propertyId}`;
        return `<a href="#" class="property-link" data-property-id="${propertyId}">${propertyName}</a>`;
      }
    );
    
    // Process location mentions - Match city, state or city names followed by FL, NY, etc.
    formattedContent = formattedContent.replace(
      /([A-Z][a-z]+ ?[A-Z]?[a-z]*(, )?([A-Z]{2})?)/g,
      (match, location) => {
        // Skip if it's inside a link already
        if (formattedContent.indexOf(`<a`) > -1 && 
            formattedContent.indexOf(`<a`) < formattedContent.indexOf(match) && 
            formattedContent.indexOf(`</a>`) > formattedContent.indexOf(match)) {
          return match;
        }
        
        // Check if it looks like a real location (avoid false positives)
        if (/(New York|Orlando|Miami|Las Vegas|Chicago|Boston|San Francisco|Los Angeles|Seattle|Houston|Dallas|Atlanta|Denver|Washington|Philadelphia|San Diego|Austin|Nashville)/i.test(match)) {
          return `<a href="#" class="location-link" data-location="${match}">${match}</a>`;
        }
        return match;
      }
    );
    
    // Standard markdown formatting
    formattedContent = formattedContent
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n•\s(.*)/g, '<li>$1</li>')
      .replace(/<li>/g, '<ul><li>')
      .replace(/<\/li>\n/g, '</li></ul>');

    return (
      <div key={index} className="space-y-4 w-full">
        <MessageBubble isUser={isUser}>
          <div 
            className="prose prose-sm dark:prose-invert"
            dangerouslySetInnerHTML={{ 
              __html: `<p>${formattedContent}</p>` 
            }}
            onClick={(e) => {
              // Handle clicks on interactive elements
              const target = e.target as HTMLElement;
              
              // Property links
              if (target.classList.contains('property-link')) {
                e.preventDefault();
                const propertyId = target.getAttribute('data-property-id');
                if (propertyId) {
                  setLocation(`/property/${propertyId}`);
                }
              }
              
              // Location links
              if (target.classList.contains('location-link')) {
                e.preventDefault();
                const locationName = target.getAttribute('data-location');
                if (locationName) {
                  // First try to geocode the location to get coordinates
                  if (isGoogleMapsLoaded) {
                    try {
                      toast({
                        title: "Finding location...",
                        description: `Looking up "${locationName}" coordinates`,
                      });
                      
                      // Use Google Places geocoding for accurate coordinates
                      geocodeAddress(locationName)
                        .then(result => {
                          if (result) {
                            // Update with coordinates for more precise search
                            updateSearchContext({
                              location: {
                                name: locationName,
                                lat: result.lat,
                                lng: result.lng,
                                placeType: result.placeType
                              }
                            });
                            
                            // Navigate to search results with the coordinates
                            setLocation(`/results?lat=${result.lat}&lng=${result.lng}&locationName=${encodeURIComponent(locationName)}`);
                          } else {
                            // Fallback to text search if geocoding fails
                            updateSearchContext({
                              location: {
                                name: locationName
                              }
                            });
                            sendMessage.mutate(`Show me hotels in ${locationName}`);
                          }
                        })
                        .catch(err => {
                          console.error('Geocoding error:', err);
                          // Fallback to text search if geocoding fails
                          updateSearchContext({
                            location: {
                              name: locationName
                            }
                          });
                          sendMessage.mutate(`Show me hotels in ${locationName}`);
                        });
                    } catch (err) {
                      console.error('Geocoding error:', err);
                      // Fallback to text search
                      updateSearchContext({
                        location: {
                          name: locationName
                        }
                      });
                      sendMessage.mutate(`Show me hotels in ${locationName}`);
                    }
                  } else {
                    // Google Maps not loaded, fall back to text-based search
                    updateSearchContext({
                      location: {
                        name: locationName
                      }
                    });
                    sendMessage.mutate(`Show me hotels in ${locationName}`);
                  }
                }
              }
            }}
          />
        </MessageBubble>

        {/* Render action buttons if present */}
        {!isUser && actions.length > 0 && (
          <div className="px-4 space-y-3">
            {actions.map((action, actionIndex) => (
              <ActionCard
                key={actionIndex}
                onClick={() => handleActionClick(action)}
                role="button"
                tabIndex={0}
              >
                <div className="action-icon">
                  {action.type === 'location' && <MapPin className="w-6 h-6" />}
                  {action.type === 'property' && <Hotel className="w-6 h-6" />}
                  {action.type === 'search' && <SearchIcon className="w-6 h-6" />}
                </div>
                <div className="action-content">
                  <div className="action-title">{action.label}</div>
                  <div className="action-description">
                    {action.type === 'property' && 'View property details and availability'}
                    {action.type === 'location' && 'Explore properties in this area'}
                    {action.type === 'search' && 'View matching properties'}
                  </div>
                </div>
                <ChevronRight className="w-5 h-5 action-arrow" />
              </ActionCard>
            ))}
          </div>
        )}

        {/* Render property recommendations if present */}
        {!isUser && message.suggestedProperties && (
          <div className="px-4 space-y-4">
            <div className="text-sm font-medium text-muted-foreground mb-3">
              Recommended Properties:
            </div>
            {message.suggestedProperties.map((property) => (
              <PropertyCard
                key={property.id}
                property={property}
                checkIn={conversationState.dates?.checkIn ? new Date(conversationState.dates.checkIn) : undefined}
                checkOut={conversationState.dates?.checkOut ? new Date(conversationState.dates.checkOut) : undefined}
                guests={conversationState.preferences?.guestCount?.toString() || "2"}
                rooms={conversationState.preferences?.roomCount?.toString() || "1"}
                onClick={() => setLocation(`/property/${property.id}`)}
              />
            ))}
          </div>
        )}

        {/* Render location marker when location is detected */}
        {!isUser && (
          <>
            {/* Show location from message.location if available */}
            {message.location && (
              <div className="px-4 pt-2 pb-1">
                <LocationMarker 
                  location={{
                    name: message.location.name,
                    lat: message.location.lat,
                    lng: message.location.lng,
                    placeType: message.location.placeType
                  }}
                  onSearchNearby={() => {
                    if (message.location?.lat && message.location?.lng && message.location?.name) {
                      setLocation(`/results?lat=${message.location.lat}&lng=${message.location.lng}&locationName=${encodeURIComponent(message.location.name)}`);
                    }
                  }}
                  className="max-w-full w-full mb-2"
                />
              </div>
            )}
            
            {/* Show location from conversation context as fallback */}
            {!message.location && conversationState.location?.name && conversationState.location?.lat && conversationState.location?.lng && (
              <div className="px-4 pt-2 pb-1">
                <LocationMarker 
                  location={{
                    name: conversationState.location.name,
                    lat: conversationState.location.lat,
                    lng: conversationState.location.lng,
                    placeType: conversationState.location.placeType
                  }}
                  onSearchNearby={() => {
                    if (conversationState.location?.lat && conversationState.location?.lng && conversationState.location?.name) {
                      setLocation(`/results?lat=${conversationState.location.lat}&lng=${conversationState.location.lng}&locationName=${encodeURIComponent(conversationState.location.name)}`);
                    }
                  }}
                  className="max-w-full w-full mb-2"
                />
              </div>
            )}
          </>
        )}

        {/* Render suggested quick replies for assistant messages */}
        {!isUser && !isStreaming && index === messages.length - 1 && (
          <QuickActionBar className="px-4 pt-3">
            {message.suggestedProperties ? (
              <>
                <QuickActionChip onClick={() => handleQuickAction("tell_more")}>
                  <Info className="w-4 h-4" />
                  Tell me more
                </QuickActionChip>
                <QuickActionChip onClick={() => handleQuickAction("compare")}>
                  <ListFilter className="w-4 h-4" />
                  Compare these
                </QuickActionChip>
                <QuickActionChip onClick={() => handleQuickAction("book")}>
                  <Calendar className="w-4 h-4" />
                  Check availability
                </QuickActionChip>
              </>
            ) : conversationState.location?.name ? (
              <>
                <QuickActionChip onClick={() => handleQuickAction("explore_area")}>
                  <MapIcon className="w-4 h-4" />
                  About this area
                </QuickActionChip>
                <QuickActionChip onClick={() => handleQuickAction("nearby_attractions")}>
                  <Landmark className="w-4 h-4" />
                  Nearby attractions
                </QuickActionChip>
                <QuickActionChip onClick={() => handleQuickAction(message.content.toLowerCase().includes("luxury") ? "budget_options" : "luxury_options")}>
                  {message.content.toLowerCase().includes("luxury") ? (
                    <><DollarSign className="w-4 h-4" />Budget options</>
                  ) : (
                    <><Star className="w-4 h-4" />Luxury options</>
                  )}
                </QuickActionChip>
              </>
            ) : (
              <>
                <QuickActionChip onClick={() => setInput("I'm looking for hotels in New York")}>
                  <Building className="w-4 h-4" />
                  Hotels in New York
                </QuickActionChip>
                <QuickActionChip onClick={() => setInput("Find me a hotel with a pool and spa")}>
                  <Waves className="w-4 h-4" />
                  Hotels with pool & spa
                </QuickActionChip>
                <QuickActionChip onClick={() => setInput("What are the top-rated hotels in Miami?")}>
                  <Star className="w-4 h-4" />
                  Top-rated in Miami
                </QuickActionChip>
              </>
            )}
          </QuickActionBar>
        )}
      </div>
    );
  };

  const handleActionClick = (action: { type: 'location' | 'property' | 'search'; label: string; data: any; }) => {
    switch (action.type) {
      case 'location':
        setLocation(`/results?location=${encodeURIComponent(action.data.name)}&lat=${action.data.lat}&lng=${action.data.lng}`);
        break;
      case 'property':
        setLocation(`/property/${action.data.id}`);
        break;
      case 'search':
        setLocation(`/results?${new URLSearchParams(action.data).toString()}`);
        break;
    }
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case "tell_more":
        sendMessage.mutate("Can you tell me more about these options?");
        break;
      case "compare":
        sendMessage.mutate("Can you compare these properties for me?");
        break;
      case "book":
        if (conversationState.lastRecommendations?.length === 1) {
          setLocation(`/property/${conversationState.lastRecommendations[0]}`);
        } else {
          sendMessage.mutate("I'd like to check availability and rates for these properties.");
        }
        break;
      case "explore_area":
        sendMessage.mutate("What can you tell me about this area and its surroundings?");
        break;
      case "nearby_attractions":
        sendMessage.mutate("What are the popular attractions and points of interest nearby?");
        break;
      case "budget_options":
        sendMessage.mutate("Can you show me more budget-friendly options in this area?");
        break;
      case "luxury_options":
        sendMessage.mutate("What luxury accommodations are available here?");        break;
      case "check_availability":
        sendMessage.mutate("What are the available dates and rates for these properties?");
        break;
      case "show_amenities":
        sendMessage.mutate("Can you list all the amenities these properties offer?");
        break;
      case "filter_amenities":
        sendMessage.mutate("I'd like to filter properties by specific amenities. What options are available?");
        break;
    }
  };

  // Update the quickActions array to be dynamic based on conversation state
  const quickActions = useMemo(() => {
    const actions = [];
    
    // If location is set, add location-specific actions
    const locationName = conversationState?.location?.name;
    if (locationName) {
      actions.push({
        label: `Explore ${locationName}`,
        icon: <MapPin />,
        action: () => {
          sendMessage.mutate(`What are the best areas to stay in ${locationName}?`);
        }
      });
    }
    
    // If price preferences are set, add budget-related actions
    if (conversationState.preferences?.priceRange) {
      const [min, max] = conversationState.preferences.priceRange;
      actions.push({
        label: `${min}-${max} range`,
        icon: <DollarSign />,
        action: () => {
          sendMessage.mutate(`Show me properties between $${min} and $${max} per night`);
        }
      });
    }
    
    // If amenities are set, add amenity-related actions
    if (conversationState.preferences?.amenities?.length) {
      actions.push({
        label: "Filter amenities",
        icon: <Hotel />,
        action: () => {
          sendMessage.mutate("Show properties with my preferred amenities");
        }
      });
    }
    
    // Add default actions if we have less than 3 contextual actions
    if (actions.length < 3) {
      if (!actions.find(a => a.label.includes("luxury"))) {
        actions.push({
          label: "Luxury stays",
          icon: <Hotel />,
          action: () => {
            sendMessage.mutate("Find me luxury accommodations");
          }
        });
      }
      
      if (!actions.find(a => a.label.includes("family"))) {
        actions.push({
          label: "Family-friendly",
          icon: <Star />,
          action: () => {
            sendMessage.mutate("What are the best family-friendly options?");
          }
        });
      }
      
      if (!actions.find(a => a.label.includes("budget"))) {
        actions.push({
          label: "Budget options",
          icon: <DollarSign />,
          action: () => {
            sendMessage.mutate("Show me affordable options");
          }
        });
      }
    }
    
    return actions.slice(0, 3); // Limit to 3 actions
  }, [conversationState, sendMessage]);

  return (
    <ChatContainer variant={variant}>
      <CardHeader className={cn(
        variant === 'integrated' ? 'pb-4' : 'pb-2',
        variant === 'modal' && 'border-b'
      )}>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="w-5 h-5" />
          RoomLamAI Agent
          {(variant === 'modal' || variant === 'floating') && (
            <div className="ml-auto flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearChat}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear Chat
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={variant === 'modal' ? onClose : () => setMessages([])}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}
        </CardTitle>
        {(variant === 'integrated' || variant === 'modal') && (
          <p className="text-sm text-gray-500 mt-2">
            Experience a smarter way to find accommodations. Our AI understands your preferences and curates the perfect options tailored just for you.
          </p>
        )}
      </CardHeader>
      
      <CardContent className={cn(
        "flex-grow overflow-hidden",
        variant === 'modal' ? 'p-6' : variant === 'integrated' ? 'p-6' : 'p-3'
      )}>
        <ScrollArea className="h-full pr-2" ref={scrollAreaRef}>
          {messages.map((message, index) => renderMessage(message, index))}
          {isStreaming && (
            <LoadingIndicator>
              <div className="animate-spin">⌛</div>
              Analyzing your request...
            </LoadingIndicator>
          )}
        </ScrollArea>
      </CardContent>
      
      <CardFooter className={cn(
        "flex flex-col gap-3",
        variant === 'modal' ? 'p-6 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60' : 
        variant === 'integrated' ? 'pt-4 pb-6' : 'pt-2'
      )}>
        <div className="flex gap-2 w-full overflow-x-auto pb-1 no-scrollbar">
          {quickActions.map((action, index) => (
            <QuickActionButton
              key={index}
              variant="ghost"
              size="sm"
              onClick={action.action}
              disabled={isStreaming}
            >
              {action.icon}
              {action.label}
            </QuickActionButton>
          ))}
        </div>
        
        <form onSubmit={handleSubmit} className="flex w-full gap-2">
          <Input
            placeholder="Ask me anything about your travel plans..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            disabled={isStreaming}
            className={cn(
              "flex-grow",
              variant === 'modal' ? 'text-lg' : variant === 'integrated' ? 'text-lg' : ''
            )}
          />
          <Button 
            type="submit" 
            disabled={isStreaming || !input.trim()} 
            className={variant === 'modal' || variant === 'integrated' ? 'px-6' : 'px-3'}
          >
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </CardFooter>
    </ChatContainer>
  );
}