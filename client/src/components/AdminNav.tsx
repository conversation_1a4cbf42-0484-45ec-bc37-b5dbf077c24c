import React from "react";
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

export default function AdminNav() {
  const [location] = useLocation();

  const navItems = [
    {
      name: "Dashboard",
      href: "/admin",
      active: location === "/admin",
    },
    {
      name: "Users",
      href: "/admin/users",
      active: location === "/admin/users",
    },
    {
      name: "Promo Codes",
      href: "/admin/promo-codes",
      active: location === "/admin/promo-codes",
    },
    {
      name: "Analytics",
      href: "/admin/analytics",
      active: location === "/admin/analytics",
    },
  ];

  return (
    <div className="border-b pb-2">
      <nav className="flex space-x-6">
        {navItems.map((item) => (
          <Link key={item.href} href={item.href}>
            <a
              className={cn(
                "font-medium transition-colors hover:text-primary px-1 py-2",
                item.active
                  ? "text-primary border-b-2 border-primary"
                  : "text-muted-foreground"
              )}
            >
              {item.name}
            </a>
          </Link>
        ))}
      </nav>
    </div>
  );
}