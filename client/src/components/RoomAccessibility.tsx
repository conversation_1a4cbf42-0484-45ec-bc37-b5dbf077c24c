import { ExtendedRoom } from '@/types/schema.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import {
  Wheelchair,
  Heart,
  Elevator,
  HandMetal,
  Bath,
  BedDouble,
} from 'lucide-react';

interface RoomAccessibilityProps {
  room: ExtendedRoom;
}

const ACCESSIBILITY_FEATURES = {
  'Mobility': [
    'Wheelchair Accessible',
    'Roll-in Shower',
    'Grab Bars',
    'Elevator Access',
    'Wide Doorways',
    'Accessible Height Bed'
  ],
  'Visual': [
    'Braille Signage',
    'Audio Descriptions',
    'High Contrast Fixtures',
    'Guide Dog Friendly'
  ],
  'Hearing': [
    'Visual Alarms',
    'TTY/TDD Compatible',
    'Closed Captioning TV'
  ],
  'Other': [] as string[]
};

export default function RoomAccessibility({ room }: RoomAccessibilityProps) {
  if (!room.accessibility) return null;

  // Parse accessibility features
  const features = typeof room.accessibility === 'string' 
    ? room.accessibility.split(',').map(f => f.trim())
    : Array.isArray(room.accessibility) 
      ? room.accessibility 
      : [];

  // Categorize features
  const categorizedFeatures = features.reduce((acc, feature) => {
    let placed = false;
    for (const [category, items] of Object.entries(ACCESSIBILITY_FEATURES)) {
      if (items.includes(feature)) {
        if (!acc[category]) acc[category] = [];
        acc[category].push(feature);
        placed = true;
        break;
      }
    }
    if (!placed) {
      if (!acc['Other']) acc['Other'] = [];
      acc['Other'].push(feature);
    }
    return acc;
  }, {} as Record<string, string[]>);

  const getFeatureIcon = (feature: string) => {
    switch (feature) {
      case 'Wheelchair Accessible':
        return <Wheelchair className="h-4 w-4" />;
      case 'Medical Assistance':
        return <Heart className="h-4 w-4" />;
      case 'Elevator Access':
        return <Elevator className="h-4 w-4" />;
      case 'Grab Bars':
        return <HandMetal className="h-4 w-4" />;
      case 'Roll-in Shower':
        return <Bath className="h-4 w-4" />;
      case 'Accessible Height Bed':
        return <BedDouble className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Accessibility Features</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(categorizedFeatures).map(([category, features]) => (
            <div key={category}>
              <h4 className="font-medium mb-3">{category}</h4>
              <div className="grid gap-3">
                {features.map((feature) => {
                  const icon = getFeatureIcon(feature);
                  return (
                    <div key={feature} className="flex items-center gap-2">
                      {icon}
                      <span className="text-sm">{feature}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {room.accessibilityNotes && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-2">Additional Notes</h4>
            <p className="text-sm text-muted-foreground">
              {room.accessibilityNotes}
            </p>
          </div>
        )}

        <div className="mt-6 pt-6 border-t">
          <h4 className="font-medium mb-3">Assistance</h4>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• 24/7 front desk assistance available</p>
            <p>• Staff trained in accessibility awareness</p>
            <p>• Special assistance can be arranged upon request</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 