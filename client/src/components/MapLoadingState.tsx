import React from 'react';
import { Loader2 } from 'lucide-react';

const MapLoadingState: React.FC = () => {
  return (
    <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center">
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="text-sm font-medium">Loading map...</p>
      </div>
    </div>
  );
};

export default MapLoadingState; 