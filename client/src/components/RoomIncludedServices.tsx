import { ExtendedRoom } from '@/types/schema.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { Check } from 'lucide-react';

interface RoomIncludedServicesProps {
  room: ExtendedRoom;
}

export default function RoomIncludedServices({ room }: RoomIncludedServicesProps) {
  if (!room.includedServices?.length) return null;

  // Group services by category (if available)
  const groupedServices = room.includedServices.reduce((acc, service) => {
    const category = service.includes(':') ? service.split(':')[0].trim() : 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(service.includes(':') ? service.split(':')[1].trim() : service);
    return acc;
  }, {} as Record<string, string[]>);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Included Services</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(groupedServices).map(([category, services]) => (
            <div key={category}>
              <h4 className="font-medium mb-3">{category}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {services.map((service, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">{service}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {room.fees?.some(fee => fee.included) && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-3">Included Fees & Charges</h4>
            <div className="space-y-2">
              {room.fees
                .filter(fee => fee.included)
                .map(fee => (
                  <div key={fee.type} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">{fee.type}</span>
                  </div>
                ))}
            </div>
          </div>
        )}

        {room.promotions && room.promotions.length > 0 && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-3">Special Offers</h4>
            <div className="flex flex-wrap gap-2">
              {room.promotions.map(promo => (
                <Badge key={promo.code} variant="secondary">
                  {promo.description}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 