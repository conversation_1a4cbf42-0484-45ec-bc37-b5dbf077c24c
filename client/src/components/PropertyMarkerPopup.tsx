import React from 'react';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PropertyWithRates, PropertyImage } from "@/types/schema";
import { getImageSource } from "@/utils/imageUtils";
import { formatCurrency } from "@/lib/utils";

interface PropertyMarkerPopupProps {
  property: PropertyWithRates;
  onViewDetails: () => void;
}

const PropertyMarkerPopup: React.FC<PropertyMarkerPopupProps> = ({
  property,
  onViewDetails,
}) => {
  // Get the search params to calculate number of nights
  const searchParams = new URLSearchParams(window.location.search);
  const checkIn = searchParams.get('checkIn') ? new Date(searchParams.get('checkIn')!) : null;
  const checkOut = searchParams.get('checkOut') ? new Date(searchParams.get('checkOut')!) : null;
  const nights = checkIn && checkOut ? 
    Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24)) : 
    1;

  return (
    <Card className="w-72 p-3 shadow-lg">
      <div className="flex gap-3">
        <div className="w-20 h-20 flex-shrink-0">
          <img
            src={getImageSource(property.images?.[0])}
            alt={property.name}
            className="w-full h-full object-cover rounded"
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-sm mb-1 truncate">{property.name}</h3>
          {property.rating && (
            <div className="flex items-center gap-1 mb-1">
              <span className="text-sm">{Number(property.rating).toFixed(1)}★</span>
              {property.reviewCount && (
                <span className="text-xs text-muted-foreground">
                  ({property.reviewCount})
                </span>
              )}
            </div>
          )}
          {property.rates?.[0] && (
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                {property.rates[0].discountPercent > 0 && (
                  <span className="text-xs line-through text-muted-foreground">
                    {formatCurrency(property.rates[0].originalAmount, property.rates[0].currency)}
                  </span>
                )}
                <span className="text-sm font-medium">
                  {formatCurrency(property.rates[0].totalAmount, property.rates[0].currency)}
                  <span className="text-muted-foreground font-normal"> total</span>
                </span>
              </div>
              {property.rates[0].discountPercent > 0 && (
                <div className="flex items-center gap-1">
                  <span className="text-xs text-red-500 font-medium">
                    Save {property.rates[0].discountPercent}%
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <Button 
        className="w-full mt-3" 
        size="sm"
        onClick={onViewDetails}
      >
        View Details
      </Button>
    </Card>
  );
};

export default PropertyMarkerPopup; 