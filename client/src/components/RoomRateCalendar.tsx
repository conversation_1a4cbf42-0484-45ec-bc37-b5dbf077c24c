import { ExtendedRoom } from '@/types/schema.js';
import { useState, useEffect } from 'react';
import { Calendar } from './ui/calendar.jsx';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { formatCurrency } from '@/lib/utils.jsx';

interface RoomRateCalendarProps {
  room: ExtendedRoom;
  onDateSelect?: (dates: { from: Date; to: Date }) => void;
}

interface DailyRate {
  date: Date;
  rate: number;
  available: boolean;
  restrictedRate?: boolean;
  promotion?: boolean;
}

export default function RoomRateCalendar({
  room,
  onDateSelect
}: RoomRateCalendarProps) {
  const [selectedDates, setSelectedDates] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined
  });

  const [dailyRates, setDailyRates] = useState<DailyRate[]>([]);

  // Simulate daily rates for the next 30 days
  useEffect(() => {
    const rates: DailyRate[] = [];
    const today = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      // Simulate some rate variations
      const baseRate = room.rate;
      const variation = Math.random() * 0.2 - 0.1; // -10% to +10%
      const dailyRate = Math.round(baseRate * (1 + variation));
      
      rates.push({
        date,
        rate: dailyRate,
        available: Math.random() > 0.1, // 90% availability
        restrictedRate: Math.random() > 0.8,
        promotion: Math.random() > 0.7
      });
    }
    
    setDailyRates(rates);
  }, [room.rate]);

  const handleSelect = (dates: { from: Date | undefined; to: Date | undefined }) => {
    setSelectedDates(dates);
    if (dates.from && dates.to && onDateSelect) {
      onDateSelect(dates as { from: Date; to: Date });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Rate Calendar</CardTitle>
      </CardHeader>
      <CardContent>
        <Calendar
          mode="range"
          selected={selectedDates}
          onSelect={handleSelect}
          numberOfMonths={2}
          disabled={{ before: new Date() }}
          modifiers={{
            promotion: dailyRates
              .filter(dr => dr.promotion)
              .map(dr => dr.date),
            restricted: dailyRates
              .filter(dr => dr.restrictedRate)
              .map(dr => dr.date),
            unavailable: dailyRates
              .filter(dr => !dr.available)
              .map(dr => dr.date)
          }}
          modifiersStyles={{
            promotion: { backgroundColor: '#fee2e2' },
            restricted: { backgroundColor: '#fef3c7' },
            unavailable: { backgroundColor: '#f3f4f6', color: '#9ca3af' }
          }}
          className="rounded-md border"
        />

        <div className="mt-4 space-y-4">
          <div className="flex gap-2">
            <Badge variant="outline" className="bg-red-50">
              Promotional Rate
            </Badge>
            <Badge variant="outline" className="bg-amber-50">
              Restricted Rate
            </Badge>
            <Badge variant="outline" className="bg-gray-100">
              Unavailable
            </Badge>
          </div>

          {selectedDates.from && selectedDates.to && (
            <div className="space-y-2">
              <h4 className="font-medium">Selected Period Rates</h4>
              <div className="text-sm space-y-1">
                {dailyRates
                  .filter(dr => 
                    dr.date >= selectedDates.from! && 
                    dr.date <= selectedDates.to!
                  )
                  .map(dr => (
                    <div key={dr.date.toISOString()} className="flex justify-between">
                      <span>{dr.date.toLocaleDateString()}</span>
                      <div className="flex items-center gap-2">
                        <span>{formatCurrency(dr.rate, room.currency)}</span>
                        {dr.promotion && (
                          <Badge variant="destructive" className="text-xs">
                            Special Offer
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 