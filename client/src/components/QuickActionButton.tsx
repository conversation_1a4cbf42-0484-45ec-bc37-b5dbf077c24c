import * as React from "react";
import { cn } from "@/lib/utils";
import { Button, ButtonProps } from "@/components/ui/button";
import styled from '@emotion/styled';

interface QuickActionButtonProps extends ButtonProps {
  children: React.ReactNode;
}

// Styled button with hover effects and animation
const StyledButton = styled(Button)`
  background: #f8f9fa;
  color: #1a1a1a;
  border: 1px solid #e0e0e0;
  padding: 8px 12px;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex: 1;
  justify-content: center;
  min-width: 0;

  &:hover:not(:disabled) {
    background: #0066cc;
    color: #ffffff;
    border-color: #0066cc;
    transform: translateY(-1px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    background: #f1f1f1;
    cursor: not-allowed;
  }

  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
`;

// Quick action button component with animated hover effects
export default function QuickActionButton({
  className,
  size = "sm",
  variant = "secondary",
  children,
  ...props
}: QuickActionButtonProps) {
  return (
    <StyledButton
      className={cn(className)}
      size={size}
      variant={variant}
      {...props}
    >
      {children}
    </StyledButton>
  );
}