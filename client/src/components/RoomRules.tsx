import { ExtendedRoom } from '@/types/schema.js';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from './ui/card.jsx';
import { Badge } from './ui/badge.jsx';
import { 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>w<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'lucide-react';

interface RoomRulesProps {
  room: ExtendedRoom;
}

export default function RoomRules({ room }: RoomRulesProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Room Rules & Policies</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Clock className="h-5 w-5 text-muted-foreground" />
              <h4 className="font-medium">Check-in/Check-out</h4>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium">Check-in Time</p>
                <p className="text-sm text-muted-foreground">From 3:00 PM</p>
              </div>
              <div>
                <p className="text-sm font-medium">Check-out Time</p>
                <p className="text-sm text-muted-foreground">Until 12:00 PM</p>
              </div>
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="flex items-center gap-2 mb-3">
              <Cigarette className="h-5 w-5 text-muted-foreground" />
              <h4 className="font-medium">Smoking Policy</h4>
            </div>
            <Badge variant={room.smokingPreference === 'Non-Smoking' ? 'destructive' : 'secondary'}>
              {room.smokingPreference || 'Non-Smoking Room'}
            </Badge>
          </div>

          {room.petPolicy && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2 mb-3">
                <PawPrint className="h-5 w-5 text-muted-foreground" />
                <h4 className="font-medium">Pet Policy</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                {room.petPolicy}
              </p>
              {room.petFee && (
                <Badge variant="secondary" className="mt-2">
                  Pet Fee: {room.petFee}
                </Badge>
              )}
            </div>
          )}

          {room.childPolicy && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2 mb-3">
                <Baby className="h-5 w-5 text-muted-foreground" />
                <h4 className="font-medium">Child Policy</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                {room.childPolicy}
              </p>
            </div>
          )}

          {room.partyPolicy && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2 mb-3">
                <PartyPopper className="h-5 w-5 text-muted-foreground" />
                <h4 className="font-medium">Event/Party Policy</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                {room.partyPolicy}
              </p>
            </div>
          )}

          {room.houseRules && room.houseRules.length > 0 && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2 mb-3">
                <Ban className="h-5 w-5 text-muted-foreground" />
                <h4 className="font-medium">House Rules</h4>
              </div>
              <div className="space-y-2">
                {room.houseRules.map((rule, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Ban className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <p className="text-sm">{rule}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {room.additionalPolicies && (
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-2">Additional Policies</h4>
              <p className="text-sm text-muted-foreground">
                {room.additionalPolicies}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 