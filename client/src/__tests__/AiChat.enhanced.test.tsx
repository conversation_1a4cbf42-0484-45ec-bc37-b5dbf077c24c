import React from 'react';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import AiChat from '../components/AiChat';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a new QueryClient for testing
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

// Mock fetch
global.fetch = jest.fn();

// Setup mock for localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Create a more sophisticated mock for SSE that simulates different response scenarios
class EnhancedMockEventSource {
  onmessage: ((event: { data: string }) => void) | null = null;
  onerror: ((error: Event) => void) | null = null;
  private messageQueue: Array<{ data: string; delay: number }> = [];
  private testScenario: string;
  
  constructor(public url: string) {
    // Extract the test scenario from the URL if present
    const urlParams = new URLSearchParams(url.split('?')[1]);
    this.testScenario = urlParams.get('scenario') || 'default';
    
    // Queue appropriate message sequence based on the scenario
    this.setupScenario();
    
    // Start sending messages
    this.processQueue();
  }
  
  private setupScenario() {
    // Set up different test scenarios
    switch (this.testScenario) {
      case 'location-detection':
        this.messageQueue = [
          { 
            data: JSON.stringify({
              type: 'text',
              data: 'I can help you find hotels in Miami Beach. It\'s a beautiful destination!'
            }),
            delay: 100 
          },
          { 
            data: JSON.stringify({
              type: 'location',
              data: {
                name: 'Miami Beach',
                lat: 25.7907,
                lng: -80.1300,
                placeType: 'locality'
              }
            }),
            delay: 200 
          },
          { 
            data: JSON.stringify({
              type: 'text',
              data: 'Would you like to see some property recommendations?'
            }),
            delay: 300 
          },
          { data: '[DONE]', delay: 350 }
        ];
        break;
        
      case 'property-recommendations':
        this.messageQueue = [
          { 
            data: JSON.stringify({
              type: 'text',
              data: 'Here are some great hotels in New York:'
            }),
            delay: 100 
          },
          { 
            data: JSON.stringify({
              type: 'location',
              data: {
                name: 'New York',
                lat: 40.7128,
                lng: -74.0060,
                placeType: 'locality'
              }
            }),
            delay: 200 
          },
          { 
            data: JSON.stringify({
              type: 'properties',
              data: [
                {
                  id: 1001,
                  name: 'Luxury Times Square Hotel',
                  description: 'Prime location in the heart of NYC',
                  latitude: 40.7589,
                  longitude: -73.9851,
                  address: '123 Broadway',
                  city: 'New York',
                  state: 'NY',
                  country: 'USA',
                  rating: 4.8,
                  reviewCount: 354,
                  basePrice: 399,
                  currency: 'USD',
                  images: ['/times-square-hotel.jpg']
                },
                {
                  id: 1002,
                  name: 'Central Park View',
                  description: 'Stunning views of Central Park',
                  latitude: 40.7645,
                  longitude: -73.9742,
                  address: '456 Park Avenue',
                  city: 'New York',
                  state: 'NY',
                  country: 'USA',
                  rating: 4.5,
                  reviewCount: 287,
                  basePrice: 349,
                  currency: 'USD',
                  images: ['/central-park-hotel.jpg']
                }
              ]
            }),
            delay: 300 
          },
          { 
            data: JSON.stringify({
              type: 'text',
              data: 'These properties are highly rated and offer great amenities.'
            }),
            delay: 400 
          },
          { data: '[DONE]', delay: 450 }
        ];
        break;
        
      case 'action-buttons':
        this.messageQueue = [
          { 
            data: JSON.stringify({
              type: 'text',
              data: 'I can help you narrow down your search. What would you like to do?'
            }),
            delay: 100 
          },
          { 
            data: JSON.stringify({
              type: 'action',
              data: {
                type: 'filter_properties',
                label: 'Filter by Amenities',
                data: {
                  amenities: ['pool', 'spa', 'gym', 'restaurant', 'beachfront']
                }
              }
            }),
            delay: 200 
          },
          { 
            data: JSON.stringify({
              type: 'action',
              data: {
                type: 'price_range',
                label: 'Set Price Range',
                data: {
                  currency: 'USD',
                  min: 100,
                  max: 500
                }
              }
            }),
            delay: 300 
          },
          { 
            data: JSON.stringify({
              type: 'action',
              data: {
                type: 'view_map',
                label: 'View on Map',
                data: {
                  lat: 40.7128,
                  lng: -74.0060,
                  zoom: 12
                }
              }
            }),
            delay: 400 
          },
          { data: '[DONE]', delay: 450 }
        ];
        break;
        
      case 'error-handling':
        this.messageQueue = [
          { 
            data: JSON.stringify({
              type: 'text',
              data: 'I\'m searching for information...'
            }),
            delay: 100 
          },
          { 
            data: JSON.stringify({
              type: 'error',
              data: {
                message: 'Sorry, I encountered an issue retrieving property information.',
                details: 'API timeout'
              }
            }),
            delay: 300 
          },
          { data: '[DONE]', delay: 350 }
        ];
        break;
        
      default:
        // Default scenario with simple text response
        this.messageQueue = [
          { 
            data: JSON.stringify({
              type: 'text',
              data: 'Hello! How can I help you with your travel plans today?'
            }),
            delay: 100 
          },
          { data: '[DONE]', delay: 150 }
        ];
    }
  }
  
  private processQueue() {
    if (this.messageQueue.length === 0) return;
    
    let cumulativeDelay = 0;
    this.messageQueue.forEach(message => {
      cumulativeDelay += message.delay;
      setTimeout(() => {
        if (this.onmessage) {
          this.onmessage({ data: message.data });
        }
      }, cumulativeDelay);
    });
  }
  
  close() {
    // Clear any pending timeouts if needed
    this.messageQueue = [];
  }
}

// Helper function to create a wrapper that provides the query client
const renderWithQueryClient = (ui: React.ReactElement) => {
  return render(
    <QueryClientProvider client={queryClient}>
      {ui}
    </QueryClientProvider>
  );
};

describe('Enhanced AiChat Component Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset localStorage mock
    localStorageMock.clear();
    
    // Mock fetch for API calls
    (global.fetch as jest.Mock).mockImplementation((url, options) => {
      // Extract the scenario from the request body if available
      let scenario = 'default';
      if (options && options.body) {
        try {
          const body = JSON.parse(options.body as string);
          scenario = body.testScenario || 'default';
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return Promise.resolve({
        ok: true,
        headers: {
          get: () => 'text/event-stream'
        },
        body: {
          getReader: () => ({
            read: () => {
              // This is handled by the MockEventSource, not needed here
              return Promise.resolve({ done: true });
            }
          })
        }
      });
    });
    
    // Replace the global EventSource with our enhanced mock
    (global as any).EventSource = EnhancedMockEventSource;
  });
  
  test('renders with initial UI elements', async () => {
    renderWithQueryClient(<AiChat />);
    
    // Check for key UI elements
    expect(screen.getByPlaceholderText(/Type a message/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
    
    // Wait for the initial greeting message
    await waitFor(() => {
      expect(screen.getByText(/Hello! How can I help you with your travel plans today?/i)).toBeInTheDocument();
    });
  });
  
  test('handles location detection scenario', async () => {
    renderWithQueryClient(<AiChat />);
    
    // Update the scenario via the form submission
    const input = screen.getByPlaceholderText(/Type a message/i);
    
    await act(async () => {
      fireEvent.change(input, { target: { value: 'I want to visit Miami Beach' } });
      
      // Force the scenario by manipulating the fetch call
      (global.fetch as jest.Mock).mockImplementationOnce((url, options) => {
        const queryParams = new URLSearchParams();
        queryParams.append('scenario', 'location-detection');
        
        return Promise.resolve({
          ok: true,
          headers: {
            get: () => 'text/event-stream'
          },
          body: {
            getReader: () => ({
              read: () => Promise.resolve({ done: true })
            })
          }
        });
      });
      
      fireEvent.click(screen.getByRole('button', { name: /send/i }));
    });
    
    // Check for the user message
    expect(screen.getByText('I want to visit Miami Beach')).toBeInTheDocument();
    
    // Check for location-specific response
    await waitFor(() => {
      expect(screen.getByText(/I can help you find hotels in Miami Beach/i)).toBeInTheDocument();
    });
    
    // Check for location data visualization
    await waitFor(() => {
      // This depends on your component implementation, but might include a map marker or location card
      const locationElements = screen.getAllByText(/Miami Beach/i);
      expect(locationElements.length).toBeGreaterThanOrEqual(1);
    });
  });
  
  test('handles property recommendations scenario', async () => {
    renderWithQueryClient(<AiChat />);
    
    await act(async () => {
      const input = screen.getByPlaceholderText(/Type a message/i);
      fireEvent.change(input, { target: { value: 'Show me hotels in New York' } });
      
      // Force the property recommendations scenario
      (global.fetch as jest.Mock).mockImplementationOnce((url, options) => {
        const queryParams = new URLSearchParams();
        queryParams.append('scenario', 'property-recommendations');
        
        return Promise.resolve({
          ok: true,
          headers: {
            get: () => 'text/event-stream'
          },
          body: {
            getReader: () => ({
              read: () => Promise.resolve({ done: true })
            })
          }
        });
      });
      
      fireEvent.click(screen.getByRole('button', { name: /send/i }));
    });
    
    // Check for property recommendations response
    await waitFor(() => {
      expect(screen.getByText(/Here are some great hotels in New York/i)).toBeInTheDocument();
    });
    
    // Check for individual property cards or listings
    await waitFor(() => {
      expect(screen.getByText('Luxury Times Square Hotel')).toBeInTheDocument();
      expect(screen.getByText('Central Park View')).toBeInTheDocument();
    });
    
    // Check for property details
    await waitFor(() => {
      expect(screen.getByText(/Prime location in the heart of NYC/i)).toBeInTheDocument();
      expect(screen.getByText(/Stunning views of Central Park/i)).toBeInTheDocument();
    });
  });
  
  test('handles action buttons scenario', async () => {
    renderWithQueryClient(<AiChat />);
    
    await act(async () => {
      const input = screen.getByPlaceholderText(/Type a message/i);
      fireEvent.change(input, { target: { value: 'Help me filter my search' } });
      
      // Force the action buttons scenario
      (global.fetch as jest.Mock).mockImplementationOnce((url, options) => {
        const queryParams = new URLSearchParams();
        queryParams.append('scenario', 'action-buttons');
        
        return Promise.resolve({
          ok: true,
          headers: {
            get: () => 'text/event-stream'
          },
          body: {
            getReader: () => ({
              read: () => Promise.resolve({ done: true })
            })
          }
        });
      });
      
      fireEvent.click(screen.getByRole('button', { name: /send/i }));
    });
    
    // Check for the action prompt
    await waitFor(() => {
      expect(screen.getByText(/I can help you narrow down your search/i)).toBeInTheDocument();
    });
    
    // Check for action buttons
    await waitFor(() => {
      expect(screen.getByText(/Filter by Amenities/i)).toBeInTheDocument();
      expect(screen.getByText(/Set Price Range/i)).toBeInTheDocument();
      expect(screen.getByText(/View on Map/i)).toBeInTheDocument();
    });
  });
  
  test('handles error responses gracefully', async () => {
    renderWithQueryClient(<AiChat />);
    
    await act(async () => {
      const input = screen.getByPlaceholderText(/Type a message/i);
      fireEvent.change(input, { target: { value: 'This will cause an error' } });
      
      // Force the error handling scenario
      (global.fetch as jest.Mock).mockImplementationOnce((url, options) => {
        const queryParams = new URLSearchParams();
        queryParams.append('scenario', 'error-handling');
        
        return Promise.resolve({
          ok: true,
          headers: {
            get: () => 'text/event-stream'
          },
          body: {
            getReader: () => ({
              read: () => Promise.resolve({ done: true })
            })
          }
        });
      });
      
      fireEvent.click(screen.getByRole('button', { name: /send/i }));
    });
    
    // Check for loading message
    await waitFor(() => {
      expect(screen.getByText(/I'm searching for information/i)).toBeInTheDocument();
    });
    
    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/Sorry, I encountered an issue retrieving property information/i)).toBeInTheDocument();
    });
  });
  
  test('persists conversation history in localStorage', async () => {
    renderWithQueryClient(<AiChat />);
    
    await act(async () => {
      const input = screen.getByPlaceholderText(/Type a message/i);
      fireEvent.change(input, { target: { value: 'Remember this message' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));
    });
    
    // Check that localStorage was called to save the messages
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'chatHistory',
        expect.stringContaining('Remember this message')
      );
    });
    
    // Unmount and remount to test persistence
    await act(async () => {
      // Setup localStorage to return our saved messages
      localStorageMock.getItem.mockReturnValueOnce(JSON.stringify([
        {
          role: 'user',
          content: 'Remember this message',
          id: expect.any(String)
        },
        {
          role: 'assistant',
          content: 'Hello! How can I help you with your travel plans today?',
          id: expect.any(String)
        }
      ]));
    });
    
    // Render a new component instance
    renderWithQueryClient(<AiChat />);
    
    // Check that the message history was restored
    await waitFor(() => {
      expect(screen.getByText('Remember this message')).toBeInTheDocument();
      expect(screen.getByText(/Hello! How can I help you with your travel plans today?/i)).toBeInTheDocument();
    });
  });
});