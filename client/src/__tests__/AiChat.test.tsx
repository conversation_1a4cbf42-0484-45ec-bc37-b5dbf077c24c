import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import AiChat from '../components/AiChat';

// Mock fetch
global.fetch = jest.fn();

// Mock EventSource for SSE
class MockEventSource {
  onmessage: ((event: { data: string }) => void) | null = null;
  onerror: ((error: Event) => void) | null = null;
  
  constructor(public url: string) {
    // Mock implementation
    setTimeout(() => {
      // Queue a series of responses
      this.queueMessages();
    }, 100);
  }
  
  close() {
    // Mock implementation to clean up
  }
  
  queueMessages() {
    // Simulate initial message
    if (this.onmessage) {
      this.onmessage({ 
        data: JSON.stringify({
          type: 'text',
          data: 'Hello! How can I help you with your travel plans today?'
        })
      });
    }
    
    // Simulate location detection
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({ 
          data: JSON.stringify({
            type: 'location',
            data: {
              name: 'Miami Beach',
              lat: 25.7907,
              lng: -80.1300,
              placeType: 'locality'
            }
          })
        });
      }
    }, 300);
    
    // Simulate property recommendations
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({ 
          data: JSON.stringify({
            type: 'properties',
            data: [
              {
                id: 1,
                name: 'Luxury Beach Resort',
                description: 'A beautiful beachfront resort',
                latitude: 25.7825,
                longitude: -80.1342,
                address: '123 Ocean Drive',
                city: 'Miami Beach',
                state: 'FL',
                country: 'USA',
                rating: 4.5,
                basePrice: 299,
                currency: 'USD',
                images: ['/beach-resort.jpg']
              },
              {
                id: 2,
                name: 'Downtown Hotel',
                description: 'Centrally located modern hotel',
                latitude: 25.7743,
                longitude: -80.1937,
                address: '456 City Center',
                city: 'Miami',
                state: 'FL',
                country: 'USA',
                rating: 4.2,
                basePrice: 199,
                currency: 'USD',
                images: ['/downtown-hotel.jpg']
              }
            ]
          })
        });
      }
    }, 500);
    
    // Simulate stream end
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({ data: '[DONE]' });
      }
    }, 600);
  }
}

// Replace the global EventSource with our mock
(global as any).EventSource = MockEventSource;

describe('AiChat Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock fetch for API calls
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true })
    });
  });
  
  test('renders the chat interface with initial greeting', async () => {
    render(<AiChat />);
    
    // Check if the input and send button are rendered
    expect(screen.getByPlaceholderText(/Type a message/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
    
    // Wait for the initial greeting message to appear
    await waitFor(() => {
      expect(screen.getByText(/Hello! How can I help you with your travel plans today?/i)).toBeInTheDocument();
    });
  });
  
  test('sends a message and displays AI responses including location data', async () => {
    render(<AiChat />);
    
    // Type a message and send it
    const input = screen.getByPlaceholderText(/Type a message/i);
    fireEvent.change(input, { target: { value: 'I want to stay in Miami Beach' } });
    fireEvent.click(screen.getByRole('button', { name: /send/i }));
    
    // Check if user message appears
    await waitFor(() => {
      expect(screen.getByText('I want to stay in Miami Beach')).toBeInTheDocument();
    });
    
    // Check if AI response appears
    await waitFor(() => {
      expect(screen.getByText(/Hello! How can I help you/i)).toBeInTheDocument();
    });
    
    // Check if location data is processed properly
    await waitFor(() => {
      // Location card or marker should appear
      const locationElements = screen.getAllByText(/Miami Beach/i);
      expect(locationElements.length).toBeGreaterThanOrEqual(1);
    });
    
    // Check if property recommendations appear
    await waitFor(() => {
      expect(screen.getByText('Luxury Beach Resort')).toBeInTheDocument();
      expect(screen.getByText('Downtown Hotel')).toBeInTheDocument();
    });
  });
  
  test('handles errors gracefully', async () => {
    // Mock an error response
    (global as any).EventSource = class ErrorEventSource {
      onerror: ((error: Event) => void) | null = null;
      
      constructor() {
        setTimeout(() => {
          if (this.onerror) {
            this.onerror(new Event('error'));
          }
        }, 100);
      }
      
      close() {}
    };
    
    render(<AiChat />);
    
    // Type a message and send it
    const input = screen.getByPlaceholderText(/Type a message/i);
    fireEvent.change(input, { target: { value: 'Will this cause an error?' } });
    fireEvent.click(screen.getByRole('button', { name: /send/i }));
    
    // Check if error message appears
    await waitFor(() => {
      expect(screen.getByText(/Sorry, I'm having trouble/i)).toBeInTheDocument();
    });
  });
});