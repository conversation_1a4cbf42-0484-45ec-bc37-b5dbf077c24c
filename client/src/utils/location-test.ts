/**
 * Client-side location data handling test
 * This verifies that the client correctly processes location data from the API
 */

// Mock location data that would come from the API
const mockLocationResponse = {
  type: 'location',
  data: {
    name: 'Miami Beach',
    lat: 25.7907,
    lng: -80.1300,
    placeType: 'locality'
  }
};

// Mock location action data
const mockLocationAction = {
  type: 'action',
  data: {
    type: 'location',
    label: 'View Miami Beach',
    data: {
      name: 'Miami Beach',
      lat: 25.7907,
      lng: -80.1300,
      placeType: 'locality'
    }
  }
};

// Function to test location data handling
export function testLocationHandling() {
  console.log('Testing client-side location data handling...');
  
  // Test direct location response
  const locationData = processLocationResponse(mockLocationResponse);
  console.log('Location data from direct response:', locationData);
  
  // Test location in action
  const actionLocationData = processLocationAction(mockLocationAction);
  console.log('Location data from action:', actionLocationData);
}

// Process a location response (mimicking the logic in AiChat component)
function processLocationResponse(response: any) {
  if (response.type === 'location' && response.data) {
    const locationData = response.data;
    
    if (locationData && 
        typeof locationData === 'object' &&
        'name' in locationData && 
        'lat' in locationData && 
        'lng' in locationData) {
      
      return {
        name: locationData.name,
        lat: locationData.lat,
        lng: locationData.lng,
        placeType: locationData.placeType || 'unknown'
      };
    }
  }
  
  return null;
}

// Process a location action (mimicking the logic in AiChat component)
function processLocationAction(response: any) {
  if (response.type === 'action' && 
      response.data && 
      response.data.type === 'location' &&
      response.data.data) {
    
    const locationData = response.data.data;
    
    if (locationData && 
        typeof locationData === 'object' &&
        'name' in locationData && 
        'lat' in locationData && 
        'lng' in locationData) {
      
      return {
        name: locationData.name,
        lat: locationData.lat,
        lng: locationData.lng,
        placeType: locationData.placeType || 'unknown'
      };
    }
  }
  
  return null;
}

// Run the test if this module is executed directly
// You can import this module and call testLocationHandling() from the console
// in the browser to debug location handling
if (typeof window !== 'undefined') {
  // @ts-ignore - Expose this for browser console debugging
  window.testLocationHandling = testLocationHandling;
}