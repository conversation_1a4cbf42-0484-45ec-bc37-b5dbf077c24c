/**
 * AI Chat Test Utility
 * 
 * This utility helps test the AI chat functionality to ensure
 * the initialization fixes work correctly.
 */

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

export class AiChatTester {
  private static instance: AiChatTester;
  
  static getInstance(): AiChatTester {
    if (!AiChatTester.instance) {
      AiChatTester.instance = new AiChatTester();
    }
    return AiChatTester.instance;
  }

  /**
   * Test the basic chat API endpoint
   */
  async testChatAPI(message: string = "Hello, can you help me find a hotel?"): Promise<TestResult> {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          sessionId: `test-${Date.now()}`,
          context: {
            extractLocation: true
          }
        })
      });

      if (!response.ok) {
        return {
          success: false,
          message: `API request failed with status: ${response.status}`,
          details: { status: response.status, statusText: response.statusText }
        };
      }

      // For streaming responses, we just check if we get a valid response
      const reader = response.body?.getReader();
      if (!reader) {
        return {
          success: false,
          message: 'Response body is not readable'
        };
      }

      // Read first chunk to verify streaming works
      const { done, value } = await reader.read();
      reader.releaseLock();

      return {
        success: true,
        message: 'Chat API is working correctly',
        details: { 
          hasStream: !done,
          chunkSize: value?.length || 0
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Chat API test failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Test localStorage initialization
   */
  testLocalStorageInit(): TestResult {
    try {
      // Clear any existing data
      localStorage.removeItem('chatHistory');
      localStorage.removeItem('ai_chat_trigger');
      localStorage.removeItem('conversationState');

      // Simulate the "Plan with AI" flow
      const testMessage = {
        role: "user",
        content: "Hi! I'm looking for a place to stay in New York. Can you help me find the perfect place?",
        id: `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      };

      localStorage.setItem('chatHistory', JSON.stringify([testMessage]));
      localStorage.setItem('ai_chat_trigger', 'true');

      // Verify data was stored
      const storedHistory = localStorage.getItem('chatHistory');
      const storedTrigger = localStorage.getItem('ai_chat_trigger');

      if (!storedHistory || !storedTrigger) {
        return {
          success: false,
          message: 'Failed to store data in localStorage'
        };
      }

      const parsedHistory = JSON.parse(storedHistory);
      if (parsedHistory.length !== 1 || parsedHistory[0].role !== 'user') {
        return {
          success: false,
          message: 'Stored chat history is invalid',
          details: { parsedHistory }
        };
      }

      return {
        success: true,
        message: 'localStorage initialization test passed',
        details: { 
          messageStored: true,
          triggerSet: storedTrigger === 'true',
          messageContent: parsedHistory[0].content
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'localStorage test failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Test session ID generation
   */
  testSessionIdGeneration(): TestResult {
    try {
      // Clear existing session ID
      localStorage.removeItem('booking_session_id');

      // Simulate session ID generation (like in AiChat component)
      const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      localStorage.setItem('booking_session_id', newSessionId);

      const storedSessionId = localStorage.getItem('booking_session_id');
      
      if (!storedSessionId || storedSessionId !== newSessionId) {
        return {
          success: false,
          message: 'Session ID generation failed'
        };
      }

      // Test that subsequent calls use the same session ID
      const existingSessionId = localStorage.getItem('booking_session_id');
      
      return {
        success: true,
        message: 'Session ID generation test passed',
        details: { 
          sessionId: existingSessionId,
          isValid: existingSessionId?.startsWith('session-')
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Session ID test failed',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<{ [key: string]: TestResult }> {
    console.log('🧪 Running AI Chat Tests...');
    
    const results = {
      localStorage: this.testLocalStorageInit(),
      sessionId: this.testSessionIdGeneration(),
      chatAPI: await this.testChatAPI()
    };

    console.log('📊 Test Results:', results);
    
    const allPassed = Object.values(results).every(result => result.success);
    console.log(allPassed ? '✅ All tests passed!' : '❌ Some tests failed');
    
    return results;
  }

  /**
   * Clean up test data
   */
  cleanup(): void {
    localStorage.removeItem('chatHistory');
    localStorage.removeItem('ai_chat_trigger');
    localStorage.removeItem('conversationState');
    localStorage.removeItem('booking_session_id');
    console.log('🧹 Test cleanup completed');
  }
}

// Export a singleton instance for easy use
export const aiChatTester = AiChatTester.getInstance();

// Add to window for browser console testing
if (typeof window !== 'undefined') {
  (window as any).aiChatTester = aiChatTester;
} 