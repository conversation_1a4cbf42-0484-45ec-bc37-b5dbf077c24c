import { PropertyImage, RoomImage } from '../types/schema';

/**
 * Transform URL to use full-sized version using regex to handle all cases
 */
export function getFullSizeImageUrl(imagePath: string): string {
  return imagePath.replace(/_.+\.jpg$/, '_0.jpg');
}

/**
 * Get the image source URL with proper type handling and fallback
 * @param image The image which can be a string URL or PropertyImage object
 * @param fallbackImage Optional fallback image URL
 * @returns The resolved image URL
 */
export function getImageSource(image: string | PropertyImage | undefined | null, fallbackImage: string = '/placeholder-property.jpg'): string {
  // console.log('getImageSource input:', {
  //   image,
  //   type: typeof image,
  //   isString: typeof image === 'string',
  //   hasUrl: image && typeof image === 'object' && 'url' in image,
  // });

  if (!image) {
    // console.log('getImageSource: no image, using fallback');
    return fallbackImage;
  }

  if (typeof image === 'string') {
    // console.log('getImageSource: image is string URL');
    return image;
  }

  if ('url' in image && image.url) {
    // console.log('getImageSource: using image.url');
    return image.url;
  }

  //console.log('getImageSource: no valid URL found, using fallback');
  return fallbackImage;
}

/**
 * Sort images by their display order
 * @param images Array of images to sort
 * @returns Sorted array of images
 */
export function sortByDisplayOrder<T extends PropertyImage>(images: T[]): T[] {
  return [...images].sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
}

/**
 * Filter property images to get room-specific images
 * @param images Array of property images
 * @param roomTypeCode The room type code to filter by
 * @returns Array of images specific to the room type
 */
export function getRoomImages(images: (PropertyImage | RoomImage)[], roomTypeCode: string): PropertyImage[] {
  return sortByDisplayOrder(
    images.filter((img): img is RoomImage => 
      'roomTypeCode' in img && img.roomTypeCode === roomTypeCode
    )
  );
}

/**
 * Get all property-level images (excluding room-specific images)
 * @param images Array of property images
 * @returns Array of property-level images
 */
export function getPropertyImages(images: (PropertyImage | RoomImage)[]): PropertyImage[] {
  return sortByDisplayOrder(
    images.filter(img => 
      !('roomTypeCode' in img) || img.category === 'property'
    )
  );
}

// TODO: Add your custom image filtering logic here
// This is where you can add additional functions to filter images based on your specific needs
// For example:
// - Filter by image category
// - Get images for multiple room types
// - Combine property and room images
// - Filter by custom metadata 