import { 
  Wifi, 
  Car, 
  Droplets, 
  Utensils, 
  Dumbbell, 
  Waves, 
  Coffee, 
  Wine,
  Bath,
  Snowflake,
  LucideIcon
} from "lucide-react";

type AmenityKey = 'wifi' | 'parking' | 'pool' | 'restaurant' | 'fitness' | 'beach' | 'breakfast' | 'bar' | 'spa' | 'aircon';

/**
 * Mapping of amenity keywords to their corresponding icons
 */
export const amenityIcons: Record<AmenityKey, LucideIcon> = {
  'wifi': Wifi,
  'parking': Car,
  'pool': Droplets,
  'restaurant': Utensils,
  'fitness': Dumbbell,
  'beach': Waves,
  'breakfast': Coffee,
  'bar': Wine,
  'spa': Bath,
  'aircon': Snowflake
};

/**
 * Get the icon component for a given amenity name
 * @param amenity The amenity name to find an icon for
 * @returns The icon component or null if no match found
 */
export function getAmenityIcon(amenity: string): LucideIcon | null {
  const lowerAmenity = amenity.toLowerCase();
  const key = Object.keys(amenityIcons).find(key => 
    lowerAmenity.includes(key)
  ) as Amenity<PERSON>ey | undefined;
  
  return key ? amenityIcons[key] : null;
} 