/**
 * Enhanced AI Chat Testing Utility
 * 
 * Comprehensive testing for the improved AI travel companion
 */

interface TestResult {
  passed: boolean;
  message: string;
  details?: any;
}

export class EnhancedAIChatTester {
  private static instance: EnhancedAIChatTester;

  static getInstance(): EnhancedAIChatTester {
    if (!this.instance) {
      this.instance = new EnhancedAIChatTester();
    }
    return this.instance;
  }

  /**
   * Test the complete "Plan with AI" flow
   */
  async testPlanWithAIFlow(): Promise<TestResult> {
    console.log('🧪 Testing Enhanced AI Chat - Plan with AI Flow...');

    try {
      // Step 1: Clear existing data
      this.clearChatData();

      // Step 2: Find and click the Plan with AI button
      const button = document.querySelector('button:has(.lucide-message-circle)') as HTMLButtonElement;
      if (!button) {
        return {
          passed: false,
          message: 'Plan with AI button not found',
        };
      }

      // Simulate button click
      button.click();

      // Step 3: Wait for modal to appear
      await this.waitForElement('.fixed.inset-0.z-\\[1000\\]', 2000);

      // Step 4: Check if enhanced chat component loaded
      const chatCard = document.querySelector('[class*="AI Travel Companion"]');
      if (!chatCard) {
        return {
          passed: false,
          message: 'Enhanced AI Chat component did not load',
        };
      }

      // Step 5: Check for welcome message and quick actions
      const quickActions = document.querySelectorAll('button:has(.lucide-compass), button:has(.lucide-calendar)');
      if (quickActions.length < 2) {
        return {
          passed: false,
          message: 'Quick action buttons not found',
          details: { quickActionsFound: quickActions.length }
        };
      }

      return {
        passed: true,
        message: 'Enhanced AI Chat loaded successfully with all components',
        details: {
          quickActionsCount: quickActions.length,
          modalVisible: true
        }
      };
    } catch (error) {
      return {
        passed: false,
        message: `Test failed with error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      };
    }
  }

  /**
   * Test chat interaction
   */
  async testChatInteraction(): Promise<TestResult> {
    console.log('🧪 Testing Chat Interaction...');

    try {
      // Find input field
      const input = document.querySelector('input[placeholder*="Ask me anything about travel"]') as HTMLInputElement;
      if (!input) {
        return {
          passed: false,
          message: 'Chat input field not found'
        };
      }

      // Type a message
      input.value = 'Show me hotels in Miami Beach';
      input.dispatchEvent(new Event('input', { bubbles: true }));

      // Find and click send button
      const sendButton = document.querySelector('button:has(.lucide-send)') as HTMLButtonElement;
      if (!sendButton) {
        return {
          passed: false,
          message: 'Send button not found'
        };
      }

      sendButton.click();

      // Wait for response
      await this.waitForElement('[class*="Thinking"]', 1000);

      return {
        passed: true,
        message: 'Chat interaction successful',
        details: {
          messageSent: true,
          loadingIndicatorShown: true
        }
      };
    } catch (error) {
      return {
        passed: false,
        message: `Interaction test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Test quick actions
   */
  async testQuickActions(): Promise<TestResult> {
    console.log('🧪 Testing Quick Actions...');

    try {
      // Find "Explore Destinations" button
      const exploreButton = Array.from(document.querySelectorAll('button')).find(
        btn => btn.textContent?.includes('Explore Destinations')
      ) as HTMLButtonElement;

      if (!exploreButton) {
        return {
          passed: false,
          message: 'Explore Destinations button not found'
        };
      }

      // Click the button
      exploreButton.click();

      // Check if text was added to input
      const input = document.querySelector('input[placeholder*="Ask me anything about travel"]') as HTMLInputElement;
      if (!input || !input.value.includes('popular travel destinations')) {
        return {
          passed: false,
          message: 'Quick action did not populate input field',
          details: { inputValue: input?.value }
        };
      }

      return {
        passed: true,
        message: 'Quick actions working correctly',
        details: { inputValue: input.value }
      };
    } catch (error) {
      return {
        passed: false,
        message: `Quick action test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Test API integration
   */
  async testAPIIntegration(): Promise<TestResult> {
    console.log('🧪 Testing API Integration...');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: 'Test message for Miami hotels',
          sessionId: 'test-enhanced-' + Date.now(),
          extractLocation: true
        })
      });

      if (!response.ok) {
        return {
          passed: false,
          message: `API returned error: ${response.status}`,
          details: { status: response.status, statusText: response.statusText }
        };
      }

      // Check if response is streaming
      const reader = response.body?.getReader();
      if (!reader) {
        return {
          passed: false,
          message: 'API response is not streaming'
        };
      }

      // Read first chunk
      const { value, done } = await reader.read();
      if (!value && !done) {
        return {
          passed: false,
          message: 'No data received from API'
        };
      }

      return {
        passed: true,
        message: 'API integration working correctly',
        details: {
          streaming: true,
          firstChunkSize: value?.length || 0
        }
      };
    } catch (error) {
      return {
        passed: false,
        message: `API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Simulate a user query and validate the response
   */
  async testUserQuery({ input, expect, description }: { input: string, expect: string[], description: string }): Promise<TestResult> {
    this.clearChatData();
    const inputBox = document.querySelector('input[placeholder="Ask me anything about travel..."]') as HTMLInputElement;
    if (!inputBox) return { passed: false, message: `Input box not found: ${description}` };
    inputBox.value = input;
    inputBox.dispatchEvent(new Event('input', { bubbles: true }));
    const sendBtn = document.querySelector('button[type="submit"]');
    if (!sendBtn) return { passed: false, message: `Send button not found: ${description}` };
    sendBtn.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    // Wait for AI response
    await new Promise(res => setTimeout(res, 2500));
    const chat = document.querySelector('.scroll-area') || document.body;
    let passed = true;
    for (const keyword of expect) {
      if (!chat.textContent?.toLowerCase().includes(keyword.toLowerCase())) {
        passed = false;
        break;
      }
    }
    return {
      passed,
      message: passed ? `PASS: ${description}` : `FAIL: ${description}`,
      details: passed ? undefined : { input, expect, chat: chat.textContent }
    };
  }

  /**
   * Simulate a quick action click and validate the response
   */
  async testQuickAction({ actionLabel, expect, description }: { actionLabel: string, expect: string[], description: string }): Promise<TestResult> {
    this.clearChatData();
    // Wait for quick actions to render
    await new Promise(res => setTimeout(res, 500));
    const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent?.includes(actionLabel));
    if (!btn) return { passed: false, message: `Quick action '${actionLabel}' not found: ${description}` };
    btn.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    // Wait for AI response
    await new Promise(res => setTimeout(res, 2500));
    const chat = document.querySelector('.scroll-area') || document.body;
    let passed = true;
    for (const keyword of expect) {
      if (!chat.textContent?.toLowerCase().includes(keyword.toLowerCase())) {
        passed = false;
        break;
      }
    }
    return {
      passed,
      message: passed ? `PASS: ${description}` : `FAIL: ${description}`,
      details: passed ? undefined : { actionLabel, expect, chat: chat.textContent }
    };
  }

  /**
   * Test modal maximize/restore controls
   */
  async testModalControls(): Promise<TestResult> {
    // Find maximize button
    const maxBtn = Array.from(document.querySelectorAll('button')).find(b => b.getAttribute('aria-label')?.toLowerCase().includes('maximize'));
    if (!maxBtn) return { passed: false, message: 'Maximize button not found' };
    maxBtn.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    await new Promise(res => setTimeout(res, 500));
    const modal = document.querySelector('.max-w-none');
    if (!modal) return { passed: false, message: 'Modal did not maximize' };
    // Restore
    maxBtn.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    await new Promise(res => setTimeout(res, 500));
    const restored = document.querySelector('.max-w-4xl');
    if (!restored) return { passed: false, message: 'Modal did not restore' };
    return { passed: true, message: 'PASS: Modal maximize/restore controls work' };
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<{
    summary: string;
    results: Record<string, TestResult>;
  }> {
    console.log('🚀 Running Enhanced AI Chat Test Suite...\n');

    const results: Record<string, TestResult> = {};

    // Test 1: Plan with AI flow
    results['planWithAI'] = await this.testPlanWithAIFlow();
    console.log(`✅ Plan with AI: ${results['planWithAI'].passed ? 'PASSED' : 'FAILED'}`);

    // Wait a bit between tests
    await this.wait(1000);

    // Test 2: Chat interaction
    results['chatInteraction'] = await this.testChatInteraction();
    console.log(`✅ Chat Interaction: ${results['chatInteraction'].passed ? 'PASSED' : 'FAILED'}`);

    await this.wait(500);

    // Test 3: Quick actions
    results['quickActions'] = await this.testQuickActions();
    console.log(`✅ Quick Actions: ${results['quickActions'].passed ? 'PASSED' : 'FAILED'}`);

    await this.wait(500);

    // Test 4: API integration
    results['apiIntegration'] = await this.testAPIIntegration();
    console.log(`✅ API Integration: ${results['apiIntegration'].passed ? 'PASSED' : 'FAILED'}`);

    // Calculate summary
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(r => r.passed).length;
    const summary = `${passedTests}/${totalTests} tests passed`;

    console.log(`\n📊 Test Summary: ${summary}`);

    return { summary, results };
  }

  /**
   * Utility functions
   */
  clearChatData(): void {
    localStorage.removeItem('chatHistory');
    localStorage.removeItem('ai_chat_trigger');
    localStorage.removeItem('ai_chat_initial_user_message');
    localStorage.removeItem('ai_chat_trigger_processed');
    localStorage.removeItem('enhancedChatHistory');
    localStorage.removeItem('enhancedConversationState');
  }

  private async waitForElement(selector: string, timeout: number = 5000): Promise<Element> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const element = document.querySelector(selector);
      if (element) return element;
      await this.wait(100);
    }
    
    throw new Error(`Element ${selector} not found after ${timeout}ms`);
  }

  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const enhancedAITester = EnhancedAIChatTester.getInstance();

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).testEnhancedAI = {
    full: () => enhancedAITester.runAllTests(),
    planWithAI: () => enhancedAITester.testPlanWithAIFlow(),
    interaction: () => enhancedAITester.testChatInteraction(),
    quickActions: () => enhancedAITester.testQuickActions(),
    api: () => enhancedAITester.testAPIIntegration(),
    clear: () => enhancedAITester.clearChatData()
  };

  console.log('🎯 Enhanced AI Chat Testing Available:');
  console.log('  testEnhancedAI.full() - Run all tests');
  console.log('  testEnhancedAI.planWithAI() - Test Plan with AI button');
  console.log('  testEnhancedAI.interaction() - Test chat interaction');
  console.log('  testEnhancedAI.quickActions() - Test quick action buttons');
  console.log('  testEnhancedAI.api() - Test API integration');
  console.log('  testEnhancedAI.clear() - Clear all chat data');
} 