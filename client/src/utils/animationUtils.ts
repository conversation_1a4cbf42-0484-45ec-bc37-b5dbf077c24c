import { Skeleton } from "@/components/ui/skeleton";
import styled from '@emotion/styled';

/**
 * CSS keyframes for shimmer loading animation
 */
export const shimmerKeyframes = `
  @keyframes shimmer {
    0% {
      background-position: -1000px 0;
    }
    100% {
      background-position: 1000px 0;
    }
  }
`;

/**
 * Styled component for shimmer loading effect
 */
export const ShimmerSkeleton = styled(Skeleton)`
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 1000px 100%;
  animation: shimmer 2s infinite linear;
  ${shimmerKeyframes}
`; 