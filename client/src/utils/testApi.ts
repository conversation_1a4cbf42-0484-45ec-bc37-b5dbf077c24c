/**
 * Test API Client
 * Provides methods to interact with the test results API endpoints
 */

/**
 * Base type for test results
 */
export interface TestResult {
  id: number;
  sessionId: string;
  userId?: number;
  testType: "location" | "chat" | "property" | "integrated";
  query: string;
  success: boolean;
  duration: number; 
  timestamp: string;
  metrics: Record<string, number | boolean | string>;
  context?: Record<string, any>;
  results?: Record<string, any>;
  error?: string;
}

/**
 * Interface for test result creation payload
 */
export interface TestResultPayload {
  sessionId?: string;
  testType: "location" | "chat" | "property" | "integrated";
  query: string;
  success: boolean;
  duration: number;
  metrics?: Record<string, number | boolean | string>;
  context?: Record<string, any>;
  results?: Record<string, any>;
  error?: string;
}

/**
 * Interface for test metrics summary
 */
export interface TestMetrics {
  overview: {
    total: number;
    success: number;
    successRate: number;
    avgDuration: number;
  };
  testTypeCounts: Array<{
    testType: string;
    count: number;
  }>;
  hourlyTrends: Array<{
    hour: string;
    count: number;
    successCount: number;
    avgDuration: number;
  }>;
  commonErrors: Array<{
    error: string;
    count: number;
  }>;
  timeframe: string;
}

/**
 * Save a test result
 * @param testData The test result data to save
 */
export async function saveTestResult(testData: TestResultPayload): Promise<TestResult> {
  const token = localStorage.getItem('token');
  
  const response = await fetch('/api/tests/results', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    },
    body: JSON.stringify(testData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to save test result');
  }

  const data = await response.json();
  return data.data;
}

/**
 * Get all test results with optional filtering
 */
export async function getTestResults(options: {
  testType?: string;
  limit?: number;
  offset?: number;
  userId?: number;
  sessionId?: string;
  success?: boolean;
} = {}): Promise<{
  results: TestResult[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    pages: number;
    currentPage: number;
  };
}> {
  // Build query string from options
  const params = new URLSearchParams();
  
  if (options.testType) {
    params.append('testType', options.testType);
  }
  
  if (options.limit) {
    params.append('limit', options.limit.toString());
  }
  
  if (options.offset) {
    params.append('offset', options.offset.toString());
  }
  
  if (options.userId) {
    params.append('userId', options.userId.toString());
  }
  
  if (options.sessionId) {
    params.append('sessionId', options.sessionId);
  }
  
  if (options.success !== undefined) {
    params.append('success', options.success.toString());
  }

  const token = localStorage.getItem('token');
  
  const response = await fetch(`/api/tests/results?${params.toString()}`, {
    headers: {
      'Authorization': token ? `Bearer ${token}` : '',
    }
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get test results');
  }

  const data = await response.json();
  return data.data;
}

/**
 * Get a test result by ID
 * @param id The ID of the test result to get
 */
export async function getTestResultById(id: number): Promise<TestResult> {
  const token = localStorage.getItem('token');
  
  const response = await fetch(`/api/tests/results/${id}`, {
    headers: {
      'Authorization': token ? `Bearer ${token}` : '',
    }
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get test result');
  }

  const data = await response.json();
  return data.data;
}

/**
 * Get test metrics summary
 */
export async function getTestMetrics(timeframe: '24h' | '7d' | '30d' = '30d', testType?: string): Promise<TestMetrics> {
  const params = new URLSearchParams();
  params.append('timeframe', timeframe);
  
  if (testType) {
    params.append('testType', testType);
  }

  const token = localStorage.getItem('token');
  
  const response = await fetch(`/api/tests/metrics?${params.toString()}`, {
    headers: {
      'Authorization': token ? `Bearer ${token}` : '',
    }
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get test metrics');
  }

  const data = await response.json();
  return data.data;
}

/**
 * Delete a test result
 * @param id The ID of the test result to delete
 */
export async function deleteTestResult(id: number): Promise<void> {
  const token = localStorage.getItem('token');
  
  const response = await fetch(`/api/tests/results/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': token ? `Bearer ${token}` : '',
    }
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete test result');
  }
}