@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes subtlePulse {
  0% {
    filter: brightness(100%) drop-shadow(0 2px 10px rgba(59,130,246,0.5)) drop-shadow(0 0px 20px rgba(147,197,253,0.3)) drop-shadow(0 0px 50px rgba(59,130,246,0.2));
  }
  50% {
    filter: brightness(115%) drop-shadow(0 2px 15px rgba(59,130,246,0.6)) drop-shadow(0 0px 30px rgba(147,197,253,0.4)) drop-shadow(0 0px 60px rgba(59,130,246,0.3));
  }
  100% {
    filter: brightness(100%) drop-shadow(0 2px 10px rgba(59,130,246,0.5)) drop-shadow(0 0px 20px rgba(147,197,253,0.3)) drop-shadow(0 0px 50px rgba(59,130,246,0.2));
  }
}

.animate-pulse-subtle {
  animation: subtlePulse 3s ease-in-out infinite;
} 