import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";

interface WholesaleConfig {
  id: number;
  propertyId: number;
  markup: number;
  enabled: boolean;
}

export default function Admin() {
  const { toast } = useToast();
  const [markup, setMarkup] = useState<string>("");

  const { data: configs, isLoading } = useQuery<WholesaleConfig[]>({
    queryKey: ["/api/admin/wholesale-configs"],
  });

  const updateConfig = useMutation({
    mutationFn: async (config: Partial<WholesaleConfig>) => {
      const res = await fetch(`/api/admin/wholesale-configs/${config.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(config),
      });
      
      if (!res.ok) throw new Error("Failed to update config");
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Wholesale configuration updated",
      });
    },
  });

  const createConfig = useMutation({
    mutationFn: async (data: { propertyId: number; markup: number }) => {
      const res = await fetch("/api/admin/wholesale-configs", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      
      if (!res.ok) throw new Error("Failed to create config");
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "New wholesale configuration added",
      });
      setMarkup("");
    },
  });

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>

        <div className="grid gap-8">
          <Card>
            <CardHeader>
              <CardTitle>Wholesale Configurations</CardTitle>
              <CardDescription>
                Manage markup rates for wholesale properties
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Property ID</TableHead>
                    <TableHead>Markup (%)</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {configs?.map((config) => (
                    <TableRow key={config.id}>
                      <TableCell>{config.propertyId}</TableCell>
                      <TableCell>{config.markup}%</TableCell>
                      <TableCell>
                        <Switch
                          checked={config.enabled}
                          onCheckedChange={(checked) =>
                            updateConfig.mutate({
                              id: config.id,
                              enabled: checked,
                            })
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          defaultValue={config.markup}
                          className="w-24"
                          onBlur={(e) =>
                            updateConfig.mutate({
                              id: config.id,
                              markup: parseFloat(e.target.value),
                            })
                          }
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="mt-6 flex gap-4">
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={markup}
                  onChange={(e) => setMarkup(e.target.value)}
                  placeholder="Enter markup %"
                  className="w-48"
                />
                <Button
                  onClick={() =>
                    createConfig.mutate({
                      propertyId: 1, // Replace with actual property selection
                      markup: parseFloat(markup),
                    })
                  }
                >
                  Add Configuration
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
