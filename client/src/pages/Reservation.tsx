import { useLocation, useParams } from "wouter";
import { useProperty, useCreateReservation } from "@/lib/api.js";
import { Button } from "@/components/ui/button.jsx";
import { Card, CardContent } from "@/components/ui/card.jsx";
import { Skeleton } from "@/components/ui/skeleton.jsx";
import { Alert, AlertDescription } from "@/components/ui/alert.jsx";
import { Input } from "@/components/ui/input.jsx";
import { Label } from "@/components/ui/label.jsx";
import { useState, useMemo } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function Reservation() {
  const [_, navigate] = useLocation();
  const searchParams = useMemo(() => {
    if (typeof window === 'undefined') return null;
    return new URLSearchParams(window.location.search);
  }, []);

  const propertyId = searchParams?.get('propertyId') || '';
  const { data: property, isLoading: isLoadingProperty } = useProperty(propertyId);
  const createReservation = useCreateReservation();

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    specialRequests: ''
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Get reservation details from URL
  const reservationDetails = useMemo(() => {
    if (!searchParams) return null;
    return {
      checkIn: searchParams.get('checkIn'),
      checkOut: searchParams.get('checkOut'),
      guests: searchParams.get('guests'),
      rooms: searchParams.get('rooms'),
      rate: parseFloat(searchParams.get('rate') || '0'),
      currency: searchParams.get('currency'),
      totalAmount: parseFloat(searchParams.get('totalAmount') || '0'),
      totalCurrency: searchParams.get('totalCurrency'),
      roomCode: searchParams.get('roomCode'),
      ratePlanCode: searchParams.get('ratePlanCode')
    };
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!reservationDetails) return;

    try {
      setError(null);
      await createReservation.mutateAsync({
        propertyId: parseInt(propertyId),
        checkIn: reservationDetails.checkIn!,
        checkOut: reservationDetails.checkOut!,
        guests: parseInt(reservationDetails.guests!),
        roomCode: reservationDetails.roomCode!,
        ratePlanCode: reservationDetails.ratePlanCode!
      });

      // Navigate to confirmation page
      navigate("/reservations");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create reservation");
    }
  };

  if (isLoadingProperty || !property || !reservationDetails) {
    return <Skeleton className="w-full h-screen" />;
  }

  const nights = Math.ceil(
    (new Date(reservationDetails.checkOut!).getTime() - new Date(reservationDetails.checkIn!).getTime()) 
    / (1000 * 60 * 60 * 24)
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid md:grid-cols-[2fr,1fr] gap-8">
        {/* Main Content */}
        <div className="space-y-8">
          <div>
            <Button 
              variant="ghost" 
              className="mb-4"
              onClick={() => window.history.back()}
            >
              <ChevronLeft className="mr-2 h-4 w-4" /> Back to Property
            </Button>
            <h1 className="text-3xl font-bold">Complete Your Reservation</h1>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4">Guest Information</h2>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={e => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={e => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={e => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={e => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      required
                    />
                  </div>
                </div>
                <div className="mt-4 space-y-2">
                  <Label htmlFor="specialRequests">Special Requests</Label>
                  <Input
                    id="specialRequests"
                    value={formData.specialRequests}
                    onChange={e => setFormData(prev => ({ ...prev, specialRequests: e.target.value }))}
                  />
                </div>
              </CardContent>
            </Card>

            <Button 
              type="submit" 
              size="lg" 
              className="w-full"
              disabled={createReservation.isPending}
            >
              {createReservation.isPending ? "Creating Reservation..." : "Complete Booking"}
            </Button>
          </form>
        </div>

        {/* Summary Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardContent className="p-6">
              <div className="relative h-48 mb-4">
                {property.images && property.images.length > 0 && (
                  <>
                    <img
                      src={typeof property.images[currentImageIndex] === 'string' 
                        ? property.images[currentImageIndex] 
                        : property.images[currentImageIndex].url}
                      alt={property.name}
                      className="w-full h-full object-cover"
                    />
                    {property.images.length > 1 && (
                      <>
                        <button
                          onClick={() => setCurrentImageIndex(prev => 
                            prev === 0 ? property.images!.length - 1 : prev - 1
                          )}
                          className="absolute left-2 top-1/2 -translate-y-1/2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                        >
                          <ChevronLeft className="h-4 w-4 text-white" />
                        </button>
                        <button
                          onClick={() => setCurrentImageIndex(prev => 
                            prev === property.images!.length - 1 ? 0 : prev + 1
                          )}
                          className="absolute right-2 top-1/2 -translate-y-1/2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                        >
                          <ChevronRight className="h-4 w-4 text-white" />
                        </button>
                      </>
                    )}
                  </>
                )}
              </div>

              <h2 className="text-xl font-bold mb-2">{property.name}</h2>
              <div className="text-sm text-muted-foreground mb-4">
                {property.address}, {property.city}, {property.state}
              </div>

              <div className="space-y-4 border-t pt-4">
                <div className="flex justify-between">
                  <span>Check-in</span>
                  <span className="font-medium">{new Date(reservationDetails.checkIn!).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Check-out</span>
                  <span className="font-medium">{new Date(reservationDetails.checkOut!).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Guests</span>
                  <span className="font-medium">{reservationDetails.guests}</span>
                </div>
                <div className="flex justify-between">
                  <span>Rooms</span>
                  <span className="font-medium">{reservationDetails.rooms}</span>
                </div>
              </div>

              <div className="border-t mt-4 pt-4">
                <div className="flex justify-between mb-2">
                  <span>Rate per night</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: reservationDetails.currency!
                    }).format(reservationDetails.rate)}
                  </span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground mb-4">
                  <span>{nights} nights</span>
                  <span>
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: reservationDetails.currency!
                    }).format(reservationDetails.rate * nights)}
                  </span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: reservationDetails.totalCurrency!
                    }).format(reservationDetails.totalAmount)}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Includes taxes and fees
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 