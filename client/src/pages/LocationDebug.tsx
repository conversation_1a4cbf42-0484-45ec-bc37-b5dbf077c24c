import { Container } from "@/components/ui/container";
import LocationDebugTool from "@/components/LocationDebugTool";

export default function LocationDebug() {
  return (
    <Container>
      <div className="max-w-3xl mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">Location Detection Debug</h1>
        <p className="text-muted-foreground mb-6">
          This page helps developers test the location detection capabilities of our AI system.
          Enter location queries to see if the AI can correctly extract and identify the location data.
        </p>
        
        <LocationDebugTool />
        
        <div className="mt-8 text-sm text-muted-foreground space-y-4">
          <h2 className="font-semibold text-foreground">About Location Detection</h2>
          <p>
            Our AI uses advanced natural language processing to extract location information from user queries.
            This is crucial for providing relevant hotel and accommodation recommendations.
          </p>
          <p>
            When testing, try queries like "I want to stay in Miami Beach" or "Find hotels in New York" 
            to see how the system identifies locations and extracts their coordinates.
          </p>
          <p>
            The location detection system supports:
          </p>
          <ul className="list-disc pl-5 space-y-1">
            <li>Major cities worldwide</li>
            <li>Popular tourist destinations</li>
            <li>Neighborhoods and districts</li>
            <li>Various natural language query formats</li>
          </ul>
          <div className="pt-4 border-t">
            <h3 className="font-semibold mb-2">Technical Details</h3>
            <p>
              The location detection uses the <code className="bg-muted px-1 rounded text-xs">extractLocation</code> flag
              in API requests to ensure consistent and accurate extraction of location data from user queries.
            </p>
          </div>
        </div>
      </div>
    </Container>
  );
}