import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  MessageCircle, 
  Search as SearchIcon, 
  Star,
  MapPin
} from "lucide-react";
import AiChatEnhanced from "@/components/AiChatEnhanced";
import SearchForm from "@/components/SearchForm";
import FeaturedProperties from "@/components/FeaturedProperties";
import { useLocation } from "wouter";
import { SearchFormValues } from "@/components/SearchForm";
import { Card } from "@/components/ui/card";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useSearchHistory } from "@/hooks/use-search-history";
import RecentSearches from "@/components/RecentSearches";
import Map from "@/components/Map";

export default function Search() {
  const [showAiChat, setShowAiChat] = useState(false);
  const [_, setLocation] = useLocation();
  const searchFormRef = useRef<{ submit: () => void }>(null);
  const [searchContext, setSearchContext] = useState({
    location: null as string | null,
    checkIn: null as string | null,
    checkOut: null as string | null,
    guests: null as string | null,
    rooms: null as string | null
  });

  const { 
    searchHistory, 
    addToHistory, 
    clearHistory, 
    repeatSearch 
  } = useSearchHistory();

  const handleSearchSubmit = (params: SearchFormValues) => {
    const searchParams = new URLSearchParams({
      lat: params.lat?.toString() || '',
      lng: params.lng?.toString() || '',
      locationName: params.locationName || '',
      checkIn: params.checkIn || '',
      checkOut: params.checkOut || '',
      guests: params.guests?.toString() || '',
      rooms: params.rooms?.toString() || ''
    });

    // Add to search history
    addToHistory({
      locationName: params.locationName || '',
      lat: params.lat || 0,
      lng: params.lng || 0,
      checkIn: params.checkIn || '',
      checkOut: params.checkOut || '',
      guests: params.guests || 2,
      rooms: params.rooms || 1
    });

    setLocation(`/results?${searchParams.toString()}`);
  };

  const handleFormUpdate = (data: SearchFormValues) => {
    setSearchContext({
      location: data.locationName || null,
      checkIn: data.checkIn || null,
      checkOut: data.checkOut || null,
      guests: data.guests?.toString() || null,
      rooms: data.rooms?.toString() || null
    });
  };

  const getInitialMessage = () => {
    // First check search history
    if (searchHistory.length > 0) {
      const lastSearch = searchHistory[0];
      return `Hi! I see you recently searched for ${lastSearch.locationName}. Would you like to explore similar destinations or find something different?`;
    }

    if (!searchContext.location && !searchContext.checkIn && !searchContext.guests) {
      return "Hi! I'd like help finding a place to stay. Can you help me explore my options?";
    }

    let message = "Hi! I'm looking for";
    if (searchContext.location) {
      message += ` a place to stay in ${searchContext.location}`;
    }
    if (searchContext.checkIn && searchContext.checkOut) {
      message += ` from ${format(new Date(searchContext.checkIn), 'PPP')} to ${format(new Date(searchContext.checkOut), 'PPP')}`;
    }
    if (searchContext.guests) {
      const guestCount = parseInt(searchContext.guests);
      message += ` for ${guestCount} ${guestCount > 1 ? 'people' : 'person'}`;
    }
    message += ". Can you help me find the perfect place?";
    return message;
  };

  const handleOpenChat = () => {
    console.log('🎯 Plan with AI button clicked!');
    
    if (showAiChat) {
      console.log('🚫 Chat already open, ignoring click');
      return;
    }
    
    // Clear relevant localStorage items for a fresh start
    localStorage.removeItem('chatHistory');
    localStorage.removeItem('conversationState'); // Keep if you want to persist some AI state across sessions
    localStorage.removeItem('ai_chat_trigger');
    localStorage.removeItem('ai_chat_trigger_processed');
    localStorage.removeItem('ai_chat_initial_user_message'); // Clear any previous one
    
    console.log('🧹 Cleared existing localStorage data for AI chat trigger');
    
    const messageContent = getInitialMessage();
    
    // Validate the message content (already good)
    const finalMessageContent = (messageContent && messageContent.trim()) ? 
      messageContent.trim() : 
      "Hi! I'd like help finding a place to stay. Can you help me explore my options?";
    
    console.log('💬 Setting initial user message for AI Chat:', finalMessageContent);
    localStorage.setItem('ai_chat_initial_user_message', finalMessageContent);
    localStorage.setItem('ai_chat_trigger', 'true');
    console.log('🚀 Set trigger flag and initial message for AiChat to pick up');
    
    // Show the chat
    // Small delay might not be strictly necessary anymore but doesn't hurt
    setTimeout(() => {
      setShowAiChat(true);
      console.log('✨ Chat modal should now be visible');
    }, 50);
  };

  const handleCloseChat = () => {
    setShowAiChat(false);
    // Clean up chat-related data from localStorage
    localStorage.removeItem('chatHistory');
    localStorage.removeItem('ai_chat_trigger');
    localStorage.removeItem('ai_chat_trigger_processed');
    // Don't remove conversationState to preserve preferences between sessions
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section with Background */}
      <div className="relative min-h-[90vh] flex items-center">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 z-0">
          <div 
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: `url('/images/hero-travel.jpg')`,
              backgroundPosition: 'center 40%'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-blue-900/40 backdrop-blur-[2px]" />
        </div>

        {/* Content */}
        <div className="container mx-auto px-4 py-12 relative z-10">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl drop-shadow-md">
              Discover your ideal stay
            </h1>
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-white/20"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 text-white/90 bg-transparent">with</span>
              </div>
            </div>
            <h1 className={cn(
              "text-3xl font-bold tracking-tight sm:text-5xl",
              "bg-clip-text text-transparent bg-gradient-to-r from-blue-200 via-blue-300 to-blue-400",
              "drop-shadow-[0_2px_15px_rgba(59,130,246,0.7)]",
              "drop-shadow-[0_0px_30px_rgba(147,197,253,0.5)]",
              "drop-shadow-[0_0px_70px_rgba(59,130,246,0.4)]",
              "filter brightness-150",
              "relative"
            )}>
              <span className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-600/20 blur-2xl" />
              AI-Powered Intelligence
            </h1>
            <p className="mt-4 text-xl text-white/90 max-w-2xl mx-auto drop-shadow-md">
              RoomLamAI is your travel agent, curating perfect options, tailored just for you.
            </p>

            <Card className={cn(
              "mt-12 p-8 shadow-2xl",
              "bg-white/95 backdrop-blur-md",
              "border border-white/20",
              "animate-fade-in-up"
            )}>
              <div className="space-y-8">
                <div className="w-full">
                  <SearchForm 
                    onSubmit={handleSearchSubmit} 
                    formRef={searchFormRef}
                    onChange={handleFormUpdate}
                  />
                </div>

                {searchHistory.length > 0 && (
                  <div className="border-t pt-6">
                    <RecentSearches
                      searches={searchHistory}
                      onSelect={repeatSearch}
                      onClear={clearHistory}
                      variant="compact"
                    />
                  </div>
                )}

                <div className="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8">
                  <Button
                    size="lg"
                    className={cn(
                      "bg-blue-600 hover:bg-blue-700 text-white",
                      "px-8 py-6 text-lg rounded-full",
                      "shadow-lg hover:shadow-blue-500/30",
                      "transition-all duration-200 ease-in-out",
                      "transform hover:scale-105",
                      "flex items-center justify-center gap-3"
                    )}
                    onClick={() => searchFormRef.current?.submit()}
                  >
                    <SearchIcon className="w-5 h-5" />
                    <span>Search Properties</span>
                  </Button>

                  <div className="relative hidden md:block">
                    <div className="absolute inset-y-0 left-1/2 -translate-x-1/2 w-px bg-gray-200"></div>
                    <div className="absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                      <span className="px-2 text-gray-500 bg-white text-sm">or</span>
                    </div>
                  </div>

                  <Button
                    size="lg"
                    className={cn(
                      "bg-blue-600 hover:bg-blue-700 text-white",
                      "px-8 py-6 text-lg rounded-full",
                      "shadow-lg hover:shadow-blue-500/30",
                      "transition-all duration-200 ease-in-out",
                      "transform hover:scale-105",
                      "flex items-center justify-center gap-3"
                    )}
                    onClick={handleOpenChat}
                  >
                    <MessageCircle className="w-5 h-5" />
                    <span>Plan with AI</span>
                    <span className="text-sm text-blue-200/90">• AI-Powered</span>
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Recent Searches Section */}
      {searchHistory.length > 0 && (
        <div className="bg-white py-12">
          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto">
              <h2 className="text-2xl font-semibold mb-6">Recent Searches</h2>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {searchHistory.slice(0, 3).map((search, index) => (
                  <Card 
                    key={index}
                    className="relative overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => repeatSearch(search)}
                  >
                    <div className="h-36">
                      <Map
                        properties={[]}
                        center={{ lat: search.lat, lng: search.lng }}
                        zoom={12}
                        className="w-full h-full"
                      />
                    </div>
                    <div className="p-4">
                      <div className="flex items-start gap-3">
                        <MapPin className="h-4 w-4 mt-1 shrink-0 text-primary" />
                        <div>
                          <div className="font-medium">{search.locationName}</div>
                          <div className="text-sm text-muted-foreground mt-1">
                            {new Date(search.checkIn).toLocaleDateString()} - {new Date(search.checkOut).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {search.guests} guests, {search.rooms} {search.rooms === 1 ? 'room' : 'rooms'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* How it Works Section */}
      <div className="bg-gradient-to-b from-gray-100 to-white py-24">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-semibold text-center mb-12 text-gray-800">
            How RoomLama Works For You
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white">
              <div className="bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4">
                <MessageCircle className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2 text-gray-800">Tell Us Your Preferences</h3>
              <p className="text-gray-600">Share your travel plans and preferences with our AI assistant</p>
            </Card>
            <Card className="p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white">
              <div className="bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4">
                <SearchIcon className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2 text-gray-800">Smart Recommendations</h3>
              <p className="text-gray-600">Get personalized suggestions based on your needs</p>
            </Card>
            <Card className="p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white">
              <div className="bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4">
                <Star className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2 text-gray-800">Book with Confidence</h3>
              <p className="text-gray-600">Choose from curated options that match your criteria</p>
            </Card>
          </div>
        </div>
      </div>

      {/* Featured Properties Section */}
      <div className="bg-gradient-to-b from-white via-gray-50 to-gray-100 py-24">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-semibold text-center mb-8 text-gray-800">
            Featured Properties
          </h2>
          <FeaturedProperties />
        </div>
      </div>

      {showAiChat && (
        <>
          <div 
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[999]"
            onClick={handleCloseChat}
          />
          <div className="fixed inset-0 z-[1000] flex items-center justify-center p-4">
            <AiChatEnhanced
              context={{
                ...searchContext,
                // Pass search history as part of context
                searchHistory: searchHistory.slice(0, 3)
              }}
              onClose={handleCloseChat}
            />
          </div>
        </>
      )}
    </div>
  );
}