import React from 'react';
import TestResultsVisualizer from '@/components/TestResultsVisualizer';
import { TestRunControls } from '@/components/TestRunControls';
import { TestLogOutput } from '@/components/TestLogOutput';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Shield, Activity, PlayCircle, FileText } from 'lucide-react';

export default function TestHub() {
  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Test Hub</h1>
          <p className="text-muted-foreground">
            Admin interface for running tests and viewing results
          </p>
        </div>
        <div className="flex items-center">
          <Shield className="h-5 w-5 mr-1 text-blue-500" />
          <span className="text-sm font-semibold">Admin Access</span>
        </div>
      </div>

      <Tabs defaultValue="run-tests" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="run-tests">
            <PlayCircle className="h-4 w-4 mr-2" />
            Run Tests
          </TabsTrigger>
          <TabsTrigger value="test-results">
            <Activity className="h-4 w-4 mr-2" />
            Test Results
          </TabsTrigger>
          <TabsTrigger value="test-documentation">
            <FileText className="h-4 w-4 mr-2" />
            Documentation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="run-tests" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <TestRunControls />
            <TestLogOutput />
          </div>
        </TabsContent>

        <TabsContent value="test-results">
          <TestResultsVisualizer />
        </TabsContent>

        <TabsContent value="test-documentation">
          <Card>
            <CardHeader>
              <CardTitle>Test Documentation</CardTitle>
              <CardDescription>
                Information about the testing framework and available tests
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium">Test Types</h3>
                <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <TestTypeCard
                    title="Unit Tests"
                    description="Tests for individual functions and components in isolation"
                    command="npm run test:unit"
                    testCount={42}
                  />
                  <TestTypeCard
                    title="Functional Tests"
                    description="Tests for complete user flows and API endpoints"
                    command="npm run test:functional"
                    testCount={18}
                  />
                  <TestTypeCard
                    title="React Component Tests"
                    description="Tests for React components with testing-library"
                    command="npm run test:react"
                    testCount={23}
                  />
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium">Testing Guidelines</h3>
                <ul className="mt-2 space-y-2 list-disc pl-6">
                  <li>Always run tests before submitting a pull request</li>
                  <li>Keep unit tests small and focused on a single function or component</li>
                  <li>Functional tests should cover key user journeys</li>
                  <li>Component tests should test behavior, not implementation details</li>
                  <li>Ensure all tests run in isolation and don't depend on other tests</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function TestTypeCard({ 
  title, 
  description, 
  command, 
  testCount 
}: { 
  title: string; 
  description: string; 
  command: string; 
  testCount: number;
}) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-2">{description}</p>
        <div className="bg-muted px-3 py-1 rounded text-sm font-mono mb-2">
          {command}
        </div>
        <div className="text-sm flex items-center">
          <span className="font-medium">{testCount}</span>
          <span className="text-muted-foreground ml-1">tests</span>
        </div>
      </CardContent>
    </Card>
  );
}