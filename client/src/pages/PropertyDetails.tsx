import { useParams, useLocation } from "wouter";
import { useProperty, usePropertyAvailability, useCreateReservation } from "@/lib/api.js";
import { Button } from "@/components/ui/button.jsx";
import { Card, CardContent } from "@/components/ui/card.jsx";
import { Skeleton } from "@/components/ui/skeleton.jsx";
import { Alert, AlertDescription } from "@/components/ui/alert.jsx";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog.jsx";
import { ChevronLeft, ChevronRight, X, ChevronDown, MapIcon, Share2, Wifi, Car as Parking, Coffee, Bath, Tv, BedDouble, CalendarX, Filter, SortAsc, ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Map from "@/components/Map";
import { DatePickerWithRange } from "@/components/ui/date-range-picker.jsx";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select.jsx";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible.jsx";
import { addDays, format } from "date-fns";
import { useState, useMemo, useEffect, useCallback } from "react";
import type { Room, RatePlan, Property, PropertyImage } from "@/types/schema.js";
import type { DateRange } from "react-day-picker";
import { Badge } from "@/components/ui/badge.jsx";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { getFullSizeImageUrl, getImageSource } from "@/utils/imageUtils.js";
import styled from "@emotion/styled";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface ExtendedRoom extends Omit<Room, 'currency'> {
  ratePlanCode: string;
  ratePlanDescription: string;
  totalAmount: number;
  originalAmount?: number;
  totalDiscount?: number;
  currency: string;
  description: string;
  maxOccupancy?: number;
  amenities?: string[];
  bedTypeCode?: string;
}

interface GroupedRoom {
  code: string;
  description: string;
  amenities?: string[];
  maxOccupancy?: number;
  bedTypeCode?: string;
  ratePlans: ExtendedRoom[];
}

const RoomGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  padding: 24px 0;

  /* This ensures all cards in a row have the same height */
  & > * {
    height: 100%;
  }
`;

const RoomCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .room-header {
    padding: 20px;
    border-bottom: 1px solid #eee;

    .room-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .room-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #666;
      font-size: 0.9rem;
      margin-bottom: 12px;
    }

    .room-amenities {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .rates-section {
    padding: 20px;

    .rate-option {
      padding: 16px;
      border: 1px solid #eee;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: #0066cc;
        background: #f8f9ff;
      }

      &.selected {
        border-color: #0066cc;
        background: #f0f4ff;
      }

      .rate-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
      }

      .rate-description {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 12px;
      }

      .rate-badges {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;
      }

      .price-display {
        text-align: right;

        .original-price {
          color: #999;
          text-decoration: line-through;
          font-size: 0.9rem;
          margin-bottom: 4px;
        }

        .current-price {
          font-size: 1.25rem;
          font-weight: 600;
          color: #0066cc;
        }

        .price-details {
          color: #666;
          font-size: 0.8rem;
        }
      }
    }
  }

  .reserve-button {
    margin-top: 20px;
    height: 48px;
    font-size: 1rem;
    font-weight: 500;
    background: #0066cc;
    color: white;
    transition: all 0.2s ease;

    &:hover {
      background: #0052a3;
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
`;

const MapButton = styled.button`
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 50;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  border-radius: 24px;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  animation: slideIn 0.3s ease-out;
  
  &:hover {
    background: hsl(var(--primary)/0.9);
    transform: translateY(-2px);
  }

  @keyframes slideIn {
    from {
      transform: translateY(100px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
`;

const FullScreenMap = styled(Dialog)`
  .dialog-content {
    width: 100vw;
    height: 100vh;
    max-width: none;
    padding: 0;
    margin: 0;
  }
`;

const AMENITY_ICONS: Record<string, React.ReactNode> = {
  'WiFi': <Wifi className="w-4 h-4" />,
  'Parking': <Parking className="w-4 h-4" />,
  'Breakfast': <Coffee className="w-4 h-4" />,
  'Private Bathroom': <Bath className="w-4 h-4" />,
  'TV': <Tv className="w-4 h-4" />,
  'King Bed': <BedDouble className="w-4 h-4" />,
  'Queen Bed': <BedDouble className="w-4 h-4" />
};

const calculateNights = (from: Date, to: Date) => {
  return Math.ceil((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24));
};

const SearchSection = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 32px;

  .search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
    }

    .search-summary {
      color: #666;
      font-size: 0.9rem;
    }
  }

  .search-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .search-button {
    width: 100%;
    height: 48px;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }
`;

const RoomSection = styled.div`
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      font-size: 1.75rem;
      font-weight: 600;
    }

    .filter-controls {
      display: flex;
      gap: 12px;
    }
  }

  .room-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  color: #0066cc;
  font-weight: 500;
  padding: 8px 0;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #0052a3;
    transform: translateX(-4px);
  }

  svg {
    width: 18px;
    height: 18px;
  }
`;

export default function PropertyDetails() {
  const { id } = useParams();
  const [_, navigate] = useLocation();
  const { data: property, isLoading: isLoadingProperty, error: propertyError } = useProperty(id || "");
  const createReservation = useCreateReservation();
  const { toast } = useToast();
  
  console.log('PropertyDetails page loaded', {
    id,
    hasProperty: !!property,
    isLoading: isLoadingProperty,
    error: propertyError,
    url: window.location.href
  });

  // Get session ID for context tracking
  const sessionId = useMemo(() => {
    const savedSessionId = localStorage.getItem('booking_session_id');
    if (savedSessionId) return savedSessionId;
    
    const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    localStorage.setItem('booking_session_id', newSessionId);
    return newSessionId;
  }, []);

  // Get search parameters from URL for back navigation
  const searchParams = useMemo(() => {
    if (typeof window === 'undefined') return null;
    return new URLSearchParams(window.location.search);
  }, []);

  // Date and guest selection state
  const [dates, setDates] = useState<DateRange>(() => {
    // Initialize dates from URL if available
    const checkIn = searchParams?.get('checkIn');
    const checkOut = searchParams?.get('checkOut');
    return {
      from: checkIn ? new Date(checkIn + 'T00:00:00') : undefined,
      to: checkOut ? new Date(checkOut + 'T00:00:00') : undefined
    };
  });
  const [guests, setGuests] = useState(() => searchParams?.get('guests') || "2");
  const [rooms, setRooms] = useState(() => searchParams?.get('rooms') || "1");
  const [selectedRatePlans, setSelectedRatePlans] = useState<Record<string, string>>({});
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAmenitiesOpen, setIsAmenitiesOpen] = useState(false);
  const [searchEnabled, setSearchEnabled] = useState(true);
  const [showMap, setShowMap] = useState(false);

  // Create availability search params
  const availabilityParams = useMemo(() => {
    if (!dates.from || !dates.to) return null;

    const params = new URLSearchParams({
      checkIn: dates.from.toISOString().split('T')[0],
      checkOut: dates.to.toISOString().split('T')[0],
      guests,
      rooms
    });

    return params;
  }, [dates.from, dates.to, guests, rooms, searchEnabled]);

  const { 
    data: availability,
    isLoading: isLoadingAvailability,
    error: availabilityError,
    refetch: searchAvailability
  } = usePropertyAvailability(id, availabilityParams, false);

  // Update search params display
  const [displayedParams, setDisplayedParams] = useState({
    dates: dates,
    guests,
    rooms,
  });

  // Initialize search on mount only
  useEffect(() => {
    if (dates.from && dates.to && guests && rooms) {
      searchAvailability();
      setDisplayedParams({ dates, guests, rooms });
    }
  }, []);
  
  // Record property view to context service on page load
  useEffect(() => {
    if (id && parseInt(id, 10) > 0) {
      // Record property view with context service
      const recordView = async () => {
        try {
          await fetch('/api/context/property-view', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sessionId, propertyId: parseInt(id, 10) })
          });
        } catch (error) {
          console.error('Failed to record property view:', error);
        }
      };
      
      recordView();
    }
  }, [id, sessionId]);

  // Handle availability data changes
  useEffect(() => {
    if (availability?.ratePlans && Object.keys(availability.ratePlans).length > 0) {
      setSearchEnabled(true);
      setDisplayedParams({ dates, guests, rooms });
    }
  }, [availability]);


  // Process available rate plans and rooms
  const { ratePlans, availableRooms, groupedRooms, lowestPriceRatePlan } = useMemo(() => {
    if (!availability?.ratePlans) {
      console.log('No rate plans available:', availability);
      return { 
        ratePlans: [] as RatePlan[], 
        availableRooms: [] as ExtendedRoom[], 
        groupedRooms: [] as GroupedRoom[],
        lowestPriceRatePlan: null as ExtendedRoom | null
      };
    }

    console.log('Processing rate plans:', availability.ratePlans);
    
    const plans: RatePlan[] = [];
    const rooms: ExtendedRoom[] = [];
    const groupedMap: Record<string, GroupedRoom> = {};
    let overallLowestPrice: ExtendedRoom | null = null;

    // Helper function to normalize room description for grouping
    const normalizeDescription = (desc: string) => {
      return desc.toLowerCase()
        .replace(/\s+/g, ' ')
        .trim()
        .replace(/\b(room|studio|suite)\b/g, '')
        .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '')
        .trim();
    };

    // First pass: Collect unique rooms by normalized description
    Object.entries(availability.ratePlans).forEach(([planCode, plan]) => {
      if (plan.rooms) {
        Object.entries(plan.rooms).forEach(([roomCode, room]) => {
          if (room.totalAmount !== undefined) {
            const description = room.description || 'Standard Room';
            const normalizedDesc = normalizeDescription(description);
            
            if (!groupedMap[normalizedDesc]) {
              groupedMap[normalizedDesc] = {
                code: roomCode,
                description: description,
                amenities: room.amenities,
                maxOccupancy: room.maxOccupancy,
                bedTypeCode: room.bedTypeCode,
                ratePlans: []
              };
            }
          }
        });
      }
    });

    // Second pass: Add rate plans to rooms
    Object.entries(availability.ratePlans).forEach(([planCode, plan]) => {
      plans.push({ ...plan, code: planCode });
      
      if (plan.rooms) {
        Object.entries(plan.rooms).forEach(([roomCode, room]) => {
          if (room.totalAmount !== undefined) {
            const description = room.description || 'Standard Room';
            const normalizedDesc = normalizeDescription(description);
            const groupedRoom = groupedMap[normalizedDesc];
            
            if (groupedRoom) {
              const extendedRoom: ExtendedRoom = {
                ...room,
                code: roomCode,
                ratePlanCode: planCode,
                ratePlanDescription: plan.description,
                totalAmount: room.totalAmount,
                originalAmount: room.totalAmount + (room.totalDiscount || 0),
                totalDiscount: room.totalDiscount,
                retailDiscountPercent: room.retailDiscountPercent,
                currency: room.currency || 'USD',
                description: description,
                maxOccupancy: room.maxOccupancy || groupedRoom.maxOccupancy,
                amenities: room.amenities || groupedRoom.amenities,
                bedTypeCode: room.bedTypeCode || groupedRoom.bedTypeCode
              };

              // Check if this rate plan is already added (avoid duplicates)
              const isDuplicate = groupedRoom.ratePlans.some(
                rp => rp.ratePlanCode === planCode && rp.totalAmount === room.totalAmount
              );

              if (!isDuplicate) {
                // Track overall lowest price
                if (!overallLowestPrice || extendedRoom.totalAmount < overallLowestPrice.totalAmount) {
                  overallLowestPrice = extendedRoom;
                }

                groupedRoom.ratePlans.push(extendedRoom);
                rooms.push(extendedRoom);
                console.log(`Added rate plan ${planCode} to room description: ${description}`, extendedRoom);
              }
            }
          }
        });
      }
    });

    // Sort rate plans within each room by price
    Object.values(groupedMap).forEach(group => {
      group.ratePlans.sort((a, b) => a.totalAmount - b.totalAmount);
      console.log(`Sorted rate plans for room ${group.description}:`, group.ratePlans);
    });

    // Convert grouped rooms to array and sort by lowest rate
    const sortedGroupedRooms = Object.values(groupedMap)
      .map(group => ({
        ...group,
        lowestRate: Math.min(...group.ratePlans.map(rp => rp.totalAmount))
      }))
      .sort((a, b) => a.lowestRate - b.lowestRate)
      .map(({ lowestRate, ...group }) => group);

    console.log('Final grouped rooms:', sortedGroupedRooms);
    console.log('Overall lowest price rate plan:', overallLowestPrice);

    return {
      ratePlans: plans,
      availableRooms: rooms,
      groupedRooms: sortedGroupedRooms,
      lowestPriceRatePlan: overallLowestPrice
    } as { 
      ratePlans: RatePlan[]; 
      availableRooms: ExtendedRoom[]; 
      groupedRooms: GroupedRoom[];
      lowestPriceRatePlan: ExtendedRoom | null;
    };
  }, [availability]);

  // Reset search when params change
  useEffect(() => {
    setSearchEnabled(false);
  }, [dates.from, dates.to, guests, rooms]);

  // Get selected room details
  const selectedRoomDetails = useMemo(() => {
    if (!selectedRoom || !selectedRatePlans[selectedRoom]) return null;
    return availableRooms.find(
      room => room.code === selectedRoom && room.ratePlanCode === selectedRatePlans[selectedRoom]
    );
  }, [selectedRoom, selectedRatePlans, availableRooms]);

  // Track booking attempt with context service
  const recordBookingAttempt = useCallback(async (propertyId: number, completed: boolean = false) => {
    try {
      await fetch('/api/context/booking-attempt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, propertyId, completed })
      });
    } catch (error) {
      console.error('Failed to record booking attempt:', error);
    }
  }, [sessionId]);

  // Handle navigation to reservation form
  const handleReserveNow = (room: ExtendedRoom) => {
    if (!dates.from || !dates.to) return;

    // Record booking attempt to context service
    if (id) {
      recordBookingAttempt(parseInt(id, 10), false);
    }

    const searchParams = new URLSearchParams({
      propertyId: id || '',
      roomCode: room.code || '',
      ratePlanCode: room.ratePlanCode || '',
      checkIn: dates.from.toISOString().split('T')[0],
      checkOut: dates.to.toISOString().split('T')[0],
      guests: guests || '1',
      rooms: rooms || '1',
      rate: room.totalAmount.toString(),
      currency: room.currency,
      totalAmount: room.totalAmount.toString(),
      totalCurrency: room.currency
    });

    navigate(`/reservation?${searchParams.toString()}`);
  };

  // Helper function to get image URL
  const getImageUrl = (image: string | PropertyImage | undefined): string => {
    const url = getImageSource(image);
    return url ? getFullSizeImageUrl(url) : "";
  };

  // Handle date changes
  const handleDateChange = (newDates: DateRange) => {
    setDates(newDates);
    // Reset search when dates change
    setSearchEnabled(false);
  };

  // Helper function to ensure currency is a string
  const getCurrency = (currency: any): string => {
    return typeof currency === 'string' ? currency : 'USD';
  };

  const handleOpenMap = () => setShowMap(true);
  const handleCloseMap = () => setShowMap(false);

  const handleShare = async () => {
    if (!property?.name) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: property.name,
          text: `Check out ${property.name} on our platform!`,
          url: window.location.href
        });
      } catch (error: unknown) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('Error sharing:', error);
        }
      }
    } else if (navigator.clipboard) {
      await navigator.clipboard.writeText(window.location.href);
      toast({
        title: "Link copied to clipboard",
        description: "You can now share this property with others"
      });
    }
  };

  // Handle back navigation to search results
  const handleBackToSearch = () => {
    // Preserve the search parameters when navigating back
    const backUrl = searchParams ? `/search?${searchParams.toString()}` : '/search';
    navigate(backUrl);
  };

  if (isLoadingProperty) {
    return <Skeleton className="w-full h-screen" />;
  }

  if (propertyError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load property details: {propertyError instanceof Error ? propertyError.message : "Unknown error"}
        </AlertDescription>
      </Alert>
    );
  }

  if (!property) {
    return <div>Property not found</div>;
  }

  console.log('Property data:', {
    hasImages: !!property.images,
    imagesLength: property.images?.length,
    imagesType: property.images ? typeof property.images : 'undefined',
    isArray: Array.isArray(property.images),
    firstImage: property.images?.[0],
  });

  console.log('Rendering with grouped rooms:', groupedRooms);

  return (
    <div className="container mx-auto px-4 py-8">
      <BackButton onClick={handleBackToSearch}>
        <ArrowLeft />
        Back to Search Results
      </BackButton>

      {availabilityError && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>
            Failed to load availability: {availabilityError instanceof Error ? availabilityError.message : "Unknown error"}
          </AlertDescription>
        </Alert>
      )}

      {property.images && Array.isArray(property.images) && property.images.length > 0 && (() => {
        const validImages = property.images.filter(img => {
          const url = getImageUrl(img);
          return url && url !== '/placeholder-property.jpg';
        });

        if (validImages.length === 0) return null;

        return (
          <div className="mb-8">
            <div className="grid grid-cols-4 gap-2 h-[500px]">
              {/* Main Image */}
              <div 
                className="col-span-2 row-span-2 relative cursor-pointer"
                onClick={() => {
                  setCurrentImageIndex(0);
                  setShowImageGallery(true);
                }}
              >
                <img
                  src={getImageUrl(validImages[0])}
                  alt={`${property.name} - Main`}
                  className="w-full h-full object-cover rounded-l-lg"
                />
              </div>

              {/* Thumbnail Grid */}
              {validImages.slice(1, 5).map((image, index) => (
                <div 
                  key={index}
                  className="relative h-[248px] cursor-pointer"
                  onClick={() => {
                    setCurrentImageIndex(index + 1);
                    setShowImageGallery(true);
                  }}
                >
                  <img
                    src={getImageUrl(image)}
                    alt={`${property.name} - ${index + 2}`}
                    className={`w-full h-full object-cover ${
                      index === 1 ? 'rounded-tr-lg' : ''
                    } ${index === 3 ? 'rounded-br-lg' : ''}`}
                  />
                  {/* View All Button - show on last visible image */}
                  {validImages.length > 5 && index === 3 && (
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowImageGallery(true);
                        }}
                        className="bg-white/90 hover:bg-white text-black px-4 py-2 rounded-full shadow-lg transition-colors"
                      >
                        View all {validImages.length} photos
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );
      })()}

      <SearchSection>
        <div className="search-header">
          <h2>Search Availability</h2>
          {displayedParams.dates.from && displayedParams.dates.to && (
            <div className="search-summary">
              {calculateNights(displayedParams.dates.from, displayedParams.dates.to)} night
              {calculateNights(displayedParams.dates.from, displayedParams.dates.to) !== 1 ? 's' : ''} · 
              {displayedParams.guests} {parseInt(displayedParams.guests) === 1 ? 'guest' : 'guests'} · 
              {displayedParams.rooms} {parseInt(displayedParams.rooms) === 1 ? 'room' : 'rooms'}
            </div>
          )}
        </div>

        <div className="search-controls">
          <DatePickerWithRange
            date={dates}
            onChange={handleDateChange}
          />
          <Select value={guests} onValueChange={setGuests}>
            <SelectTrigger>
              <SelectValue placeholder="Guests" />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3, 4].map(num => (
                <SelectItem key={num} value={num.toString()}>
                  {num} {num === 1 ? 'Guest' : 'Guests'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={rooms} onValueChange={setRooms}>
            <SelectTrigger>
              <SelectValue placeholder="Rooms" />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3].map(num => (
                <SelectItem key={num} value={num.toString()}>
                  {num} {num === 1 ? 'Room' : 'Rooms'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button 
          className="search-button"
          onClick={() => {
            setSearchEnabled(true);
            searchAvailability();
          }}
          disabled={!dates.from || !dates.to}
        >
          {dates.from && dates.to ? 'Update Rates' : 'Select Dates'}
        </Button>
      </SearchSection>

      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold">{property.name}</h1>
            <p className="text-gray-600 mt-2">{property.description}</p>
          </div>
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleShare}
          >
            <Share2 className="h-4 w-4" />
            <span className="sr-only">Share property</span>
          </Button>
        </div>

        {property.amenities && property.amenities.length > 0 && (
          <Card className="mb-8">
            <Collapsible
              open={isAmenitiesOpen}
              onOpenChange={setIsAmenitiesOpen}
              className="p-6"
            >
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">Property Amenities</h2>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="w-9 p-0">
                    <ChevronDown className={`h-4 w-4 transition-transform ${isAmenitiesOpen ? 'transform rotate-180' : ''}`} />
                    <span className="sr-only">Toggle amenities</span>
                  </Button>
                </CollapsibleTrigger>
              </div>

              <CollapsibleContent className="mt-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {property.amenities.map((amenity: string) => (
                    <div key={amenity} className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-primary rounded-full" />
                      <span>{amenity}</span>
                    </div>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        )}
      </div>

      <RoomSection>
        <div className="section-header">
          <h2>Available Rooms</h2>
          <div className="filter-controls">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <SortAsc className="h-4 w-4 mr-2" />
              Sort
            </Button>
          </div>
        </div>

        {isLoadingAvailability ? (
          <div className="py-8">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/4"></div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-96 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <>
            {!dates.from || !dates.to ? (
              <Alert className="mb-8">
                <AlertDescription>
                  Please select your stay dates to see room availability and rates.
                </AlertDescription>
              </Alert>
            ) : searchEnabled && groupedRooms.length === 0 ? (
              <Alert className="mb-8">
                <AlertDescription>
                  No rooms available for the selected dates. Please try different dates.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="room-grid">
                {groupedRooms.map((room: GroupedRoom) => (
                  <RoomCard key={room.code}>
                    <div className="room-header">
                      <h3 className="room-title">{room.description}</h3>
                      <div className="room-meta">
                        {room.maxOccupancy && (
                          <span>{room.maxOccupancy} {room.maxOccupancy === 1 ? 'Guest' : 'Guests'}</span>
                        )}
                        {room.bedTypeCode && (
                          <>
                            <span>·</span>
                            <span>
                              {room.bedTypeCode === 'K' ? 'King Bed' : 
                               room.bedTypeCode === 'Q' ? 'Queen Bed' : 
                               room.bedTypeCode === 'D' ? 'Double Bed' : 
                               room.bedTypeCode === 'T' ? 'Twin Beds' : 
                               room.bedTypeCode}
                            </span>
                          </>
                        )}
                      </div>
                      {room.amenities && room.amenities.length > 0 && (
                        <div className="room-amenities">
                          {room.amenities.slice(0, 3).map(amenity => (
                            <Badge key={amenity} variant="outline" className="flex items-center gap-1">
                              {AMENITY_ICONS[amenity] || null}
                              <span>{amenity}</span>
                            </Badge>
                          ))}
                          {room.amenities.length > 3 && (
                            <Badge variant="outline">+{room.amenities.length - 3} more</Badge>
                          )}
                        </div>
                      )}
                    </div>

                    <RadioGroup 
                      value={selectedRatePlans[room.code] || ''}
                      onValueChange={(value) => {
                        setSelectedRatePlans({
                          ...selectedRatePlans,
                          [room.code]: value
                        });
                        setSelectedRoom(room.code);
                      }}
                      className="rates-section"
                    >
                      {room.ratePlans.map((ratePlan, idx) => {
                        const isLowestPrice = idx === 0;
                        const isSelected = selectedRatePlans[room.code] === ratePlan.ratePlanCode;
                        
                        return (
                          <div
                            key={ratePlan.ratePlanCode}
                            className={`rate-option ${isSelected ? 'selected' : ''}`}
                            onClick={() => {
                              setSelectedRatePlans({
                                ...selectedRatePlans,
                                [room.code]: ratePlan.ratePlanCode
                              });
                              setSelectedRoom(room.code);
                            }}
                          >
                            <div className="rate-header">
                              <div className="flex items-center gap-2">
                                <RadioGroupItem 
                                  value={ratePlan.ratePlanCode} 
                                  id={`${room.code}-${ratePlan.ratePlanCode}`}
                                />
                                <label 
                                  htmlFor={`${room.code}-${ratePlan.ratePlanCode}`}
                                  className="font-medium"
                                >
                                  {ratePlan.ratePlanDescription || 'Standard Rate'}
                                </label>
                              </div>
                              {isLowestPrice && (
                                <Badge variant="default" className="lowest-price-badge">Best Price</Badge>
                              )}
                            </div>

                            <p className="rate-description">
                              {ratePlan.refundable !== false ? 
                                'Free cancellation until 24 hours before check-in' : 
                                'Non-refundable rate'}
                            </p>

                            <div className="rate-badges">
                              {ratePlan.refundable !== false ? (
                                <Badge variant="outline" className="cancellation-badge">Free Cancellation</Badge>
                              ) : (
                                <Badge variant="outline" className="cancellation-badge flex items-center gap-1">
                                  <CalendarX className="h-3 w-3" />
                                  <span>Non-refundable</span>
                                </Badge>
                              )}
                              
                              {ratePlan.retailDiscountPercent && ratePlan.retailDiscountPercent > 0 && (
                                <Badge variant="secondary" className="cancellation-badge">
                                  {Math.round(ratePlan.retailDiscountPercent)}% OFF
                                </Badge>
                              )}
                            </div>

                            <div className="price-display">
                              {ratePlan.retailDiscountPercent && ratePlan.retailDiscountPercent > 0 && ratePlan.originalAmount && (
                                <div className="original-price">
                                  {new Intl.NumberFormat('en-US', { 
                                    style: 'currency', 
                                    currency: getCurrency(ratePlan.currency)
                                  }).format(ratePlan.originalAmount)}
                                </div>
                              )}
                              <div className="current-price">
                                {new Intl.NumberFormat('en-US', { 
                                  style: 'currency', 
                                  currency: getCurrency(ratePlan.currency)
                                }).format(ratePlan.totalAmount)}
                              </div>
                              <div className="price-details">
                                {displayedParams.dates.from && displayedParams.dates.to && 
                                  `for ${calculateNights(displayedParams.dates.from, displayedParams.dates.to)} night${calculateNights(displayedParams.dates.from, displayedParams.dates.to) !== 1 ? 's' : ''}`}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </RadioGroup>

                    <div className="p-4">
                      <Button 
                        className="reserve-button w-full" 
                        disabled={!selectedRatePlans[room.code]}
                        onClick={() => {
                          if (selectedRatePlans[room.code]) {
                            const selectedRatePlan = room.ratePlans.find(
                              rp => rp.ratePlanCode === selectedRatePlans[room.code]
                            );
                            if (selectedRatePlan) {
                              handleReserveNow(selectedRatePlan);
                            }
                          }
                        }}
                      >
                        {selectedRatePlans[room.code] ? 'Reserve Now' : 'Select a rate option above'}
                      </Button>
                    </div>
                  </RoomCard>
                ))}
              </div>
            )}
          </>
        )}
      </RoomSection>
      
      {property.latitude && property.longitude && (
        <>
          <MapButton onClick={handleOpenMap}>
            <MapIcon className="h-5 w-5" />
            <span>View on Map</span>
          </MapButton>
          
          <FullScreenMap open={showMap} onOpenChange={setShowMap}>
            <DialogContent className="dialog-content">
              <DialogTitle className="sr-only">Property Location Map</DialogTitle>
              <Button 
                className="absolute top-4 right-4 z-10" 
                size="icon" 
                variant="secondary"
                onClick={handleCloseMap}
              >
                <X className="h-4 w-4" />
              </Button>
              <Map 
                properties={[{
                  ...property,
                  rates: selectedRoomDetails ? [{
                    code: selectedRoomDetails.code || '',
                    description: selectedRoomDetails.description || '',
                    rate: selectedRoomDetails.rate,
                    totalAmount: selectedRoomDetails.totalAmount || 0,
                    originalAmount: selectedRoomDetails.originalAmount || selectedRoomDetails.totalAmount || 0,
                    discountAmount: selectedRoomDetails.totalDiscount || 0,
                    discountPercent: selectedRoomDetails.retailDiscountPercent || 0,
                    currency: selectedRoomDetails.currency,
                    roomTypeCode: selectedRoomDetails.roomTypeCode || '',
                    bedTypeCode: selectedRoomDetails.bedTypeCode || '',
                    restrictedRate: selectedRoomDetails.restrictedRate || false,
                    refundable: selectedRoomDetails.refundable || false,
                    maxOccupancy: selectedRoomDetails.maxOccupancy || 1,
                    availableQuantity: selectedRoomDetails.availableQuantity || 1
                  }] : []
                }]} 
                center={{ lat: Number(property.latitude), lng: Number(property.longitude) }}
                zoom={15}
                isFullScreen
                className="w-full h-full"
              />
            </DialogContent>
          </FullScreenMap>
        </>
      )}
    </div>
  );
}
