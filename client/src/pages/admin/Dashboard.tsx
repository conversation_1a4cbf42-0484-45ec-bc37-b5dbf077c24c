import React from "react";
import { useLocation } from "wouter";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import AdminNav from "@/components/AdminNav";
import { Users, Tag, BarChart4, Server, Settings } from "lucide-react";

export default function AdminDashboard() {
  const [_, setLocation] = useLocation();

  const dashboardCards = [
    {
      title: "User Management",
      description: "View, create, update, and delete user accounts.",
      icon: <Users className="h-8 w-8 text-primary" />,
      onClick: () => setLocation("/admin/users"),
    },
    {
      title: "Promo Codes",
      description: "Manage promo codes and discounts for users.",
      icon: <Tag className="h-8 w-8 text-primary" />,
      onClick: () => setLocation("/admin/promo-codes"),
    },
    {
      title: "Analytics",
      description: "View site usage statistics and reports.",
      icon: <BarChart4 className="h-8 w-8 text-primary" />,
      onClick: () => setLocation("/admin/analytics"),
    },
    {
      title: "Test Hub",
      description: "Run and manage test cases for the application.",
      icon: <Server className="h-8 w-8 text-primary" />,
      onClick: () => setLocation("/debug/test-hub"),
    },
    {
      title: "Settings",
      description: "Configure site-wide settings and preferences.",
      icon: <Settings className="h-8 w-8 text-primary" />,
      onClick: () => setLocation("/admin/settings"),
      disabled: true,
    },
  ];

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-8">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Welcome to the RoomLama admin dashboard. Use the cards below to manage various aspects of the site.
          </p>
        </div>

        <AdminNav />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dashboardCards.map((card, index) => (
            <Card key={index} className={card.disabled ? "opacity-70" : ""}>
              <CardHeader>
                <div className="flex items-center gap-3">
                  {card.icon}
                  <CardTitle>{card.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  {card.description}
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={card.onClick} 
                  disabled={card.disabled}
                  className="w-full"
                >
                  {card.disabled ? "Coming Soon" : "Manage"}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}