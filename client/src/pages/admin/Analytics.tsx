import React from "react";
import { useQuery } from "@tanstack/react-query";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  BarChart,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveC<PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Users, Hotel, Search, Calendar, Percent, BarChart4 } from "lucide-react";
import AdminNav from "@/components/AdminNav";

// Types for our analytics data
interface OverviewMetrics {
  totalUsers: number;
  totalProperties: number;
  totalSearches: number;
  totalBookings: number;
  bookingConversionRate: number;
}

interface DateCount {
  date: string;
  count: number;
}

interface LocationCount {
  location: string;
  count: number;
}

interface MembershipCount {
  membershipType: string;
  count: number;
}

interface UserActivity {
  registrations: DateCount[];
  logins: DateCount[];
}

interface BookingActivity {
  bookingsByDay: DateCount[];
  bookingsByMembershipType: MembershipCount[];
}

interface SearchActivity {
  searchesByDay: DateCount[];
  topLocations: LocationCount[];
}

interface AnalyticsData {
  overview: OverviewMetrics;
  userActivity: UserActivity;
  bookingActivity: BookingActivity;
  searchActivity: SearchActivity;
}

// Analytics Dashboard component
export default function AdminAnalytics() {
  const { data, isLoading, error } = useQuery<AnalyticsData>({
    queryKey: ["/api/admin/analytics"],
    queryFn: async () => {
      const response = await fetch("/api/admin/analytics");
      if (!response.ok) {
        throw new Error("Failed to fetch analytics data");
      }
      return response.json();
    },
  });

  // Colors for charts
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];
  
  // Function to format dates for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }).format(date);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-8">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Monitor site activity, user engagement, and booking metrics.
          </p>
        </div>

        <AdminNav />

        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader className="pb-2">
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-1/3 mb-2" />
                  <Skeleton className="h-4 w-full" />
                </CardContent>
              </Card>
            ))}
            <Card className="col-span-1 md:col-span-2 lg:col-span-4">
              <CardHeader>
                <Skeleton className="h-5 w-1/4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[200px] w-full" />
              </CardContent>
            </Card>
          </div>
        )}

        {error && (
          <Card className="bg-destructive/10">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <p className="text-destructive font-semibold">Error loading analytics data</p>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                {error instanceof Error ? error.message : "An unknown error occurred"}
              </p>
            </CardContent>
          </Card>
        )}

        {data && (
          <>
            {/* Overview metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <MetricCard
                title="Total Users"
                value={data.overview.totalUsers}
                icon={<Users className="h-5 w-5 text-primary" />}
                description="Registered user accounts"
              />
              <MetricCard
                title="Properties"
                value={data.overview.totalProperties}
                icon={<Hotel className="h-5 w-5 text-primary" />}
                description="Listed accommodations"
              />
              <MetricCard
                title="Searches"
                value={data.overview.totalSearches}
                icon={<Search className="h-5 w-5 text-primary" />}
                description="Total location searches"
              />
              <MetricCard
                title="Bookings"
                value={data.overview.totalBookings}
                icon={<Calendar className="h-5 w-5 text-primary" />}
                description="Completed reservations"
              />
              <MetricCard
                title="Conversion"
                value={`${data.overview.bookingConversionRate.toFixed(1)}%`}
                icon={<Percent className="h-5 w-5 text-primary" />}
                description="Search to booking rate"
              />
            </div>

            {/* Charts */}
            <Tabs defaultValue="users" className="w-full">
              <TabsList className="grid w-full max-w-md grid-cols-3">
                <TabsTrigger value="users">User Activity</TabsTrigger>
                <TabsTrigger value="bookings">Bookings</TabsTrigger>
                <TabsTrigger value="searches">Searches</TabsTrigger>
              </TabsList>

              {/* User Activity Tab */}
              <TabsContent value="users" className="space-y-4 pt-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>User Registrations</CardTitle>
                      <CardDescription>New user accounts over time</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={data.userActivity.registrations.map(item => ({
                              ...item,
                              date: formatDate(item.date),
                            }))}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="count"
                              name="New Users"
                              stroke="#8884d8"
                              activeDot={{ r: 8 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>User Logins</CardTitle>
                      <CardDescription>Login activity trends</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={data.userActivity.logins.map(item => ({
                              ...item,
                              date: formatDate(item.date),
                            }))}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar
                              dataKey="count"
                              name="Logins"
                              fill="#82ca9d"
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Bookings Tab */}
              <TabsContent value="bookings" className="space-y-4 pt-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Booking Trends</CardTitle>
                      <CardDescription>Reservations per day</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={data.bookingActivity.bookingsByDay.map(item => ({
                              ...item,
                              date: formatDate(item.date),
                            }))}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="count"
                              name="Bookings"
                              stroke="#ff7300"
                              activeDot={{ r: 8 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Bookings by Membership</CardTitle>
                      <CardDescription>Distribution by user type</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={data.bookingActivity.bookingsByMembershipType}
                              cx="50%"
                              cy="50%"
                              labelLine={true}
                              outerRadius={100}
                              fill="#8884d8"
                              dataKey="count"
                              nameKey="membershipType"
                              label={({ membershipType, count, percent }) => 
                                `${membershipType}: ${(percent * 100).toFixed(0)}%`
                              }
                            >
                              {data.bookingActivity.bookingsByMembershipType.map(
                                (entry, index) => (
                                  <Cell
                                    key={`cell-${index}`}
                                    fill={COLORS[index % COLORS.length]}
                                  />
                                )
                              )}
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Searches Tab */}
              <TabsContent value="searches" className="space-y-4 pt-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Search Volume</CardTitle>
                      <CardDescription>Searches per day</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={data.searchActivity.searchesByDay.map(item => ({
                              ...item,
                              date: formatDate(item.date),
                            }))}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar
                              dataKey="count"
                              name="Searches"
                              fill="#8884d8"
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Popular Locations</CardTitle>
                      <CardDescription>Most searched destinations</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            layout="vertical"
                            data={data.searchActivity.topLocations}
                            margin={{ top: 5, right: 30, left: 50, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis type="number" />
                            <YAxis
                              type="category"
                              dataKey="location"
                              width={100}
                            />
                            <Tooltip />
                            <Legend />
                            <Bar
                              dataKey="count"
                              name="Searches"
                              fill="#0088FE"
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </div>
  );
}

// Simple metric card component
interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  description: string;
}

function MetricCard({ title, value, icon, description }: MetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}