import React, { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import AdminNav from "@/components/AdminNav";
import { 
  Loader2, 
  PlusCircle, 
  Edit, 
  Trash2, 
  Tag, 
  Power, 
  TimerOff, 
  Timer, 
  CheckCircle2, 
  XCircle
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { format, isAfter } from "date-fns";
import { Badge } from "@/components/ui/badge";

// PromoCode type that matches our backend schema
interface PromoCode {
  id: number;
  code: string;
  description: string;
  membershipType: string;
  discountPercent: number;
  maxUsages: number;
  usageCount: number;
  isActive: boolean;
  expiresAt: string | null;
  createdAt: string;
  updatedAt: string;
  createdBy: number | null;
}

// PromoCode form data type
interface PromoCodeFormData {
  code: string;
  description: string;
  membershipType: string;
  discountPercent: number;
  maxUsages: number;
  isActive: boolean;
  expiresAt: string | null;
}

export default function PromoCodes() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPromoCode, setSelectedPromoCode] = useState<PromoCode | null>(null);
  
  // Form state for creating/editing promo codes
  const [formData, setFormData] = useState<PromoCodeFormData>({
    code: "",
    description: "",
    membershipType: "standard",
    discountPercent: 10,
    maxUsages: 100,
    isActive: true,
    expiresAt: null,
  });

  // Fetch promo codes
  const { data: promoCodes, isLoading, error } = useQuery<PromoCode[]>({
    queryKey: ["/api/admin/promo-codes"],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/admin/promo-codes");
      return await res.json();
    },
  });

  // Create promo code mutation
  const createPromoCodeMutation = useMutation({
    mutationFn: async (promoCodeData: PromoCodeFormData) => {
      const res = await apiRequest("POST", "/api/admin/promo-codes", promoCodeData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Promo code created",
        description: "The promo code has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/promo-codes"] });
      setIsCreateDialogOpen(false);
      resetForm();
    },
    onError: (error: Error) => {
      toast({
        title: "Error creating promo code",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update promo code mutation
  const updatePromoCodeMutation = useMutation({
    mutationFn: async ({ id, promoCodeData }: { id: number; promoCodeData: Partial<PromoCodeFormData> }) => {
      const res = await apiRequest("PUT", `/api/admin/promo-codes/${id}`, promoCodeData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Promo code updated",
        description: "The promo code has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/promo-codes"] });
      setIsEditDialogOpen(false);
      resetForm();
    },
    onError: (error: Error) => {
      toast({
        title: "Error updating promo code",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete promo code mutation
  const deletePromoCodeMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/admin/promo-codes/${id}`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Promo code deleted",
        description: "The promo code has been deleted successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/promo-codes"] });
      setIsDeleteDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Error deleting promo code",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Toggle promo code active status mutation
  const togglePromoCodeStatusMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("PUT", `/api/admin/promo-codes/${id}/toggle`, {});
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Promo code status toggled",
        description: "The promo code status has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/promo-codes"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error toggling promo code status",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Generate test promo code mutation
  const generateTestPromoCodeMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", "/api/admin/promo-codes/generate-test", {});
      return await res.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Test promo code generated",
        description: `New promo code: ${data.code || 'TESTCODE'}`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/promo-codes"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error generating test promo code",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'number') {
      setFormData((prev) => ({ ...prev, [name]: Number(value) }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Handle date input changes
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setFormData((prev) => ({ 
      ...prev, 
      expiresAt: value ? value : null 
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isActive: checked }));
  };

  // Handle select changes
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Reset form to default values
  const resetForm = () => {
    setFormData({
      code: "",
      description: "",
      membershipType: "standard",
      discountPercent: 10,
      maxUsages: 100,
      isActive: true,
      expiresAt: null,
    });
    setSelectedPromoCode(null);
  };

  // Open the create dialog
  const openCreateDialog = () => {
    resetForm();
    setIsCreateDialogOpen(true);
  };

  // Open the edit dialog
  const openEditDialog = (promoCode: PromoCode) => {
    setSelectedPromoCode(promoCode);
    setFormData({
      code: promoCode.code,
      description: promoCode.description,
      membershipType: promoCode.membershipType,
      discountPercent: promoCode.discountPercent,
      maxUsages: promoCode.maxUsages,
      isActive: promoCode.isActive,
      expiresAt: promoCode.expiresAt,
    });
    setIsEditDialogOpen(true);
  };

  // Open the delete dialog
  const openDeleteDialog = (promoCode: PromoCode) => {
    setSelectedPromoCode(promoCode);
    setIsDeleteDialogOpen(true);
  };

  // Toggle promo code status
  const togglePromoCodeStatus = (id: number) => {
    togglePromoCodeStatusMutation.mutate(id);
  };

  // Handle create promo code form submission
  const handleCreatePromoCode = (e: React.FormEvent) => {
    e.preventDefault();
    createPromoCodeMutation.mutate(formData);
  };

  // Handle edit promo code form submission
  const handleUpdatePromoCode = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPromoCode) return;

    updatePromoCodeMutation.mutate({ id: selectedPromoCode.id, promoCodeData: formData });
  };

  // Handle delete promo code
  const handleDeletePromoCode = () => {
    if (!selectedPromoCode) return;
    deletePromoCodeMutation.mutate(selectedPromoCode.id);
  };

  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Never";
    try {
      return format(new Date(dateString), "MMM dd, yyyy HH:mm");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Check if promo code is expired
  const isExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    try {
      return isAfter(new Date(), new Date(expiresAt));
    } catch (error) {
      return false;
    }
  };

  // Get promo code status badge
  const getStatusBadge = (promoCode: PromoCode) => {
    if (!promoCode.isActive) {
      return <Badge variant="outline" className="bg-muted text-muted-foreground">Inactive</Badge>;
    }
    if (isExpired(promoCode.expiresAt)) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    if (promoCode.usageCount >= promoCode.maxUsages) {
      return <Badge variant="destructive">Exhausted</Badge>;
    }
    return <Badge variant="success" className="bg-green-100 text-green-800">Active</Badge>;
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <AdminNav />
        <div className="flex justify-center items-center min-h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <AdminNav />
        <div className="bg-destructive/10 p-4 rounded-md my-4">
          <p className="text-destructive">Error loading promo codes. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-8">
        <div>
          <h1 className="text-3xl font-bold">Promo Codes</h1>
          <p className="text-muted-foreground mt-2">
            Create, manage and track promotional codes for user registrations and discounts.
          </p>
        </div>

        <AdminNav />

        <div className="flex justify-end gap-2 mb-4">
          <Button 
            variant="outline" 
            onClick={() => generateTestPromoCodeMutation.mutate()}
            disabled={generateTestPromoCodeMutation.isPending}
          >
            {generateTestPromoCodeMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Generate Test Code
          </Button>
          <Button onClick={openCreateDialog}>
            <PlusCircle className="mr-2 h-4 w-4" /> Add Promo Code
          </Button>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Code</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Membership</TableHead>
                <TableHead>Discount</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {promoCodes && promoCodes.length > 0 ? (
                promoCodes.map((promoCode) => (
                  <TableRow key={promoCode.id}>
                    <TableCell className="font-mono font-medium">{promoCode.code}</TableCell>
                    <TableCell>{promoCode.description}</TableCell>
                    <TableCell className="capitalize">{promoCode.membershipType}</TableCell>
                    <TableCell>{promoCode.discountPercent}%</TableCell>
                    <TableCell>
                      {promoCode.usageCount} / {promoCode.maxUsages}
                    </TableCell>
                    <TableCell>{getStatusBadge(promoCode)}</TableCell>
                    <TableCell>{formatDate(promoCode.expiresAt)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => togglePromoCodeStatus(promoCode.id)}
                          title={promoCode.isActive ? "Deactivate" : "Activate"}
                        >
                          {promoCode.isActive ? (
                            <XCircle className="h-4 w-4 text-red-500" />
                          ) : (
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => openEditDialog(promoCode)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => openDeleteDialog(promoCode)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center h-24">
                    No promo codes found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Create Promo Code Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Promo Code</DialogTitle>
            <DialogDescription>
              Add a new promotional code to the system.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleCreatePromoCode}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="code">Code</Label>
                <Input
                  id="code"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  className="uppercase"
                  required
                  placeholder="e.g. SUMMER2025"
                  maxLength={20}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  required
                  placeholder="e.g. Summer promotion 2025"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="membershipType">Membership Type</Label>
                <select
                  id="membershipType"
                  name="membershipType"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  value={formData.membershipType}
                  onChange={handleSelectChange}
                >
                  <option value="standard">Standard</option>
                  <option value="premium">Premium</option>
                  <option value="vip">VIP</option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="discountPercent">Discount Percentage</Label>
                <Input
                  id="discountPercent"
                  name="discountPercent"
                  type="number"
                  min={0}
                  max={100}
                  value={formData.discountPercent}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="maxUsages">Maximum Usages</Label>
                <Input
                  id="maxUsages"
                  name="maxUsages"
                  type="number"
                  min={1}
                  value={formData.maxUsages}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="expiresAt">Expiration Date (Optional)</Label>
                <Input
                  id="expiresAt"
                  name="expiresAt"
                  type="datetime-local"
                  value={formData.expiresAt || ""}
                  onChange={handleDateChange}
                />
              </div>
              <div className="flex items-center gap-2">
                <Checkbox
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange(checked === true)
                  }
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createPromoCodeMutation.isPending}>
                {createPromoCodeMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Create Promo Code
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Promo Code Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Promo Code</DialogTitle>
            <DialogDescription>
              Update promotional code information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdatePromoCode}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-code">Code</Label>
                <Input
                  id="edit-code"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  className="uppercase"
                  required
                  maxLength={20}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Input
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-membershipType">Membership Type</Label>
                <select
                  id="edit-membershipType"
                  name="membershipType"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  value={formData.membershipType}
                  onChange={handleSelectChange}
                >
                  <option value="standard">Standard</option>
                  <option value="premium">Premium</option>
                  <option value="vip">VIP</option>
                </select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-discountPercent">Discount Percentage</Label>
                <Input
                  id="edit-discountPercent"
                  name="discountPercent"
                  type="number"
                  min={0}
                  max={100}
                  value={formData.discountPercent}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-maxUsages">Maximum Usages</Label>
                <Input
                  id="edit-maxUsages"
                  name="maxUsages"
                  type="number"
                  min={1}
                  value={formData.maxUsages}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-expiresAt">Expiration Date (Optional)</Label>
                <Input
                  id="edit-expiresAt"
                  name="expiresAt"
                  type="datetime-local"
                  value={formData.expiresAt || ""}
                  onChange={handleDateChange}
                />
              </div>
              <div className="flex items-center gap-2">
                <Checkbox
                  id="edit-isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange(checked === true)
                  }
                />
                <Label htmlFor="edit-isActive">Active</Label>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updatePromoCodeMutation.isPending}>
                {updatePromoCodeMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Update Promo Code
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Promo Code Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Promo Code</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this promo code? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedPromoCode && (
              <p className="font-medium">
                Promo Code: {selectedPromoCode.code}
              </p>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeletePromoCode}
              disabled={deletePromoCodeMutation.isPending}
            >
              {deletePromoCodeMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}