import { useParams } from "wouter";
import { useProperty } from "@/lib/api";
import BookingForm from "@/components/BookingForm";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function Booking() {
  const { id } = useParams();
  const { data: property, isLoading } = useProperty(id || "");

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Skeleton className="h-8 w-64 mb-8" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (!property) {
    return <div>Property not found</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Complete Your Booking</h1>

        <div className="grid grid-cols-1 lg:grid-cols-[2fr,1fr] gap-8">
          <Card>
            <CardContent className="p-6">
              <BookingForm property={property} />
            </CardContent>
          </Card>

          <div className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4">{property.name}</h2>
                <img
                  src={property.images[0]}
                  alt={property.name}
                  className="w-full h-48 object-cover rounded-lg mb-4"
                />
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Base price</span>
                  <span className="font-semibold">
                    ${property.basePrice}/night
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
