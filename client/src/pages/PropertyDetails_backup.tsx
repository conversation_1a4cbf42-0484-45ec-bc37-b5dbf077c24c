import { useParams, useLocation } from "wouter";
import { useProperty, usePropertyAvailability, useCreateReservation } from "@/lib/api.js";
import { Button } from "@/components/ui/button.jsx";
import { Card, CardContent } from "@/components/ui/card.jsx";
import { Skeleton } from "@/components/ui/skeleton.jsx";
import { Alert, AlertDescription } from "@/components/ui/alert.jsx";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog.jsx";
import { ChevronLeft, ChevronRight, X, ChevronDown, MapIcon, Share2, Wifi, Car as Parking, Coffee, Bath, Tv, BedDouble, CalendarX } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Map from "@/components/Map.jsx";
import { DatePickerWithRange } from "@/components/ui/date-range-picker.jsx";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select.jsx";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible.jsx";
import { addDays, format } from "date-fns";
import { useState, useMemo, useEffect, useCallback } from "react";
import type { Room, RatePlan, Property, PropertyImage } from "@/types/schema.js";
import type { DateRange } from "react-day-picker";
import { Badge } from "@/components/ui/badge.jsx";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { getFullSizeImageUrl, getImageSource } from "@/utils/imageUtils.js";
import styled from "@emotion/styled";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface ExtendedRoom extends Omit<Room, 'currency'> {
  ratePlanCode: string;
  ratePlanDescription: string;
  totalAmount: number;
  originalAmount?: number;
  totalDiscount?: number;
  currency: string;
  description: string;
  maxOccupancy?: number;
  amenities?: string[];
  bedTypeCode?: string;
}

interface GroupedRoom {
  code: string;
  description: string;
  amenities?: string[];
  maxOccupancy?: number;
  bedTypeCode?: string;
  ratePlans: ExtendedRoom[];
}

const RoomGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  padding: 24px 0;

  /* This ensures all cards in a row have the same height */
  & > * {
    height: 100%;
  }
`;

const RoomCard = styled(Card)`
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease-in-out;
  height: 100%;
  min-height: 500px;
  max-height: 700px;
  
  &:hover {
    transform: translateY(-4px);
  }

  .card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 1.5rem;
  }

  .room-header {
    flex: 0 0 auto;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid hsl(var(--border));
  }

  .room-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .room-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground));
    margin-bottom: 0.75rem;
  }

  .room-amenities {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
  }

  .rates-section {
    flex: 1 1 auto;
    overflow-y: auto;
    margin: 0 -1.5rem;
    padding: 0 1.5rem;
    min-height: 200px;
    max-height: 350px;

    /* Custom scrollbar */
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: hsl(var(--muted));
      border-radius: 3px;
    }
  }

  .rate-option {
    position: relative;
    padding: 1.25rem 1rem 1rem;
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    transition: all 0.2s ease-in-out;
    margin-bottom: 0.75rem;

    .lowest-price-badge {
      position: absolute;
      top: -0.75rem;
      left: -0.5rem;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      font-weight: 600;
      letter-spacing: 0.025em;
      border-radius: 0.375rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    &:hover {
      background: hsl(var(--muted)/0.3);
    }

    &.selected {
      background: hsl(var(--primary)/0.1);
      border-color: hsl(var(--primary)/0.2);
    }
  }

  .rate-description {
    font-size: 0.875rem;
    line-height: 1.4;
    margin: 0.5rem 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .rate-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;

    .cancellation-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      height: auto;
    }
  }

  .price-display {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    min-width: 120px;
  }

  .reserve-button-container {
    flex: 0 0 auto;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid hsl(var(--border));
    background: hsl(var(--background));
  }

  .reserve-button {
    transition: all 0.2s ease-in-out;
    font-weight: 600;
    height: 3.5rem;
    
    .price {
      font-size: 1.25rem;
      font-weight: 700;
    }
    
    .details {
      font-size: 0.875rem;
      opacity: 0.9;
    }
  }

  &:hover .reserve-button {
    transform: scale(1.02);
    background-color: hsl(var(--primary)/0.9);
  }
`;

const MapButton = styled.button`
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 50;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  border-radius: 24px;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  animation: slideIn 0.3s ease-out;
  
  &:hover {
    background: hsl(var(--primary)/0.9);
    transform: translateY(-2px);
  }

  @keyframes slideIn {
    from {
      transform: translateY(100px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
`;

const FullScreenMap = styled(Dialog)`
  .dialog-content {
    width: 100vw;
    height: 100vh;
    max-width: none;
    padding: 0;
    margin: 0;
  }
`;

const AMENITY_ICONS: Record<string, React.ReactNode> = {
  'WiFi': <Wifi className="w-4 h-4" />,
  'Parking': <Parking className="w-4 h-4" />,
  'Breakfast': <Coffee className="w-4 h-4" />,
  'Private Bathroom': <Bath className="w-4 h-4" />,
  'TV': <Tv className="w-4 h-4" />,
  'King Bed': <BedDouble className="w-4 h-4" />,
  'Queen Bed': <BedDouble className="w-4 h-4" />
};

const calculateNights = (from: Date, to: Date) => {
  return Math.ceil((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24));
};

export default function PropertyDetails() {
  const { id } = useParams();
  const [_, navigate] = useLocation();
  const { data: property, isLoading: isLoadingProperty, error: propertyError } = useProperty(id || "");
  const createReservation = useCreateReservation();
  const { toast } = useToast();
  
  // Get session ID for context tracking
  const sessionId = useMemo(() => {
    const savedSessionId = localStorage.getItem('booking_session_id');
    if (savedSessionId) return savedSessionId;
    
    const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    localStorage.setItem('booking_session_id', newSessionId);
    return newSessionId;
  }, []);

  // Parse URL search params for initial dates
  const searchParams = useMemo(() => {
    if (typeof window === 'undefined') return null;
    return new URLSearchParams(window.location.search);
  }, []);

  // Date and guest selection state
  const [dates, setDates] = useState<DateRange>(() => {
    // Initialize dates from URL if available
    const checkIn = searchParams?.get('checkIn');
    const checkOut = searchParams?.get('checkOut');
    return {
      from: checkIn ? new Date(checkIn + 'T00:00:00') : undefined,
      to: checkOut ? new Date(checkOut + 'T00:00:00') : undefined
    };
  });
  const [guests, setGuests] = useState(() => searchParams?.get('guests') || "2");
  const [rooms, setRooms] = useState(() => searchParams?.get('rooms') || "1");
  const [selectedRatePlans, setSelectedRatePlans] = useState<Record<string, string>>({});
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAmenitiesOpen, setIsAmenitiesOpen] = useState(false);
  const [searchEnabled, setSearchEnabled] = useState(true);
  const [showMap, setShowMap] = useState(false);

  // Create availability search params
  const availabilityParams = useMemo(() => {
    if (!dates.from || !dates.to) return null;

    const params = new URLSearchParams({
      checkIn: dates.from.toISOString().split('T')[0],
      checkOut: dates.to.toISOString().split('T')[0],
      guests,
      rooms
    });

    return params;
  }, [dates.from, dates.to, guests, rooms, searchEnabled]);

  const { 
    data: availability,
    isLoading: isLoadingAvailability,
    error: availabilityError,
    refetch: searchAvailability
  } = usePropertyAvailability(id, availabilityParams, false);

  // Update search params display
  const [displayedParams, setDisplayedParams] = useState({
    dates: dates,
    guests,
    rooms,
  });

  // Initialize search on mount only
  useEffect(() => {
    if (dates.from && dates.to && guests && rooms) {
      searchAvailability();
      setDisplayedParams({ dates, guests, rooms });
    }
  }, []);
  
  // Record property view to context service on page load
  useEffect(() => {
    if (id && parseInt(id, 10) > 0) {
      // Record property view with context service
      recordView();
    }
  }, [id, sessionId]);

  // Record property view to context service
  const recordView = async () => {
    try {
      // Record property view with context service
      await fetch('/api/context/property-view', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyId: id,
          sessionId,
          timestamp: new Date().toISOString(),
          referrer: document.referrer || 'direct',
          searchParams: {
            checkIn: dates.from?.toISOString().split('T')[0],
            checkOut: dates.to?.toISOString().split('T')[0],
            guests,
            rooms
          }
        }),
      });
    } catch (error) {
      console.error('Failed to record property view:', error);
    }
  };

  // Handle availability data changes
  useEffect(() => {
    if (availability?.ratePlans && Object.keys(availability.ratePlans).length > 0) {
      setSearchEnabled(true);
      setDisplayedParams({ dates, guests, rooms });
    }
  }, [availability]);


  // Process available rate plans and rooms
  const { ratePlans, availableRooms, groupedRooms, lowestPriceRatePlan } = useMemo(() => {
    if (!availability?.ratePlans) {
      console.log('No rate plans available:', availability);
      return { 
        ratePlans: [] as RatePlan[], 
        availableRooms: [] as ExtendedRoom[], 
        groupedRooms: [] as GroupedRoom[],
        lowestPriceRatePlan: null as ExtendedRoom | null
      };
    }

    console.log('Processing rate plans:', availability.ratePlans);
    
    const plans: RatePlan[] = [];
    const rooms: ExtendedRoom[] = [];
    const groupedMap: Record<string, GroupedRoom> = {};
    let overallLowestPrice: ExtendedRoom | null = null;

    // Helper function to normalize room description for grouping
    const normalizeDescription = (desc: string) => {
      return desc.toLowerCase()
        .replace(/\s+/g, ' ')
        .trim()
        .replace(/\b(room|studio|suite)\b/g, '')
        .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '')
        .trim();
    };

    // First pass: Collect unique rooms by normalized description
    Object.entries(availability.ratePlans).forEach(([planCode, plan]) => {
      if (plan.rooms) {
        Object.entries(plan.rooms).forEach(([roomCode, room]) => {
          if (room.totalAmount !== undefined) {
            const description = room.description || 'Standard Room';
            const normalizedDesc = normalizeDescription(description);
            
            if (!groupedMap[normalizedDesc]) {
              groupedMap[normalizedDesc] = {
                code: roomCode,
                description: description,
                amenities: room.amenities,
                maxOccupancy: room.maxOccupancy,
                bedTypeCode: room.bedTypeCode,
                ratePlans: []
              };
            }
          }
        });
      }
    });

    // Second pass: Add rate plans to rooms
    Object.entries(availability.ratePlans).forEach(([planCode, plan]) => {
      plans.push({ ...plan, code: planCode });
      
      if (plan.rooms) {
        Object.entries(plan.rooms).forEach(([roomCode, room]) => {
          if (room.totalAmount !== undefined) {
            const description = room.description || 'Standard Room';
            const normalizedDesc = normalizeDescription(description);
            const groupedRoom = groupedMap[normalizedDesc];
            
            if (groupedRoom) {
              const extendedRoom: ExtendedRoom = {
                ...room,
                code: roomCode,
                ratePlanCode: planCode,
                ratePlanDescription: plan.description,
                totalAmount: room.totalAmount,
                originalAmount: room.totalAmount + (room.totalDiscount || 0),
                totalDiscount: room.totalDiscount,
                retailDiscountPercent: room.retailDiscountPercent,
                currency: room.currency || 'USD',
                description: description,
                maxOccupancy: room.maxOccupancy || groupedRoom.maxOccupancy,
                amenities: room.amenities || groupedRoom.amenities,
                bedTypeCode: room.bedTypeCode || groupedRoom.bedTypeCode
              };

              // Check if this rate plan is already added (avoid duplicates)
              const isDuplicate = groupedRoom.ratePlans.some(
                rp => rp.ratePlanCode === planCode && rp.totalAmount === room.totalAmount
              );

              if (!isDuplicate) {
                // Track overall lowest price
                if (!overallLowestPrice || extendedRoom.totalAmount < overallLowestPrice.totalAmount) {
                  overallLowestPrice = extendedRoom;
                }

                groupedRoom.ratePlans.push(extendedRoom);
                rooms.push(extendedRoom);
                console.log(`Added rate plan ${planCode} to room description: ${description}`, extendedRoom);
              }
            }
          }
        });
      }
    });

    // Sort rate plans within each room by price
    Object.values(groupedMap).forEach(group => {
      group.ratePlans.sort((a, b) => a.totalAmount - b.totalAmount);
      console.log(`Sorted rate plans for room ${group.description}:`, group.ratePlans);
    });

    // Convert grouped rooms to array and sort by lowest rate
    const sortedGroupedRooms = Object.values(groupedMap)
      .map(group => ({
        ...group,
        lowestRate: Math.min(...group.ratePlans.map(rp => rp.totalAmount))
      }))
      .sort((a, b) => a.lowestRate - b.lowestRate)
      .map(({ lowestRate, ...group }) => group);

    console.log('Final grouped rooms:', sortedGroupedRooms);
    console.log('Overall lowest price rate plan:', overallLowestPrice);

    return {
      ratePlans: plans,
      availableRooms: rooms,
      groupedRooms: sortedGroupedRooms,
      lowestPriceRatePlan: overallLowestPrice
    } as { 
      ratePlans: RatePlan[]; 
      availableRooms: ExtendedRoom[]; 
      groupedRooms: GroupedRoom[];
      lowestPriceRatePlan: ExtendedRoom | null;
    };
  }, [availability]);

  // Reset search when params change
  useEffect(() => {
    setSearchEnabled(false);
  }, [dates.from, dates.to, guests, rooms]);

  // Get selected room details
  const selectedRoomDetails = useMemo(() => {
    if (!selectedRoom || !selectedRatePlans[selectedRoom]) return null;
    return availableRooms.find(
      room => room.code === selectedRoom && room.ratePlanCode === selectedRatePlans[selectedRoom]
    );
  }, [selectedRoom, selectedRatePlans, availableRooms]);

  // Track booking attempt with context service
  const recordBookingAttempt = useCallback(async (propertyId: number, completed: boolean = false) => {
    try {
      await fetch('/api/context/booking-attempt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, propertyId, completed })
      });
    } catch (error) {
      console.error('Failed to record booking attempt:', error);
    }
  }, [sessionId]);

  // Handle navigation to reservation form
  const handleReserveNow = (room: ExtendedRoom) => {
    if (!dates.from || !dates.to) return;

    // Record booking attempt to context service
    if (id) {
      recordBookingAttempt(parseInt(id, 10), false);
    }

    const searchParams = new URLSearchParams({
      propertyId: id || '',
      roomCode: room.code || '',
      ratePlanCode: room.ratePlanCode || '',
      checkIn: dates.from.toISOString().split('T')[0],
      checkOut: dates.to.toISOString().split('T')[0],
      guests: guests || '1',
      rooms: rooms || '1',
      rate: room.totalAmount.toString(),
      currency: room.currency,
      totalAmount: room.totalAmount.toString(),
      totalCurrency: room.currency
    });

    navigate(`/reservation?${searchParams.toString()}`);
  };

  // Helper function to get image URL
  const getImageUrl = (image: string | PropertyImage | undefined): string => {
    const url = getImageSource(image);
    return url ? getFullSizeImageUrl(url) : "";
  };

  // Handle date changes
  const handleDateChange = (newDates: DateRange) => {
    setDates(newDates);
    // Reset search when dates change
    setSearchEnabled(false);
  };

  // Helper function to ensure currency is a string
  const getCurrency = (currency: any): string => {
    return typeof currency === 'string' ? currency : 'USD';
  };

  const handleOpenMap = () => setShowMap(true);
  const handleCloseMap = () => setShowMap(false);

  const handleShare = async () => {
    if (!property?.name) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: property.name,
          text: `Check out ${property.name} on our platform!`,
          url: window.location.href
        });
      } catch (error: unknown) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('Error sharing:', error);
        }
      }
    } else if (navigator.clipboard) {
      await navigator.clipboard.writeText(window.location.href);
      toast({
        title: "Link copied to clipboard",
        description: "You can now share this property with others"
      });
    }
  };

  if (isLoadingProperty) {
    return <Skeleton className="w-full h-screen" />;
  }

  if (propertyError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load property details: {propertyError instanceof Error ? propertyError.message : "Unknown error"}
        </AlertDescription>
      </Alert>
    );
  }

  if (!property) {
    return <div>Property not found</div>;
  }

  console.log('Property data:', {
    hasImages: !!property.images,
    imagesLength: property.images?.length,
    imagesType: property.images ? typeof property.images : 'undefined',
    isArray: Array.isArray(property.images),
    firstImage: property.images?.[0],
  });

  console.log('Rendering with grouped rooms:', groupedRooms);

  return (
    <div className="container mx-auto px-4 py-8">
      {availabilityError && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>
            Failed to load availability: {availabilityError instanceof Error ? availabilityError.message : "Unknown error"}
          </AlertDescription>
        </Alert>
      )}

      {property.images && Array.isArray(property.images) && property.images.length > 0 && (() => {
        const validImages = property.images.filter(img => {
          const url = getImageUrl(img);
          return url && url !== '/placeholder-property.jpg';
        });

        if (validImages.length === 0) return null;

        return (
          <div className="mb-8">
            <div className="grid grid-cols-4 gap-2 h-[500px]">
              {/* Main Image */}
              <div 
                className="col-span-2 row-span-2 relative cursor-pointer"
                onClick={() => {
                  setCurrentImageIndex(0);
                  setShowImageGallery(true);
                }}
              >
                <img
                  src={getImageUrl(validImages[0])}
                  alt={`${property.name} - Main`}
                  className="w-full h-full object-cover rounded-l-lg"
                />
              </div>

              {/* Thumbnail Grid */}
              {validImages.slice(1, 5).map((image, index) => (
                <div 
                  key={index}
                  className="relative h-[248px] cursor-pointer"
                  onClick={() => {
                    setCurrentImageIndex(index + 1);
                    setShowImageGallery(true);
                  }}
                >
                  <img
                    src={getImageUrl(image)}
                    alt={`${property.name} - ${index + 2}`}
                    className={`w-full h-full object-cover ${
                      index === 1 ? 'rounded-tr-lg' : ''
                    } ${index === 3 ? 'rounded-br-lg' : ''}`}
                  />
                  {/* View All Button - show on last visible image */}
                  {validImages.length > 5 && index === 3 && (
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowImageGallery(true);
                        }}
                        className="bg-white/90 hover:bg-white text-black px-4 py-2 rounded-full shadow-lg transition-colors"
                      >
                        View all {validImages.length} photos
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );
      })()}

      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold">{property.name}</h1>
            <p className="text-gray-600 mt-2">{property.description}</p>
          </div>
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleShare}
          >
            <Share2 className="h-4 w-4" />
            <span className="sr-only">Share property</span>
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          <div className="md:col-span-2">
            <DatePickerWithRange
              date={dates}
              onChange={handleDateChange}
            />
          </div>
          <Select value={guests} onValueChange={setGuests}>
            <SelectTrigger>
              <SelectValue placeholder="Guests" />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3, 4].map(num => (
                <SelectItem key={num} value={num.toString()}>
                  {num} {num === 1 ? 'Guest' : 'Guests'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={rooms} onValueChange={setRooms}>
            <SelectTrigger>
              <SelectValue placeholder="Rooms" />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3].map(num => (
                <SelectItem key={num} value={num.toString()}>
                  {num} {num === 1 ? 'Room' : 'Rooms'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <div className="md:col-span-4">
            <Button 
              className="w-full"
              onClick={() => {
                setSearchEnabled(true);
                searchAvailability();
              }}
              disabled={!dates.from || !dates.to}
            >
              {dates.from && dates.to ? 'Update Rates' : 'Select Dates'}
            </Button>
          </div>
        </div>
      </div>

      {property.amenities && property.amenities.length > 0 && (
        <Card className="mb-8">
          <Collapsible
            open={isAmenitiesOpen}
            onOpenChange={setIsAmenitiesOpen}
            className="p-6"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Property Amenities</h2>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="w-9 p-0">
                  <ChevronDown className={`h-4 w-4 transition-transform ${isAmenitiesOpen ? 'transform rotate-180' : ''}`} />
                  <span className="sr-only">Toggle amenities</span>
                </Button>
              </CollapsibleTrigger>
            </div>

            <CollapsibleContent className="mt-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {property.amenities.map((amenity: string) => (
                  <div key={amenity} className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-primary rounded-full" />
                    <span>{amenity}</span>
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      )}

      <div className="relative">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold">Available Rooms</h2>
            {availableRooms.length > 0 && (
              <Badge variant="secondary">
                {availableRooms.length} {availableRooms.length === 1 ? 'room' : 'rooms'} available
              </Badge>
            )}
          </div>
          {displayedParams.dates.from && displayedParams.dates.to && (
            <div className="text-sm text-muted-foreground">
              {format(displayedParams.dates.from, "MMM d")} - {format(displayedParams.dates.to, "MMM d")} 
              <span className="mx-2">•</span>
              {displayedParams.guests} {parseInt(displayedParams.guests) === 1 ? 'guest' : 'guests'}
              <span className="mx-2">•</span>
              {displayedParams.rooms} {parseInt(displayedParams.rooms) === 1 ? 'room' : 'rooms'}
            </div>
          )}
        </div>

        {isLoadingAvailability ? (
          <div className="flex items-center justify-center p-8 bg-muted/10 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
              <span className="text-muted-foreground">Updating rates...</span>
            </div>
          </div>
        ) : !searchEnabled ? (
          <Card>
            <CardContent className="p-6">
              <p>Select dates and click "Update Rates" to see available rooms</p>
            </CardContent>
          </Card>
        ) : availableRooms.length > 0 ? (
          <RoomGrid>
            {groupedRooms.map((room: GroupedRoom) => {
              console.log(`Rendering room ${room.code} with ${room.ratePlans.length} rate plans:`, room);
              
              if (!room || room.ratePlans.length === 0) {
                console.log(`Skipping room ${room?.code} - invalid data`);
                return null;
              }

              const selectedRatePlan = room.ratePlans.find(
                rp => rp.ratePlanCode === selectedRatePlans[room.code]
              ) || room.ratePlans[0];

              return (
                <RoomCard key={room.code} className="group">
                  <CardContent className="card-content">
                    {/* Room Header */}
                    <div className="room-header">
                      <div className="flex justify-between">
                        <div className="flex-1 mr-4">
                          <h3 className="room-title">{room.description}</h3>
                          <div className="room-meta">
                            {typeof room.maxOccupancy === 'number' && (
                              <span className="flex items-center gap-1">
                                <BedDouble className="w-4 h-4" />
                                Sleeps {room.maxOccupancy}
                              </span>
                            )}
                            {room.bedTypeCode && (
                              <>
                                <span>•</span>
                                <span>{room.bedTypeCode}</span>
                              </>
                            )}
                          </div>
                        </div>
                        {room.ratePlans.length > 0 && (
                          <div className="text-right shrink-0">
                            <div className="text-sm text-muted-foreground">From</div>
                            <div className="text-xl font-bold whitespace-nowrap">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: room.ratePlans[0].currency
                              }).format(room.ratePlans[0].totalAmount)}
                            </div>
                            <div className="text-sm text-muted-foreground">per night</div>
                          </div>
                        )}
                      </div>

                      {room.amenities && room.amenities.length > 0 && (
                        <div className="room-amenities">
                          {room.amenities.slice(0, 3).map((amenity: string, index: number) => (
                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                              <span className="text-base leading-none">{
                                amenity.toLowerCase().includes('wifi') ? '📶' :
                                amenity.toLowerCase().includes('parking') ? '🅿️' :
                                amenity.toLowerCase().includes('breakfast') ? '☕️' :
                                amenity.toLowerCase().includes('bathroom') ? '🚿' :
                                amenity.toLowerCase().includes('tv') ? '📺' :
                                amenity.toLowerCase().includes('bed') ? '🛏️' :
                                '✨'
                              }</span>
                              <span>{amenity}</span>
                            </Badge>
                          ))}
                          {room.amenities.length > 3 && (
                            <Badge variant="secondary">+{room.amenities.length - 3} more</Badge>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Rate Plans */}
                    <div className="rates-section">
                      <RadioGroup
                        value={selectedRatePlans[room.code] || room.ratePlans[0]?.ratePlanCode}
                        onValueChange={(value) => {
                          setSelectedRatePlans(prev => ({ ...prev, [room.code]: value }));
                        }}
                        className="space-y-3"
                      >
                        {room.ratePlans.map((ratePlan: ExtendedRoom) => {
                          const discountPercent = ratePlan.retailDiscountPercent ?? 0;
                          const hasDiscount = (ratePlan.totalDiscount || 0) > 0 && ratePlan.originalAmount;
                          const isLowestPrice = lowestPriceRatePlan?.ratePlanCode === ratePlan.ratePlanCode;
                          const isSelected = selectedRatePlans[room.code] === ratePlan.ratePlanCode;
                          
                          return (
                            <div 
                              key={ratePlan.ratePlanCode}
                              className={`rate-option ${isSelected ? 'selected' : ''}`}
                            >
                              {isLowestPrice && (
                                <Badge className="lowest-price-badge bg-green-600">
                                  LOWEST PRICE
                                </Badge>
                              )}
                              <div className="flex items-start gap-4">
                                <RadioGroupItem 
                                  value={ratePlan.ratePlanCode} 
                                  id={`${room.code}-${ratePlan.ratePlanCode}`}
                                  className="mt-1"
                                />
                                <label 
                                  htmlFor={`${room.code}-${ratePlan.ratePlanCode}`}
                                  className="flex-1 cursor-pointer"
                                >
                                  <div className="flex justify-between gap-4">
                                    <div className="flex-1 min-w-0">
                                      <p className="rate-description">{ratePlan.ratePlanDescription}</p>
                                      <div className="rate-badges">
                                        <Badge variant="outline" className="cancellation-badge">
                                          {ratePlan.ratePlanCode.includes('NRF') ? 'Non-refundable' : 'Free cancellation'}
                                        </Badge>
                                        {hasDiscount && discountPercent > 0 && (
                                          <Badge variant="destructive">Save {discountPercent}%</Badge>
                                        )}
                                      </div>
                                    </div>
                                    <div className="price-display">
                                      {hasDiscount && ratePlan.originalAmount && (
                                        <span className="text-sm line-through text-muted-foreground whitespace-nowrap">
                                          {new Intl.NumberFormat('en-US', {
                                            style: 'currency',
                                            currency: ratePlan.currency
                                          }).format(ratePlan.originalAmount)}
                                        </span>
                                      )}
                                      <div className="text-2xl font-bold whitespace-nowrap">
                                        {new Intl.NumberFormat('en-US', {
                                          style: 'currency',
                                          currency: ratePlan.currency
                                        }).format(ratePlan.totalAmount)}
                                      </div>
                                      <div className="text-sm text-muted-foreground whitespace-nowrap">per night</div>
                                    </div>
                                  </div>
                                </label>
                              </div>
                            </div>
                          );
                        })}
                      </RadioGroup>
                    </div>

                    {/* Reserve Button */}
                    <div className="reserve-button-container">
                      <Button 
                        onClick={() => handleReserveNow(selectedRatePlan)}
                        className="w-full reserve-button"
                        size="lg"
                      >
                        <div className="flex flex-col items-center justify-center">
                          <span className="price">
                            Reserve Now for {new Intl.NumberFormat('en-US', {
                              style: 'currency',
                              currency: selectedRatePlan.currency
                            }).format(selectedRatePlan.totalAmount)}
                          </span>
                          {dates.from && dates.to && (
                            <span className="details">
                              {calculateNights(dates.from, dates.to)} night{calculateNights(dates.from, dates.to) !== 1 ? 's' : ''} • {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: selectedRatePlan.currency
                              }).format(selectedRatePlan.totalAmount * calculateNights(dates.from, dates.to))} total
                            </span>
                          )}
                        </div>
                      </Button>
                    </div>
                  </CardContent>
                </RoomCard>
              );
            })}
          </RoomGrid>
        ) : (
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center p-4">
                <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center mb-4">
                  <span className="text-2xl">🔍</span>
                </div>
                <h3 className="text-lg font-semibold mb-2">No Rooms Available</h3>
                <p className="text-muted-foreground mb-4">
                  We couldn't find any rooms for your selected dates. Try adjusting your dates or reducing the number of guests.
                </p>
                <div className="flex gap-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setDates({ from: undefined, to: undefined })}
                  >
                    Clear Dates
                  </Button>
                  <Button onClick={() => searchAvailability()}>
                    Try Again
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <MapButton onClick={handleOpenMap}>
        <MapIcon className="w-4 h-4" />
        <span>View on Map</span>
      </MapButton>

      <FullScreenMap open={showMap} onOpenChange={handleCloseMap}>
        <DialogContent className="dialog-content">
          <div className="absolute top-4 right-4 z-50">
            <Button variant="outline" size="icon" onClick={handleCloseMap}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="h-full relative">
            <Map
              properties={[property].filter(Boolean)}
              center={{ lat: Number(property.latitude), lng: Number(property.longitude) }}
              zoom={15}
              onPropertySelect={(id) => {
                handleCloseMap();
                navigate(`/property/${id}`);
              }}
            />
            <div className="absolute inset-0 bg-background/80 flex items-center justify-center pointer-events-none animate-out fade-out duration-300">
              <div className="flex items-center gap-2">
                <div className="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
                <span>Loading map...</span>
              </div>
            </div>
          </div>
        </DialogContent>
      </FullScreenMap>

      <Dialog open={showImageGallery} onOpenChange={setShowImageGallery}>
        <DialogContent className="max-w-7xl h-[90vh] p-0">
          <VisuallyHidden>
            <DialogTitle>Property Image Gallery</DialogTitle>
          </VisuallyHidden>
          <DialogHeader className="sr-only">
            <DialogTitle>Property Images</DialogTitle>
            <DialogDescription>
              Image gallery for {property.name}
            </DialogDescription>
          </DialogHeader>

          {(() => {
            const validImages = property.images?.filter(img => {
              const url = getImageUrl(img);
              return url && url !== '/placeholder-property.jpg';
            }) || [];

            if (validImages.length === 0) return null;

            return (
              <div className="relative h-full">
                <button
                  onClick={() => setShowImageGallery(false)}
                  className="absolute top-4 right-4 z-50 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                >
                  <X className="h-6 w-6 text-white" />
                </button>

                {/* Navigation Arrows */}
                <button
                  onClick={() => setCurrentImageIndex(prev => 
                    prev === 0 ? validImages.length - 1 : prev - 1
                  )}
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-50 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                >
                  <ChevronLeft className="h-8 w-8 text-white" />
                </button>

                <button
                  onClick={() => setCurrentImageIndex(prev => 
                    prev === validImages.length - 1 ? 0 : prev + 1
                  )}
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-50 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                >
                  <ChevronRight className="h-8 w-8 text-white" />
                </button>

                {/* Current Image */}
                <div className="h-full flex items-center justify-center bg-black/90">
                  <img
                    src={getImageUrl(validImages[currentImageIndex])}
                    alt={`${property.name} - ${currentImageIndex + 1}`}
                    className="max-h-full max-w-full object-contain"
                  />
                </div>

                {/* Image Counter */}
                <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                  <span className="bg-black/50 text-white px-4 py-2 rounded-full">
                    {currentImageIndex + 1} / {validImages.length}
                  </span>
                </div>
              </div>
            );
          })()}
        </DialogContent>
      </Dialog>
    </div>
  );
}