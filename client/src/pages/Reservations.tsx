import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { format } from "date-fns";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import { ChevronRight, BookOpenText, Building, Calendar, CreditCard } from "lucide-react";

interface Reservation {
  id: number;
  userId: number;
  propertyId: number;
  checkIn: string;
  checkOut: string;
  totalPrice: string;
  status: string;
  propertyName?: string;
  createdAt: string;
}

export default function Reservations() {
  const { user } = useAuth();
  const { toast } = useToast();

  // Fetch the user's reservations
  const { data: reservations, isLoading, refetch } = useQuery<Reservation[]>({
    queryKey: ["/api/reservations"],
    queryFn: async () => {
      try {
        const res = await apiRequest("GET", "/api/reservations");
        const data = await res.json();
        return data.reservations || [];
      } catch (error) {
        console.error("Failed to fetch reservations:", error);
        return [];
      }
    },
    enabled: !!user, // Only fetch if user is logged in
  });

  // Function to cancel a reservation
  const cancelReservation = async (reservationId: number) => {
    try {
      const res = await apiRequest("POST", `/api/reservations/${reservationId}/cancel`);
      const data = await res.json();
      
      if (data.success) {
        toast({
          title: "Success",
          description: "Your reservation has been cancelled.",
        });
        // Refetch reservations to update the list
        refetch();
      } else {
        throw new Error(data.message || "Failed to cancel reservation");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel reservation",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Skeleton className="h-8 w-64 mb-8" />
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className="h-48 w-full mb-4" />
        ))}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">My Bookings</h1>
          <p className="text-muted-foreground mt-2">
            Manage your current and upcoming reservations
          </p>
        </div>

        <div className="space-y-6">
          {reservations && reservations.length > 0 ? (
            reservations.map((reservation) => (
              <Card key={reservation.id} className="overflow-hidden">
                <div className="bg-primary h-2 w-full" />
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center">
                        <BookOpenText className="mr-2 h-5 w-5 text-primary" />
                        {reservation.propertyName || `Booking #${reservation.id}`}
                      </CardTitle>
                      <CardDescription>
                        Status: <span className="font-medium">{reservation.status.toUpperCase()}</span>
                      </CardDescription>
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/property/${reservation.propertyId}`}>
                        View Property <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-start">
                      <Calendar className="h-5 w-5 text-muted-foreground mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium">Check-in</p>
                        <p className="text-muted-foreground">
                          {format(new Date(reservation.checkIn), "MMM d, yyyy")}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Calendar className="h-5 w-5 text-muted-foreground mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium">Check-out</p>
                        <p className="text-muted-foreground">
                          {format(new Date(reservation.checkOut), "MMM d, yyyy")}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Building className="h-5 w-5 text-muted-foreground mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium">Property ID</p>
                        <p className="text-muted-foreground">{reservation.propertyId}</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <CreditCard className="h-5 w-5 text-muted-foreground mr-2 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium">Total Price</p>
                        <p className="text-muted-foreground">
                          ${parseInt(reservation.totalPrice).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Only show cancel button for reservations with status "confirmed" or "pending" */}
                  {(reservation.status === "confirmed" || reservation.status === "pending") && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" className="mt-2">
                          Cancel Reservation
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Cancel this reservation?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. The cancellation policy may
                            apply and cancellation fees might be charged.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Keep Reservation</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => cancelReservation(reservation.id)}
                          >
                            Cancel Reservation
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="mx-auto mb-4 bg-muted rounded-full w-12 h-12 flex items-center justify-center">
                  <BookOpenText className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">No Bookings Found</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any reservations yet. Start by searching for properties to book.
                </p>
                <Button asChild>
                  <Link href="/">
                    Find Properties
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}