/**
 * Enhanced AI Chat Demo Page
 * 
 * This page demonstrates the improved AI travel planner with:
 * - Proactive Intelligence
 * - Enhanced Location Understanding
 * - Improved Technical Architecture
 * - Comprehensive Testing
 */
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import EnhancedAIChat from "@/components/EnhancedAIChat";
import { Badge } from "@/components/ui/badge";

export default function EnhancedAIChatDemo() {
  const [showChat, setShowChat] = useState(false);
  
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Enhanced AI Travel Planner</h1>
      
      <div className="grid md:grid-cols-2 gap-8 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              Enhanced AI Chat
              <Badge className="ml-2 bg-green-500">New</Badge>
            </CardTitle>
            <CardDescription>
              Our AI travel planner now has improved intelligence and understanding
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p>
                Ask our enhanced AI assistant about travel destinations, accommodation options,
                and get intelligent insights about your trip planning.
              </p>
              <Button 
                onClick={() => {
                  // Clear any existing chat history to start fresh
                  localStorage.removeItem('enhancedChatHistory');
                  localStorage.removeItem('enhancedConversationState');
                  setShowChat(true);
                }}
              >
                Start Enhanced Chat
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Key Improvements</CardTitle>
            <CardDescription>
              Four major areas of enhancement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 list-disc pl-5">
              <li><strong>Proactive Intelligence:</strong> Suggests alternatives, alerts about events</li>
              <li><strong>Enhanced Location Understanding:</strong> Neighborhood-level recommendations</li>
              <li><strong>Improved Technical Architecture:</strong> Better state management</li>
              <li><strong>Testing & Quality Assurance:</strong> Comprehensive conversation testing</li>
            </ul>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="features" className="mb-8">
        <TabsList className="mb-4">
          <TabsTrigger value="features">New Features</TabsTrigger>
          <TabsTrigger value="examples">Example Prompts</TabsTrigger>
          <TabsTrigger value="technical">Technical Details</TabsTrigger>
        </TabsList>
        
        <TabsContent value="features">
          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Proactive Intelligence</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Suggests alternative dates when rates are lower</li>
                  <li>• Alerts about special events during travel dates</li>
                  <li>• Recommends complementary activities</li>
                  <li>• Provides weather information and travel advisories</li>
                </ul>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Enhanced Location Understanding</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Neighborhood-level recommendations</li>
                  <li>• Safety, walkability, and convenience information</li>
                  <li>• Better proximity understanding ("near the beach")</li>
                  <li>• Landmark and point-of-interest awareness</li>
                </ul>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Technical Improvements</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Server-side conversation state management</li>
                  <li>• Improved error handling and recovery</li>
                  <li>• Better streaming response handling</li>
                  <li>• Comprehensive testing framework</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="examples">
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Try These Prompts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 bg-slate-50 rounded-md">
                    <strong>Location Intelligence:</strong>
                    <p className="text-slate-600">I want to stay in Miami Beach near the Art Deco district</p>
                  </div>
                  
                  <div className="p-3 bg-slate-50 rounded-md">
                    <strong>Seasonal Planning:</strong>
                    <p className="text-slate-600">What's the best time to visit New York City?</p>
                  </div>
                  
                  <div className="p-3 bg-slate-50 rounded-md">
                    <strong>Alternative Suggestions:</strong>
                    <p className="text-slate-600">I'm looking for a beachfront hotel in Miami, but I'm on a budget</p>
                  </div>
                  
                  <div className="p-3 bg-slate-50 rounded-md">
                    <strong>Neighborhood Questions:</strong>
                    <p className="text-slate-600">Which areas in Chicago are best for families?</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="technical">
          <Card>
            <CardHeader>
              <CardTitle>Technical Implementation</CardTitle>
              <CardDescription>
                Behind-the-scenes improvements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>
                  The enhanced AI travel planner has been rebuilt with a more sophisticated architecture:
                </p>
                
                <ul className="space-y-2 list-disc pl-5">
                  <li><strong>Server-side State Management:</strong> Conversations persist even if the client disconnects</li>
                  <li><strong>Enhanced Data Models:</strong> More detailed location and property understanding</li>
                  <li><strong>Comprehensive Testing:</strong> Automated tests for conversation paths and location detection</li>
                  <li><strong>Improved Error Handling:</strong> Graceful recovery from failures with fallback mechanisms</li>
                  <li><strong>Streaming Optimization:</strong> Faster responses with better incremental updates</li>
                </ul>
                
                <p>
                  These improvements result in a more reliable, intelligent, and helpful travel planning assistant.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {showChat && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="relative w-full max-w-4xl h-[80vh]">
            <Button 
              variant="outline" 
              className="absolute -top-12 right-0 bg-white"
              onClick={() => setShowChat(false)}
            >
              Close Chat
            </Button>
            <EnhancedAIChat variant="integrated" />
          </div>
        </div>
      )}
    </div>
  );
}