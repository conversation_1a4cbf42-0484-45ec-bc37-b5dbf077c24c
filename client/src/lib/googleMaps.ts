import { Loader } from '@googlemaps/js-api-loader';

interface LoaderOptions {
  apiKey: string;
  version: string;
  libraries: Array<"places" | "marker" | "geometry">;
  id?: string;
}

/**
 * Initialize the Google Maps JavaScript API loader
 */
async function initializeMapsLoader(): Promise<Loader> {
  // Default to a placeholder API key that will work with restricted usage
  // In production, this should be provided via environment variables
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg';
  
  const loaderOptions: LoaderOptions = {
    apiKey,
    version: 'weekly',
    libraries: ['places', 'geometry'],
    id: '__googleMapsScriptId',
  };
  
  return new Loader(loaderOptions);
}

/**
 * Get the Google Maps global object with proper loading
 */
export async function getGoogleMaps(): Promise<typeof google> {
  if (typeof window !== 'undefined' && window.google && window.google.maps) {
    return window.google;
  }
  
  try {
    const loader = await initializeMapsLoader();
    await loader.load();
    
    if (!window.google || !window.google.maps) {
      throw new Error('Google Maps API failed to load');
    }
    
    return window.google;
  } catch (error) {
    console.error('Error loading Google Maps:', error);
    throw error;
  }
}

/**
 * Get detailed information about a place using its ID
 */
export async function getPlaceDetails(placeId: string): Promise<{
  name: string;
  formatted_address: string;
  lat: number;
  lng: number;
  types: string[];
}> {
  const google = await getGoogleMaps();
  
  return new Promise((resolve, reject) => {
    const placesService = new google.maps.places.PlacesService(
      document.createElement('div')
    );
    
    placesService.getDetails(
      {
        placeId,
        fields: ['name', 'formatted_address', 'geometry', 'types'],
      },
      (place, status) => {
        if (status !== google.maps.places.PlacesServiceStatus.OK || !place || !place.geometry) {
          reject(new Error(`Place details request failed: ${status}`));
          return;
        }
        
        resolve({
          name: place.name || '',
          formatted_address: place.formatted_address || '',
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng(),
          types: place.types || [],
        });
      }
    );
  });
}