import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a number as currency with proper localization
 * @param amount The amount to format
 * @param currency The currency code (defaults to USD)
 * @param options Additional Intl.NumberFormat options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number, 
  currency: string = 'USD', 
  options: Partial<Intl.NumberFormatOptions> = {}
): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toString(),
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    ...options
  }).format(amount);
}

/**
 * Calculate the number of nights between two dates
 * @param checkIn Check-in date
 * @param checkOut Check-out date
 * @returns Number of nights, or 0 if dates are invalid
 */
export function calculateNights(checkIn?: Date | null, checkOut?: Date | null): number {
  if (!checkIn || !checkOut) return 0;
  return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
}
