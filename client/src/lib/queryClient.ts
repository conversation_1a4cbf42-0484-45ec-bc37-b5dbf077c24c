import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Utility function to get a query fetcher with standardized error handling
export const getQueryFn = ({ on401 = 'throw' }: { on401?: 'throw' | 'returnNull' } = {}) => {
  return async ({ queryKey }: { queryKey: string[] }) => {
    const [endpoint] = queryKey;
    const res = await fetch(endpoint);
    
    if (!res.ok) {
      if (res.status === 401 && on401 === 'returnNull') {
        return null;
      }
      throw new Error(`API error ${res.status}: ${await res.text()}`);
    }
    
    return res.json();
  };
};

// Utility function for API requests
export const apiRequest = async (
  method: string,
  url: string,
  data?: any,
  headers: HeadersInit = {}
) => {
  const options: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorText = await response.text();
    let errorMessage;
    
    try {
      const errorJson = JSON.parse(errorText);
      errorMessage = errorJson.message || errorJson.error || errorText;
    } catch {
      errorMessage = errorText || `HTTP error ${response.status}`;
    }
    
    throw new Error(errorMessage);
  }

  return response;
};