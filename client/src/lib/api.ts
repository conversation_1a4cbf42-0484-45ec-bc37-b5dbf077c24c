import { useMutation, useQuery } from "@tanstack/react-query";
import { queryClient } from "./queryClient.js";
import type { Property, AvailabilityResponse, Reservation } from "@/types/schema.js";
import type { SearchResponse } from '../types/schema';

interface Rate {
  code: string;
  description: string;
  rate: number;
  currency: string;
}

interface RatePlan {
  code: string;
  description: string;
  commissionStatus: string;
  rooms: {
    [key: string]: {
      code: string;
      description: string;
      rate: number;
      currency: string;
      roomTypeCode: string;
      bedTypeCode: string;
      maxOccupancy: number;
      availableQuantity: number;
      totalAmount: number;
      totalCurrency: string;
    }
  };
}

export function useSearchProperties(params: URLSearchParams | null) {
  return useQuery<SearchResponse>({
    queryKey: params ? [`/api/properties/search?${params.toString()}`] : [],
    queryFn: async () => {
      const res = await fetch(`/api/properties/search?${params!.toString()}`, {
        credentials: 'include',
      });
      if (!res.ok) {
        throw new Error(await res.text());
      }
      const data = await res.json();
      return {
        properties: data.properties || [],
        total: data.total || 0,
        currentPage: data.currentPage || 1,
        totalPages: data.totalPages || 1,
        explanation: data.explanation,
        searchId: data.searchId || `search-${Date.now()}`,
        timing: {
          total: data.timing?.total || 0,
          source: data.timing?.source
        }
      };
    },
    enabled: !!params,
  });
}

export function useProperty(id: string) {
  return useQuery<Property>({
    queryKey: [`/api/properties/${id}`],
    enabled: !!id,
    queryFn: async ({ queryKey }) => {
      const res = await fetch(queryKey[0] as string, {
        credentials: 'include',
      });
      
      if (!res.ok) {
        throw new Error(await res.text());
      }
      
      return res.json();
    }
  });
}

export function usePropertyAvailability(
  id: string | undefined, 
  params: URLSearchParams | null,
  bestRateOnly: boolean = true
) {
  return useQuery<AvailabilityResponse>({
    queryKey: params ? [`/api/properties/${id}/availability?${params.toString()}${bestRateOnly ? '&bestRateOnly=true' : ''}`] : [],
    queryFn: async ({ queryKey }) => {
      const res = await fetch(queryKey[0] as string, {
        credentials: 'include',
      });
      
      if (!res.ok) {
        throw new Error(await res.text());
      }
      
      return res.json();
    },
    enabled: !!id && !!params,
  });
}

interface ReservationData {
  propertyId: number;
  checkIn: string;
  checkOut: string;
  guests: number;
  roomCode: string;
  ratePlanCode: string;
}

export function useCreateReservation() {
  return useMutation({
    mutationFn: async (data: ReservationData) => {
      const res = await fetch("/api/reservations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!res.ok) {
        throw new Error(await res.text());
      }

      return res.json() as Promise<{ id: number }>;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reservations"] });
    },
  });
}

export function useReservations() {
  return useQuery<Reservation[]>({
    queryKey: ["/api/reservations"],
  });
}

export function useCancelReservation() {
  return useMutation({
    mutationFn: async (id: number) => {
      const res = await fetch(`/api/reservations/${id}/cancel`, {
        method: "POST",
      });

      if (!res.ok) {
        throw new Error(await res.text());
      }

      return res.json() as Promise<Reservation>;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/reservations"] });
    },
  });
}