# RoomLama Authentication System

This document details the authentication system implemented in the RoomLama platform.

## Overview

The authentication system in RoomLama provides:
- User registration and login functionality
- JWT-based authentication
- Role-based access control (admin vs standard users)
- Protected routes for admin-only functionality
- Consistent authentication across all environments

## Authentication System

The application uses a PostgreSQL database for user management in all environments:

1. On startup, the application runs database migrations to ensure tables exist
2. An initial admin user is created automatically if one doesn't exist
3. Full JWT-based authentication is employed
4. Passwords are securely hashed with bcrypt

### Initial Admin User
The following admin user is created during initial setup:
- **Email:** <EMAIL>
- **Password:** password123

## Authentication Flow

1. User submits login credentials
2. Backend verifies the credentials and issues a JWT token
3. Token is stored in localStorage and included in future API requests
4. Protected routes check for valid token and appropriate role/permissions

## Protected Routes

- `/api/admin/*` - Requires admin privileges
- `/testh` - Admin test interface (requires admin privileges)
- Various reservation management endpoints

## Database Schema

Authentication is built on the following database tables:

### Users Table
```sql
CREATE TABLE public.users (
    id SERIAL PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    membership_type TEXT DEFAULT 'standard',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    used_promo_code TEXT,
    preferences JSONB, -- Stores user preferences including display name
    profile_image_url TEXT,
    phone_number TEXT
);
```

Note: The user's display name is stored in the `preferences` JSONB field since we don't have a separate name column.

### Promo Codes Table
```sql
CREATE TABLE public.promo_codes (
    id SERIAL PRIMARY KEY,
    code TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    membership_type TEXT DEFAULT 'premium',
    discount_percent INTEGER NOT NULL,
    max_usages INTEGER DEFAULT 1,
    usage_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES public.users(id)
);
```

## Implementation Notes

- Authentication is handled by middleware in `server/middleware/auth.ts`
- Login/registration endpoints are in `server/controllers/authController.ts`
- Password hashing and JWT functions are in `server/services/authService.ts`
- Migration files in `migrations/` maintain database schema
- Client auth hooks are in `client/src/hooks/use-auth.tsx`
- Protected route component is in `client/src/components/ProtectedRoute.tsx`