{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Node.js", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/server/index.ts", "runtimeExecutable": "node", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "tsx"], "env": {"NODE_ENV": "development"}, "sourceMaps": true, "protocol": "inspector", "port": 9229, "address": "0.0.0.0", "localRoot": "${workspaceFolder}", "remoteRoot": "/home/<USER>/${REPL_SLUG}", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug TravSrv Tests", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--watchAll=false", "server/services/__tests__/travsrv.test.ts"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_OPTIONS": "--experimental-vm-modules"}}]}